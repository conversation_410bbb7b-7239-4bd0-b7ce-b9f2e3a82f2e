var express = require("express");
var router = express.Router();

var supabaseInstance = require("../../services/supabaseClient").supabase;

var sendAdminNotifications = require("./../firebase").sendAdminNotifications;
const logger = require("../../services/logger");

/* GET users listing. */
router.get("/", function (req, res, next) {
  res.send({ success: true, message: "respond send from admin.js" });
});

router.get("/getAdminList", async (req, res) => {
  const { page, perPage, status, sortBy, searchText } = req.query; // Extract query parameters
  const pageNumber = parseInt(page) || 1;
  const itemsPerPage = parseInt(perPage) || 10;

  try {
    let query = supabaseInstance.from("Super_Admin_Users").select("*", { count: "exact" }).range((pageNumber - 1) * itemsPerPage, pageNumber * itemsPerPage - 1);

    if (searchText) {
      query = query.ilike('name', `%${searchText}%`)
    }

    if (status === "true") {
      query = query.eq("isActive", true);
    } else if (status === "false") {
      query = query.eq("isActive", false);
    }

    if (sortBy === "name") {
      query = query.order("name", { ascending: true });
    } else if (sortBy === "date") {
      query = query.order("created_at", { ascending: false });
    } else {
      query = query.order("updated_at", { ascending: false });
    }


    const { data, error, count } = await query;

    if (data) {
      const totalPages = Math.ceil(count / itemsPerPage);
      res.status(200).json({
        success: true,
        data,
        meta: {
          page: pageNumber,
          perPage: itemsPerPage,
          totalPages,
          totalCount: count,
        },
      });
    } else {
      throw error;
    }
  } catch (error) {
    logger.error("Error in admin/getAdminList", error);
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/createAdmin", async function (req, res, next) {
  const { email, password, name, mobile, role, tabAccess } = req.body;

  try {
    const authResponse = await supabaseInstance.auth.signUp({ email, password });
    if (authResponse?.data?.user) {
      const adminId = authResponse.data.user.id;
      const insertResponse = await supabaseInstance
        .from("Super_Admin_Users")
        .insert({ adminId, name, email, mobile, role, tabAccess })
        .select("*")
        .maybeSingle();
      if (insertResponse.data) {
        res.send({ success: true, data: insertResponse.data });
      } else {
        throw insertResponse.error;
      }
      // todo Super_Admin_Users insert user object =>adminId <= uid
    } else {
      throw authResponse.error;
    }
  } catch (error) {
    logger.error("Error in admin/createAdmin", error);
    if (error.code === "23505") {
      res.send({ success: false, message: "Email already exist" });
    }
    res.send({ success: false, message: error?.message || error });
  }
});

router.post("/adminLogin", async (req, res) => {
  const { email, password } = req.body;
  try {
    const authResponse = await supabaseInstance.auth.signInWithPassword({ email, password });
    if (authResponse.data?.user) {
      const adminId = authResponse.data.user.id;
      const { data, error } = await supabaseInstance
        .from("Super_Admin_Users")
        .select("*")
        .eq("adminId", adminId)
        .maybeSingle();

      res.send({
        success: true,
        message: "Login successful",
        data: data,
      });
    } else {
      throw authResponse.error;
    }
  } catch (error) {
    logger.error("Error in admin/adminLogin", error);
    res.status(500).json({ success: false, message: error.message || error });
  }
});

router.post("/resetPassword", async (req, res) => {
  const { email } = req.body;
  try {
    const resetPasswordForEmailResponse = await supabaseInstance.auth.resetPasswordForEmail(email, {
      redirectTo: 'https://mealpe-super-admin-testing.web.app',
    })
    if (resetPasswordForEmailResponse?.data) {
      res.send({
        success: true,
        message: "Link Shared",
      });
    } else {
      throw resetPasswordForEmailResponse.error;
    }
  } catch (error) {
    logger.error("Error in admin/resetPassword", error);
    res.status(500).json({ success: false, message: error.message || error });
  }
});

router.post("/updatePassword", async (req, res) => {
  const { new_password } = req.body;
  try {
    const updatePasswordForEmailResponse = await supabaseInstance.auth.updateUser({
      password: new_password
    })
    if (updatePasswordForEmailResponse?.data) {
      res.send({
        success: true,
        message: "Password Updated succesfully",
      });
    } else {
      throw updatePasswordForEmailResponse.error;
    }
  } catch (error) {
    logger.error("Error in admin/updatePassword", error);
    res.status(500).json({ success: false, message: error.message || error });
  }
});

router.post("/updateAdminPassword", async (req, res) => {
  const { password, adminId } = req.body;

  try {
    const { data, error } = await supabaseInstance.auth.admin.updateUserById(adminId, { password: password });
    if (data) {
      res.status(200).json({
        success: true,
        message: "Password updated succesfully",
      });
    } else {
      throw error;
    }
  } catch (error) {
    logger.error("Error in admin/updateAdminPassword", error);
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/updateAdmin/:adminId", async (req, res) => {
  const { adminId } = req.params;
  const adminData = req.body;
  delete adminData?.email;
  delete adminData?.password;
  console.log("adminData", adminData)
  try {
    const { data, error } = await supabaseInstance
      .from("Super_Admin_Users")
      .update({ ...adminData })
      .eq("adminId", adminId)
      .select("*");

    if (data) {
      res.status(200).json({
        success: true,
        message: "Data updated succesfully",
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    logger.error("Error in admin/updateAdmin", error);
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/updateOutletAdmin/:outletAdminId", async (req, res) => {
  const { outletAdminId } = req.params;
  const { name, mobile } = req.body;
  try {
    const { data, error } = await supabaseInstance
      .from("Outlet_Admin")
      .update({ name, mobile })
      .eq("outletAdminId", outletAdminId)
      .select("*");

    if (data) {
      res.status(200).json({
        success: true,
        message: "Data updated succesfully",
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    logger.error("Error in admin/updateOutletAdmin", error);
    res.status(500).json({ success: false, error: error });
  }
});

router.get("/getOutletAdminList", async (req, res) => {
  const { page, perPage, searchText, sortBy } = req.query;
  const pageNumber = parseInt(page) || 1;
  const itemsPerPage = parseInt(perPage) || 10;

  try {

    let query = supabaseInstance.rpc("get_outlet_admin_list", {}, { count: "exact" }).range((pageNumber - 1) * itemsPerPage, pageNumber * itemsPerPage - 1)

    if (searchText) {
      query = query.or(`name.ilike.%${searchText}%,outletname.ilike.%${searchText}%`);
    }

    if (sortBy === "name") {
      query = query.order("name", { ascending: true });
    } else if (sortBy === "date") {
      query = query.order("created_at", { ascending: false });
    } else {
      query = query.order("updated_at", { ascending: false });
    }

    const { data, error, count } = await query;

    if (data) {
      const totalPages = Math.ceil(count / itemsPerPage);
      res.status(200).json({
        success: true,
        data,
        meta: {
          page: pageNumber,
          perPage: itemsPerPage,
          totalPages,
          totalCount: count,
        },
      });
    } else {
      throw error;
    }
  } catch (error) {
    logger.error("Error in admin/getOutletAdminList", error);
    res.status(500).json({ success: false, error: error.message });
  }
});

router.get("/getAllCustomers", async (req, res) => {
  try {
    const {data,error} = await supabaseInstance.from("Customer").select("customerName, mobile, customerAuthUID");

    if(data){
      return res.status(200).json({ success: true, data });
    }
    throw error;
  } catch (error) {
    logger.error("Error in admin/getAllCustomers", error);
    return res.status(500).json({ success: false, error });
  }
});

// Helper function to fetch FCM tokens for multiple apps and customers
async function fetchFCMTokens(customerAuthUIDs, appNames) {
  try {
    const tokens = {};
    for (const appName of appNames) {
      const { data, error } = await supabaseInstance
        .from("FCM_Tokens")
        .select("fcmToken, customerAuthUID")
        .in("customerAuthUID", customerAuthUIDs)
        .eq("appName", appName);

      if (error) throw error;
      tokens[appName] = data || [];
    }
    return { success: true, tokens };
  } catch (error) {
    logger.error("Error in admin/fetchFCMTokens", error);
    return { success: false, error };
  }
}

router.post("/sendNotifications", async (req, res) => {
  try {
    const { title, body, selectedCustomers, selectedApps } = req.body;
    
    if (!selectedCustomers?.length || !selectedApps?.length) {
      return res.status(400).json({ 
        success: false, 
        error: "Selected customers and apps are required" 
      });
    }

    // Fetch FCM tokens for all apps
    const { success, tokens, error: fetchError } = await fetchFCMTokens(
      selectedCustomers, 
      selectedApps
    );

    if (!success) {
      throw fetchError;
    }

    // Send notifications for each app
    const results = [];
    for (const appName of selectedApps) {
      const appTokens = tokens[appName].map(t => t.fcmToken);
      if (appTokens.length > 0) {
        const result = await sendAdminNotifications(
          title,
          body,
          appTokens,
          appName
        );
        results.push({ appName, ...result });
      }
    }

    // Aggregate results
    const aggregateResults = {
      totalProcessed: results.reduce((sum, r) => sum + r.totalProcessed, 0),
      totalSuccess: results.reduce((sum, r) => sum + r.successCount, 0),
      totalFailures: results.reduce((sum, r) => sum + r.failureCount, 0),
      totalInvalidTokens: results.reduce((sum, r) => sum + r.invalidTokensRemoved, 0),
      resultsByApp: results
    };

    return res.status(200).json({ 
      success: true, 
      message: "Notifications sent successfully",
      ...aggregateResults
    });

  } catch (error) {
    logger.error("Error in admin/sendNotifications", error);
    return res.status(500).json({ success: false, error });
  }
});

/*
  --- Add money to customer wallet from super admin ---
  2024-11-21
  body : customerAuthUID array, amount, description
  
  - get the walletId from customerAuthUID
  - if walletId not found, create wallet
  - add the amount to wallet
  - add the transaction history
  - send notification to customer
*/
router.post("/topupCustomerWalletFromAdmin", async (req, res) => {
  const { customerAuthUIDs, amount, description } = req.body;

  if (
    !customerAuthUIDs ||
    !Array.isArray(customerAuthUIDs) ||
    !amount ||
    !description
  ) {
    return res.status(400).json({
      success: false,
      error: "customerAuthUIDs array, amount and description are required",
    });
  }

  try {
    const results = {
      successful: [],
      failed: [],
    };

    // Process each customerAuthUID
    for (const customerAuthUID of customerAuthUIDs) {
      try {
        // Get or create wallet
        let walletData;
        const { data: existingWallet, error: walletError } =
          await supabaseInstance
            .from("Wallet")
            .select("*")
            .eq("customerAuthUID", customerAuthUID)
            .maybeSingle();

        if (walletError) throw walletError;

        if (!existingWallet) {
          const { data: newWallet, error: createWalletError } =
            await supabaseInstance
              .from("Wallet")
              .insert({ customerAuthUID })
              .select("*")
              .single();

          if (createWalletError) throw createWalletError;
          walletData = newWallet;
        } else {
          walletData = existingWallet;
        }

        // Update wallet balance
        const walletId = walletData.walletId;
        const newBalance = walletData.balance + amount;
        const { data: updatedWallet, error: updateError } =
          await supabaseInstance
            .from("Wallet")
            .update({ balance: newBalance })
            .eq("walletId", walletId)
            .select("*")
            .single();

        if (updateError) throw updateError;

        // Add transaction history
        const { data: transaction, error: transactionError } =
          await supabaseInstance
            .from("Wallet_Transaction")
            .insert({
              walletId,
              amount,
              description,
              bearer: "ADMIN",
              transactionType: "CREDIT",
              customerAuthUID,
            })
            .select("*")
            .single();

        if (transactionError) throw transactionError;

        // Get FCM token and send notification
        const { data: customerData, error: customerError } =
          await supabaseInstance
            .from("FCM_Tokens")
            .select("fcmToken, customerAuthUID")
            .eq("customerAuthUID", customerAuthUID);

        if (customerError) throw customerError;

        if (customerData && customerData.length > 0) {
          const fcmToken = customerData[0].fcmToken;
          const { error: notificationError } = await sendAdminNotifications(
            "Wallet Topup",
            `Your wallet has been credited with ₹${amount} from admin`,
            [fcmToken],
            "Customer"
          );

          if (notificationError) throw notificationError;
        }

        // Add to successful results
        results.successful.push({
          customerAuthUID,
          wallet: updatedWallet,
          transaction,
        });
      } catch (error) {
        // Add to failed results
        results.failed.push({
          customerAuthUID,
          error: error.message || "Unknown error occurred",
        });
      }
    }

    // Return response based on results
    if (results.failed.length === 0) {
      return res.status(200).json({
        success: true,
        message: "All wallet topups successful",
        data: results,
      });
    } else if (results.successful.length === 0) {
      return res.status(500).json({
        success: false,
        message: "All wallet topups failed",
        data: results,
      });
    } else {
      return res.status(207).json({
        success: true,
        message: "Some wallet topups were successful",
        data: results,
      });
    }
  } catch (error) {
    return res.status(500).json({
      success: false,
      error: error.message || "Unknown error occurred",
    });
  }
});

// router.get("/topupCustomerWalletFromAdmin", async (req, res) => {
//   // const { customerAuthUID, amount, description } = req.body;
//   let customerAuthUID = "a9254742-5807-4ce0-a037-d49be1687696";
//   let amount = 100;
//   let description = "Wallet Topup from Admin";
  
//   if (!customerAuthUID || !amount || !description) {
//     return res.status(400).json({ 
//       success: false, 
//       error: "customerAuthUID, amount and description are required" 
//     });
//   }

//   try {
//       //get walletId
//       let { data:walletData, error:walletError } = await supabaseInstance
//         .from("Wallet")
//         .select("*")
//         .eq("customerAuthUID", customerAuthUID)
//         .maybeSingle(); 

//       if(walletError) throw walletError;

//       if(!walletData){
//         //create wallet
//         const { data:createWalletData, error:createWalletError } = await supabaseInstance
//           .from("Wallet")
//           .insert({ customerAuthUID })
//           .select("*")
//           .single();

//         if(createWalletError) throw createWalletError;
//         walletData = createWalletData;
//       }

//       //add amount to wallet
//       const walletId = walletData.walletId;
//       const newBalance = walletData.balance + amount;
//       const { data:addAmountData, error:addAmountError } = await supabaseInstance
//         .from("Wallet")
//         .update({ balance: newBalance })
//         .eq("walletId", walletId)
//         .select("*")
//         .single();

//       if(addAmountError) throw addAmountError;

//       //add transaction history
//       const { data:transactionData, error:transactionError } = await supabaseInstance
//         .from("Wallet_Transaction")
//         .insert({ walletId, amount, description, bearer:"ADMIN", transactionType:"CREDIT", customerAuthUID:customerAuthUID })
//         .select("*")
//         .single();

//       if(transactionError) throw transactionError;

//       //send notification to customer
//       const { data:customerData, error:customerError } = await supabaseInstance
//         .from("FCM_Tokens")
//         .select("fcmToken, customerAuthUID")
//         .eq("customerAuthUID", customerAuthUID);

//       if(customerError) throw customerError;

//       if(customerData){
//         const fcmToken = customerData[0].fcmToken;
//         const { data:notificationData, error:notificationError } = await sendAdminNotifications(
//           "Wallet Topup",
//           `Your wallet has been credited with ₹${amount}`,
//           [fcmToken],
//           "Customer"
//         );

//         if(notificationError) throw notificationError;
//       }
      
//       return res.status(200).json({ success: true, message: "Wallet Topup successful", data: { walletData, addAmountData, transactionData } });
    
//   } catch (error) {
//     return res.status(500).json({ success: false, error });
//   }
    
// });

module.exports = router;


