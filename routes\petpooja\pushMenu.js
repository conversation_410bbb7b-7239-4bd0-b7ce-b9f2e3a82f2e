const axios = require("axios").default;
var express = require("express");
const moment = require("moment-timezone");
var router = express.Router();
var supabaseInstance = require("../../services/supabaseClient").supabase;
var petpoojaconfig = require("../../configs/petpoojaConfig");
const logger = require("../../services/logger");

router.post("/pushData", async (req, res) => {
  const { outletId } = req.body;
  try {
    let restaurentDataQuery = await supabaseInstance
      .from("Outlet")
      .select("*")
      .eq("outletId", outletId);
    // if (restaurantId) {
    //   restaurentDataQuery = supabaseInstance.from("Restaurant").select("*").eq("restaurantId", restaurantId);
    // } else if (restaurantId && outletId) {
    //   restaurentDataQuery = supabaseInstance.from("Outlet").select("*").eq("restaurantId", restaurantId).eq("outletId", outletId);
    // }

    const restaurentData = await restaurentDataQuery;

    let payload = {
      success: 1,
      restaurants: [
        {
          restaurantid: outletId,
          active: restaurentData?.data?.status,
          details: {
            menusharingcode: "xxxxxx",
            currency_html: "₹",
            country: "India",
            images: [],
            restaurantname: restaurentData?.data?.restaurantName,
            address: restaurentData?.data?.address, //done dynamic
            contact: restaurentData?.data?.mobile,
            latitude: "23.190394",
            longitude: "72.610591",
            landmark: "",
            city3: restaurentData?.data?.address,
            state: "Gujarat",
            minimumorderamount: "0",
            minimumdeliverytime: "40Minutes",
            deliverycharge: "40",
            deliveryhoursfrom1: "",
            deliveryhoursto1: "",
            deliveryhoursfrom2: "",
            deliveryhoursto2: "",
            sc_applicable_on: "H,P,D",
            sc_type: "2",
            sc_calculate_on: "2",
            sc_value: "5",
            tax_on_sc: "1",
            calculatetaxonpacking: 1,
            pc_taxes_id: "11213,20375",
            calculatetaxondelivery: 1,
            dc_taxes_id: "11213,20375",
            packaging_applicable_on: "ORDER",
            packaging_charge: restaurentData?.packaging_charge,
            packaging_charge_type: "",
          },
        },
      ],
      ordertypes: [
        {
          ordertypeid: 1,
          ordertype: "Delivery",
        },
        {
          ordertypeid: 2,
          ordertype: "PickUp",
        },
        {
          ordertypeid: 3,
          ordertype: "DineIn",
        },
      ],
      categories: [],
      parentcategories: [],
      items: [],
      variations: [],
      addongroups: [],
      attributes: [],
      discounts: [],
      taxes: [],
      serverdatetime: "2022-01-1811:33:13",
      db_version: "1.0",
      application_version: "4.0",
      http_code: 200,
    };

    let parentCategoryQuery = await supabaseInstance
      .from("Menu_Parent_Categories")
      .select("*")
      .eq("outletId", outletId);
    // if (restaurantId && outletId) {
    //   parentCategoryQuery = await supabaseInstance.from("Menu_Parent_Categories").select("*").eq("restaurantId", restaurantId).eq("outletId", outletId);
    // } else if (restaurantId) {
    //   parentCategoryQuery = await supabaseInstance.from("Menu_Parent_Categories").select("*").eq("restaurantId", restaurantId).is("outletId", null);
    // }
    const parentCategoryData = await parentCategoryQuery;
    for (let data of parentCategoryData?.data) {
      payload.parentcategories.push(data);
    }

    const attributeQuery = await supabaseInstance
      .from("Menu_Item_Attributes")
      .select("*");
    for (let data of attributeQuery.data) {
      payload.attributes.push(data);
    }

    let menucategoryQuery = await supabaseInstance
      .from("Menu_Categories")
      .select("*")
      .eq("outletId", outletId);
    // if (restaurantId && outletId) {
    //   menucategoryQuery = await supabaseInstance.from("Menu_Categories").select("*").eq("restaurantId", restaurantId).eq("outletId", outletId);
    // } else if (restaurantId) {
    //   menucategoryQuery = await supabaseInstance.from("Menu_Categories").select("*").eq("restaurantId", restaurantId).is("outletId", null);
    // }
    const categoryData = await menucategoryQuery;
    for (let data of categoryData.data) {
      let petpoojaObj = {
        categoryid: data?.categoryid,
        active: data.status,
        categoryrank: "16",
        parent_category_id: data?.parent_category_id,
        categoryname: data?.categoryname,
        categorytimings: "",
        category_image_url: data?.category_image_url,
      };
      payload.categories.push(petpoojaObj);
    }

    let itemQuery = await supabaseInstance
      .from("Menu_Item")
      .select("*")
      .eq("outletId", outletId);
    // if (restaurantId && outletId) {
    //   itemQuery = await supabaseInstance.from("Menu_Item").select("*").eq("restaurantId", restaurantId).eq("outletId", outletId);
    // } else if (restaurantId) {
    //   itemQuery = await supabaseInstance.from("Menu_Item").select("*").eq("restaurantId", restaurantId).is("outletId", null);
    // }

    /*
    const itemData = await itemQuery;

    const { data: addonGroups, error: addonGroupsError } =
      await supabaseInstance
        .from("ItemAddonGroups")
        .select("*, AddonGroup(*)");

    if (addonGroupsError) throw addonGroupsError;

    const variationsQuery = await supabaseInstance
      .from("Variation")
      .select("*");

    const variations = variationsQuery.data;

    // set the addonGroups in the addon field of itemData where it matches with the itemid in form of array
    itemData.data.forEach((item) => {
      item.addon = addonGroups.filter((agi) => agi.itemId === item.itemid);
      item.variation = variations.filter((v) => v.itemId === item.itemid);
    });

    for (let data of itemData.data) {
      setTimeout(() => {
        console.log("\n\n\n\nPrinting Variations : ");
        console.log(data?.variations);
      }, 3000);
      let petpoojaObj = {
        itemid: data.itemid,
        itemallowvariation: data?.itemallowvariation,
        itemrank: "52",
        item_categoryid: data?.item_categoryid,
        item_ordertype: "1,2,3",
        item_packingcharges: "",
        itemallowaddon: data?.itemallowaddon,
        itemaddonbasedon: data?.itemaddonbasedon,
        item_favorite: "0",
        ignore_taxes: "0",
        ignore_discounts: "0",
        in_stock: "2",
        cuisine: [],
        variation_groupname: "Portion Size",
        variation: data?.variation,
        addon: data?.addon,
        itemname: data?.itemname,
        item_attributeid: data?.attributeid,
        itemdescription: data?.itemdescription,
        minimumpreparationtime: data?.minimumpreparationtime,
        price: data?.price,
        active: data?.status,
        item_image_url: data?.item_image_url,
        item_tax: "sgst,cgst",
        gst_type: "services",
        nutrition: {},
      };

      //condition for variation price
      // if (data.itemallowvariation == "1" && data.variation && data.variation.length > 0) {
      //   petpoojaObj.price = data.variation[0].price;
      // }



      

      payload.items.push(petpoojaObj);
    }



*/

    const itemData = await itemQuery;

    const { data: addonGroups, error: addonGroupsError } =
      await supabaseInstance.from("ItemAddonGroups").select("*, AddonGroup(*)");

    if (addonGroupsError) throw addonGroupsError;

    const variationsQuery = await supabaseInstance
      .from("Variation")
      .select("*");

    const variations = variationsQuery.data;

    // set the addonGroups in the addon field of itemData where it matches with the itemid in form of array
    itemData.data.forEach((item) => {
      item.addon = addonGroups.filter((agi) => agi.itemId === item.itemid);
      item.variation = variations
        .filter((v) => v.itemId === item.itemid)
        .map((v) => ({
          id: v.variationId,
          variationid: v.variationId, // or any unique ID you want to assign
          name: v.name,
          groupname: "Portion Size", // assuming this is a static value or replace accordingly
          price: v.price.toString(),
          active: v.status ? "1" : "0",
          item_packingcharges: "0", // assuming this is a static value or replace accordingly
          variationrank: "1", // assuming this is a static value or replace accordingly
          addon: [], // if there are addons for variations, map them here
          variationallowaddon: 0, // assuming this is a static value or replace accordingly
        }));
    });

    for (let data of itemData.data) {
      setTimeout(() => {
        console.log("\n\n\n\nPrinting Variations : ");
        console.log(data?.variation);
      }, 3000);
      let petpoojaObj = {
        itemid: data.itemid,
        itemallowvariation: data?.itemallowvariation,
        itemrank: "52",
        item_categoryid: data?.item_categoryid,
        item_ordertype: "1,2,3",
        item_packingcharges: "",
        itemallowaddon: data?.itemallowaddon,
        itemaddonbasedon: data?.itemaddonbasedon,
        item_favorite: "0",
        ignore_taxes: "0",
        ignore_discounts: "0",
        in_stock: "2",
        cuisine: [],
        variation_groupname: "Portion Size",
        variation: data?.variation,
        addon: data?.addon,
        itemname: data?.itemname,
        item_attributeid: data?.attributeid,
        itemdescription: data?.itemdescription,
        minimumpreparationtime: data?.minimumpreparationtime,
        price: data?.price,
        active: data?.status,
        item_image_url: data?.item_image_url,
        tax_inclusive: "true",
        item_tax: "sgst,cgst",
        gst_type: "services",
        nutrition: {},
      };

      payload.items.push(petpoojaObj);
    }

    // const taxQuery = supabaseInstance.from("Tax").select("*");
    // if (restaurantId && outletId) {
    //   taxDataQuery = taxQuery.eq("restaurantId", restaurantId).eq("outletId", outletId);
    // } else if (restaurantId) {
    //   taxDataQuery = taxQuery.eq("restaurantId", restaurantId).is("outletId", null);
    // }
    // const taxData = await taxDataQuery;
    // for (let data of taxData.data) {
    //   let petpoojaObj = {
    //     taxid: data.taxid,
    //     taxname: data.taxname,
    //     tax: data.tax,
    //     taxtype: "1",
    //     tax_ordertype: "",
    //     active: "1",
    //     tax_coreortotal: "2",
    //     tax_taxtype: "1",
    //     rank: "1",
    //     consider_in_core_amount: "0",
    //     description: ""
    //   }
    //   payload.taxes.push(petpoojaObj)
    // }

    console.log("payload ", payload);

    const payloadData = await axios.post(
      petpoojaconfig.config.push_menu_api,
      payload
    );

    if (restaurentData) {
      res.status(200).json({
        success: true,
        data: payload,
        petpooja: payloadData?.data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    logger.error("Error in pushMenu", error);
    res.status(500).json({ success: false, error: error });
  }
});

// router.post("/pushData", async (req, res) => {
//   try {
//     const { outletId } = req.body;

//     // Fetch restaurant data
//     const { data: restaurantData } = await supabaseInstance
//       .from("Outlet")
//       .select("*")
//       .eq("outletId", outletId)
//       .single();

//     if (!restaurantData) {
//       throw new Error("Restaurant data not found");
//     }

//     const payload = {
//       success: 1,
//       restaurants: [
//         {
//           restaurantid: outletId,
//           active: restaurantData.status,
//           details: {
//             menusharingcode: "xxxxxx",
//             currency_html: "₹",
//             country: "India",
//             images: [],
//             restaurantname: restaurantData.restaurantName,
//             address: restaurantData.address,
//             contact: restaurantData.mobile,
//             latitude: "23.190394",
//             longitude: "72.610591",
//             landmark: "",
//             city3: restaurantData.address,
//             state: "Gujarat",
//             minimumorderamount: "0",
//             minimumdeliverytime: "40Minutes",
//             deliverycharge: "40",
//             deliveryhoursfrom1: "",
//             deliveryhoursto1: "",
//             deliveryhoursfrom2: "",
//             deliveryhoursto2: "",
//             sc_applicable_on: "H,P,D",
//             sc_type: "2",
//             sc_calculate_on: "2",
//             sc_value: "5",
//             tax_on_sc: "1",
//             calculatetaxonpacking: 1,
//             pc_taxes_id: "11213,20375",
//             calculatetaxondelivery: 1,
//             dc_taxes_id: "11213,20375",
//             packaging_applicable_on: "ORDER",
//             packaging_charge: restaurantData?.packaging_charge,
//             packaging_charge_type: "",
//           },
//         },
//       ],
//       ordertypes: [
//         { ordertypeid: 1, ordertype: "Delivery" },
//         { ordertypeid: 2, ordertype: "PickUp" },
//         { ordertypeid: 3, ordertype: "DineIn" },
//       ],
//       categories: [],
//       parentcategories: [],
//       items: [],
//       variations: [],
//       addongroups: [],
//       attributes: [],
//       discounts: [],
//       taxes: [],
//       serverdatetime: new Date().toISOString(),
//       db_version: "1.0",
//       application_version: "4.0",
//       http_code: 200,
//     };

//     // Fetch data in parallel
//     const [
//       parentCategoryData,
//       attributeData,
//       categoryData,
//       addongroupitemsData,
//       itemData,
//       addonGroups,
//       addonGrp,
//       variations,
//     ] = await Promise.all([
//       supabaseInstance
//         .from("Menu_Parent_Categories")
//         .select("*")
//         .eq("outletId", outletId),
//       supabaseInstance.from("Menu_Item_Attributes").select("*"),
//       supabaseInstance
//         .from("Menu_Categories")
//         .select("*")
//         .eq("outletId", outletId),
//       supabaseInstance.from("Addongroupitems").select("*"),
//       supabaseInstance
//         .from("Menu_Item")
//         .select("*")
//         .eq("outletId", outletId)
//         .order("itemid"),
//       supabaseInstance.from("ItemAddonGroups").select("*"),
//       supabaseInstance.from("AddonGroup").select("*").eq("outletId", outletId),
//       supabaseInstance.from("Variation").select("*"),
//     ]);

//     // Populate parentcategories
//     payload.parentcategories.push(...parentCategoryData.data);

//     // Populate attributes
//     payload.attributes.push(...attributeData.data);

//     // Populate categories
//     payload.categories.push(
//       ...categoryData.data.map((data) => ({
//         categoryid: data.categoryid,
//         active: data.status,
//         categoryrank: "16",
//         parent_category_id: data.parent_category_id,
//         categoryname: data.categoryname,
//         categorytimings: "",
//         category_image_url: data.category_image_url,
//       }))
//     );

//     // Populate addongroupitems
//     payload.addongroupitems = addongroupitemsData.data.map((data) => ({
//       addonitemid: data.id,
//       addonitem_name: data.name,
//       addonitem_price: data.price,
//       active: data.active,
//       attributes: data.attributeId,
//       addonitem_rank: data.rank,
//     }));

//     // Process addongroups
//     const addongroupsForPayload = addonGroups.data.map((agi) => {
//       const group = addonGrp.data.find((ag) => ag.id === agi.groupId);
//       return {
//         ...agi,
//         addongroup_name: group?.name || "",
//         addongroup_rank: group?.rank || "",
//       };
//     });
//     payload.addongroups = addongroupsForPayload;

//     // Process items
//     itemData.data.forEach((item) => {
//       const addon = addonGroups.data.filter(
//         (agi) => agi.itemId === item.itemid
//       );
//       const variation = variations.data.filter((v) => v.itemId === item.itemid);
//       payload.items.push({
//         itemid: item.itemid,
//         itemallowvariation: item.itemallowvariation,
//         itemrank: "52",
//         item_categoryid: item.item_categoryid,
//         item_ordertype: "1,2,3",
//         item_packingcharges: "",
//         itemallowaddon: item.itemallowaddon,
//         itemaddonbasedon: item.itemaddonbasedon,
//         item_favorite: "0",
//         ignore_taxes: "0",
//         ignore_discounts: "0",
//         in_stock: "2",
//         cuisine: [],
//         variation_groupname: "",
//         variation,
//         addon,
//         itemname: item.itemname,
//         item_attributeid: item.attributeid,
//         itemdescription: item.itemdescription,
//         minimumpreparationtime: item.minimumpreparationtime,
//         price: item.price,
//         active: item.status,
//         item_image_url: item.item_image_url,
//         item_tax: "sgst,cgst",
//         gst_type: "services",
//         nutrition: {},
//       });
//     });

//     console.log("Data Pushed Successfully", payload);
//     // send the payload to the petpooja
//     let payloadData;
//     try {
//       payloadData = await axios.post(
//         petpoojaconfig.config.push_menu_api,
//         payload
//       );
//     } catch (error) {
//       console.error("Error pushing data:", error);
//       return res.status(500).json({ success: false, error: error });
//     }

//     if (restaurantData) {
//       res.status(200).json({
//         success: true,
//         data: payload,
//         petpooja: payloadData?.data,
//       });
//     } else {
//       throw error;
//     }
//   } catch (error) {
//     console.error("Error pushing data:", error);
//     return res.status(500).json({ success: false, error: error });
//   }
// });

router.post("/orderStatus", async (req, res) => {
  const { restaurantId, orderId } = req.body;
  try {
    const { data, error } = await supabaseInstance
      .from("Order")
      .select("*")
      .eq("restaurantId", restaurantId)
      .eq("orderId", orderId)
      .maybeSingle();
    let payload = {
      restID: restaurantId,
      orderID: orderId,
      status: data.orderStatusId,
      cancel_reason: "",
      minimum_prep_time: 20,
      minimum_delivery_time: "",
      rider_name: "",
      rider_phone_number: "",
      is_modified: "No",
    };

    //  const payloadData = await axios.post(petpoojaconfig.config.order_status_api, payload);

    if (data) {
      res.status(200).json({
        success: true,
        data: payload,
        petpooja: payloadData?.data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    logger.error("Error in orderStatus", error);
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/fetchMenu", async (req, res) => {
  const { outletId } = req.body;
  try {
    let catgoryData = [];
    let taxData = [];
    let itemData = [];
    const options = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "app-key": petpoojaconfig.config.app_key,
        "app-secret": petpoojaconfig.config.app_secret,
        "access-token": petpoojaconfig.config.access_token,
      },
    };

    const categoryid = await supabaseInstance
      .from("Menu_Categories")
      .select("categoryid")
      .eq("outletId", outletId);
    const taxid = await supabaseInstance
      .from("Tax")
      .select("taxid")
      .eq("outletId", outletId);
    const itemid = await supabaseInstance
      .from("Menu_Item")
      .select("itemid")
      .eq("outletId", outletId);
    const parentcategoryid = await supabaseInstance
      .from("Menu_Parent_Categories")
      .select("parent_category_id")
      .eq("outletId", outletId);

    const categoryId = categoryid.data.map((c) => c.categoryid);
    const taxId = taxid.data.map((t) => t.taxid);
    const itemId = itemid.data.map((i) => i.itemid);
    const parentcategoryId = parentcategoryid.data.map(
      (c) => c.parent_category_id
    );

    // const menuCategoryDataRemove = await supabaseInstance.from("Menu_Categories").delete().in("categoryid", categoryId);
    // const menuItemDataRemove = await supabaseInstance.from("Menu_Item").delete().in("itemid", itemId);
    // const parentDataRemove = await supabaseInstance.from("Menu_Parent_Categories").delete().in("parent_category_id", parentcategoryId);
    // const taxDataRemove = await supabaseInstance.from("Tax").delete().in("taxid", taxId);

    const data = {
      restID: outletId,
    };
    const payloadData = await axios.post(
      petpoojaconfig.config.fetch_menu_api,
      data
    );

    // for (let data of payloadData?.data?.taxes) {
    //   taxQuery = await supabaseInstance.from("Tax").insert({ taxid: data.taxid, taxname: data.taxname, tax: data.tax, outletId: outletId }).select("*");
    //   taxData.push(taxQuery.data[0])
    // }

    for (let data of payloadData?.data?.categories) {
      categoryQuery = await supabaseInstance
        .from("Menu_Categories")
        .insert({
          outletId: outletId,
          categoryid: data.categoryid,
          categoryname: data.categoryname,
          status: data.active,
          //  parent_category_id:data.parent_category_id,
          category_image_url: data.category_image_url,
        })
        .select("*");
      catgoryData.push(categoryQuery.data[0]);
    }

    for (let data of payloadData?.data?.items) {
      let price = Number(data.price);
      
      if(price<=0) continue;

      let minimumpreparationtime = Number(data.minimumpreparationtime);
      itemQuery = await supabaseInstance
        .from("Menu_Item")
        .insert({
          itemid: data.itemid,
          // item_categoryid:data.item_categoryid,
          itemname: data.itemname,
          outletId: outletId,
          itemdescription: data.itemdescription,
          minimumpreparationtime: minimumpreparationtime,
          price: price,
          status: data.active,
          item_image_url: data.item_image_url,
          attributeid: data.item_attributeid,
          itemdescription: data.itemdescription,
        })
        .select("*");
      itemData.push(itemQuery.data[0]);
    }

    for (let data of payloadData?.data?.parentcategories) {
      parentcategoryQuery = await supabaseInstance
        .from("Menu_Parent_Categories")
        .insert({
          outletId: outletId,
          parent_category_id: data.id,
          parentCategoryName: data.name,
          status: data.active,
          parent_category_image_url: data.image_url,
        })
        .select("*");
    }

    if (payloadData?.data) {
      res.status(200).json({
        success: true,
        data: {
          categories: catgoryData,
          items: itemData,
          taxes: taxData,
          // parentcategory:parentcategoryQuery.data
        },
      });
    } else {
      throw error;
    }
  } catch (error) {
    logger.error("Error in fetchMenu", error);
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/fetchMenuCard", async (req, res) => {
  const { outletId } = req.body;
  const fetchMenuCardResponse = await fetchMenuCard(outletId);
  res.status(fetchMenuCardResponse?.status).json(fetchMenuCardResponse);
});

async function fetchMenuCard(outletId) {
  return new Promise(async (resolve, reject) => {
    try {
      const petPoojaQuery = await supabaseInstance
        .from("Outlet")
        .select("*")
        .eq("outletId", outletId)
        .maybeSingle();

      if (petPoojaQuery?.data) {
        const options = {
          headers: {
            "Content-Type": "application/json",
            "app-key": petPoojaQuery.data?.petPoojaAppKey,
            "app-secret": petPoojaQuery.data?.petPoojaAppSecret,
            "access-token": petPoojaQuery.data?.petPoojaApAccessToken,
          },
        };

        const data = {
          restID: petPoojaQuery.data?.petPoojaRestId,
        };
        const payloadData = await axios.post(
          petpoojaconfig.config.fetch_menu_api,
          data,
          options
        );

        if (payloadData?.data) {
          if (
            !petPoojaQuery?.data?.petpoojaMenuBackup &&
            payloadData?.data?.success === "1"
          ) {
            const petpoojaMenuBackup = await supabaseInstance
              .from("Outlet")
              .update({ petpoojaMenuBackup: payloadData?.data })
              .select("*")
              .eq("outletId", outletId)
              .maybeSingle();
          }

          resolve({
            status: 200,
            success: true,
            data: payloadData.data,
            op: {
              fetch_menu_api: petpoojaconfig.config.fetch_menu_api,
              data,
              options,
            },
          });
        } else {
          throw error;
        }
      } else {
        resolve({ status: 500, success: false, error: "Outlet not found." });
      }
    } catch (error) {
      logger.error("Error in fetchMenuCard", error);
      resolve({ status: 500, success: false, error: error });
    }
  });
}

//? ref => https://onlineorderingapisv210.docs.apiary.io/#/reference/0/save-order?mc=reference%2F0%2Fsave-order%2Fsave-order%2F200
function saveOrderToPetpooja(request, orderId) {
  return new Promise(async (resolve, reject) => {
    try {
      const orderData = await supabaseInstance
        .from("Order")
        .select(
          "*, outletId(*), customerAuthUID(*), txnid(*), Order_Schedule!left(*), Order_Item!left(*, itemId(*)), DeliveryAddress!left(*)"
        )
        .eq("orderId", orderId)
        .maybeSingle();
      // console.log("orderData => ", orderData);
      // console.log("orderData Order_Schedule => ", orderData.data?.Order_Schedule[0]);
      // console.log("orderData Order_Item => ", orderData.data?.Order_Item);
      // console.log("orderData outletId => ", orderData.data?.outletId);

      if (
        orderData.data?.outletId?.isOrderHandlingFromPetpooja &&
        orderData.data?.outletId?.petPoojaAppKey &&
        orderData.data?.outletId?.petPoojaAppSecret &&
        orderData.data?.outletId?.petPoojaApAccessToken &&
        orderData.data?.outletId?.petPoojaRestId
      ) {
        let payload = {
          app_key: orderData.data?.outletId?.petPoojaAppKey,
          app_secret: orderData.data?.outletId?.petPoojaAppSecret,
          access_token: orderData.data?.outletId?.petPoojaApAccessToken,
          orderinfo: {
            OrderInfo: {
              //* Done
              Restaurant: {
                details: {
                  res_name: orderData.data?.outletId?.outletName,
                  address: orderData.data?.outletId?.address,
                  contact_information: String(orderData.data?.outletId?.mobile),
                  restID: orderData.data?.outletId?.petPoojaRestId,
                },
              },

              //* Done
              Customer: {
                details: {
                  email: orderData.data?.customerAuthUID?.email,
                  name: orderData.data?.customerAuthUID?.customerName,
                  address: orderData.data?.DeliveryAddress?.[0]?.address || "",
                  phone: String(orderData.data?.customerAuthUID?.mobile),
                  latitude: "23.190394",
                  longitude: "72.610591",
                },
              },

              //* Done
              Order: {
                details: {
                  orderID: String(orderData?.data?.orderSequenceId),
                  created_on: moment(
                    new Date(orderData.data?.created_at)
                  ).format("YYYY-MM-DD HH:mm:ss"),
                  preorder_date:
                    orderData.data?.Order_Schedule?.[0]?.scheduleDate,
                  preorder_time:
                    orderData.data?.Order_Schedule?.[0]?.scheduleTime,

                  order_type: orderData.data?.isDelivery
                    ? "H"
                    : orderData.data?.isPickUp
                    ? "P"
                    : "D",

                  payment_type: orderData.data?.isCashOrder ? "COD" : "ONLINE",
                  // total: String((orderData.data?.txnid?.amount - Number(Number(orderData.data?.txnid?.foodGST) - Number(orderData.data?.txnid?.convenienceTotalAmount)).toFixed(2)) || ""), //todo add charge
                  total: String(
                    Number(orderData.data?.txnid?.itemTotalPrice).toFixed(2) ||
                      ""
                  ), //todo add charge
                  tax_total: String(
                    Number(
                      Number(orderData.data?.txnid?.foodGST) +
                        Number(orderData.data?.txnid?.convenienceTotalAmount)
                    ).toFixed(2) || ""
                  ),
                  packing_charges: String(
                    orderData.data?.txnid?.packagingCharge || ""
                  ),
                  pc_tax_amount: "", //* Tax amount calculated on packing charge
                  pc_gst_details: [], //* Packing Charge GST liability with amount. It will be there in Order object (Required for Ecomm platform)
                  delivery_charges: String(
                    orderData.data?.txnid?.deliveryCharge || ""
                  ), //todo add delivery charge
                  dc_tax_amount: "", //* Tax amount calculated on delivery charge
                  dc_gst_details: [], //* Delivery Charge GST liability with amount. It will be there in Order object (Required for Ecomm platform)
                  service_charge: "", //* Service charge applicable at order level.
                  sc_tax_amount: "", //* Tax calculated on service charge
                  discount_total: "",
                  discount_type: "F",

                  description: orderData?.data?.additional_Instruction || "",
                  // min_prep_time: orderData.data?.isScheduleNow ? orderData.data?.preparationTime : 0,
                  callback_url: `https://${request.get(
                    "host"
                  )}/petpooja/pushMenu/petpooja-status-change/${orderId}`,
                  // callback_url: `https://mealpe-testing-api.onrender.com/petpooja/pushMenu/petpooja-status-change/${orderId}`,
                  enable_delivery: 1, //*Values can be 0 or 1 where 0 means Rider from thirdparty side will come and 1 means Rider from Restaurant i.e. self delivery order.

                  ondc_bap: "MealPe", //*An identifier to indicate if the order is from ONDC by passing the buyer app name.
                  advanced_order: "N", //* Flag which says that placed order is advance or not.Value is Boolean either Y or N.

                  table_no: "",
                  no_of_persons: "0",
                  otp: orderData?.data?.orderOTP || "",
                },
              },

              //* Done
              OrderItem: {
                details: [],
              },

              Tax: {
                details: [
                  {
                    id: "01",
                    title: "CGST",
                    type: "P",
                    price: "2.5",
                    tax: String(
                      Number(orderData.data?.txnid?.foodGST / 2)?.toFixed(2) ||
                        ""
                    ),
                    restaurant_liable_amt: "",
                  },
                  {
                    id: "02",
                    title: "SGST",
                    type: "P",
                    price: "2.5",
                    tax: String(
                      Number(orderData.data?.txnid?.foodGST / 2)?.toFixed(2) ||
                        ""
                    ),
                    restaurant_liable_amt: "",
                  },
                  {
                    id: "03",
                    title: "Convenience Fee",
                    type: "F",
                    // "tax": String(orderData.data?.txnid?.convenienceTotalAmount + orderData.data?.txnid?.commissionTotalAmount) || ''
                    tax: String(
                      orderData.data?.txnid?.convenienceTotalAmount || ""
                    ),
                    restaurant_liable_amt: "",
                  },
                  // {
                  //   "id": "03",
                  //   "title": "Convenience Amount",
                  //   "type": "F",
                  //   // "price": "2.5",
                  //   "tax": String(orderData.data?.txnid?.convenienceTotalAmount) || ''
                  // },
                  // {
                  //   "id": "04",
                  //   "title": "Commission Amount",
                  //   "type": "F",
                  //   // "price": "2.5",
                  //   "tax": String(orderData.data?.txnid?.commissionTotalAmount) || ''
                  // }
                ],
              },
              // Discount: {}
            },
            udid: "",
            device_type: "Web",
          },
        };

        for (let itemData of orderData.data?.Order_Item) {
          let _itemPrice = itemData.itemPrice;
          // if (orderData?.data?.txnid?.isGSTApplied === false) {
          //   // _itemPrice = itemData.itemPrice - Number((5*itemData.itemPrice)/100).toFixed(2);
          //   _itemPrice = Number(((itemData.itemPrice * 100) / 105).toFixed(2));
          // }

          let petpoojaOrderObj = {
            id: itemData?.itemId?.petpoojaItemId
              ? itemData?.itemId?.petpoojaItemId
              : itemData?.itemId?.itemid,
            name: itemData?.itemId?.itemname,
            description: "",

            gst_liability: "vendor", //* Required for Ecomm platform and Optional for others.GST liability ownership. It will be there in the item object (vendor/restaurant)
            item_tax: [], //* Tax calculated at item level after discount
            item_discount: "0",
            final_price: _itemPrice, //* Item price after discount. If there is no discount, Price and finalPrice objects will have the same value.
            price: _itemPrice, //* Unit price of item.(This price includes addonitems price and if variations then includes variation price.)
            // price: itemData.calculatedPrice,
            quantity: itemData.quantity,

            variation_name: itemData?.variation?.name || "",
            variation_id: itemData?.variation?.variationId || "",
            AddonItem: {
              details: [],
              addonItems: itemData?.addons || [],
            },
          };
          payload.orderinfo.OrderInfo.OrderItem.details.push(petpoojaOrderObj);
        }

        // const payloadData = await axios.post(petpoojaconfig.config.save_order_api, payload);

        let saveOrderReaponse = null;
        let saveOrderError = null;
        axios
          .post(petpoojaconfig.config.save_order_api, payload)
          .then((_saveOrderReaponse) => {
            if (_saveOrderReaponse.data?.success === "1") {
              saveOrderReaponse = _saveOrderReaponse.data;
            } else {
              saveOrderError = _saveOrderReaponse.data;
            }
          })
          .catch((_saveOrderError) => {
            let _err =
              _saveOrderError?.response?.data ||
              _saveOrderError?.response ||
              _saveOrderError;
            saveOrderError = _err;
          })
          .finally(async () => {
            let _postObject = {
              orderId: orderId,
              postBody: payload,
            };
            if (saveOrderReaponse) {
              _postObject.success = saveOrderReaponse;
            }
            if (saveOrderError) {
              _postObject.error = saveOrderError;
            }

            const insertResponse = await supabaseInstance
              .from("Order_Save_Petpooja")
              .insert(_postObject)
              .select("*")
              .maybeSingle();
            console.log("insertResponse => ", insertResponse);
            resolve({
              success: Boolean(saveOrderReaponse),
              Order_Save_Petpooja: insertResponse?.data || null,
            });
          });
      } else {
        resolve({ success: false, error: "Petpooja config not found." });
      }
    } catch (error) {
      logger.error("Error in saveOrderToPetpooja", error);
      resolve({
        success: false,
        error: error?.message || error,
      });
    }
  });
}

router.post("/saveOrderToPetpoojaTest", async (req, res) => {
  saveOrderToPetpooja(req, "b131e7ea-b5b0-4c38-90e1-c8acfe6e2a03")
    .then((response) => {
      res.send(response);
    })
    .catch((err) => {
      res.send(err);
    });
});

router.post("/petpooja-status-change/:orderId", async (req, res) => {
  // const postBody = req.body;
  // const query = req.query;
  // const params = req.params;

  // console.log("petpooja-status-change-[POST]-postBody => ", postBody);
  // console.log("petpooja-status-change-[POST]-query =>    ", query);
  // console.log("petpooja-status-change-[POST]-params =>   ", params);

  const { orderID, status, minimum_prep_time } = req.body;
  const { orderId } = req.params;
  let _status = status;

  if (status && orderId) {
    try {
      if (_status === "-1") {
        _status = "-2";
      }
      console.log("_status => ", { orderStatusId: _status });
      const orderResponse = await supabaseInstance
        .from("Order")
        .update({ orderStatusId: _status, preparationTime: minimum_prep_time })
        .eq("orderId", orderId)
        .select("*")
        .maybeSingle();
      if (orderResponse.data) {
        console.log("status change successfully");
        res.send({ success: true });
      } else {
        throw orderResponse.error;
      }
    } catch (error) {
      logger.error(`petpooja-status-change error for ${orderId} => `, error);
      res.send({ success: false });
    }
  } else {
    console.error({ _status, orderId });
    res.send({ success: false });
  }
});

function updateOrderStatus(orderId, updatedOrderStatus) {
  return new Promise(async (resolve, reject) => {
    try {
      const orderQuery = await supabaseInstance
        .from("Order")
        .select("*,outletId(*)")
        .eq("orderId", orderId)
        .maybeSingle();

      if (orderQuery?.data) {
        const orderData = orderQuery.data;
        if (
          orderData?.outletId?.petPoojaAppKey &&
          orderData?.outletId?.petPoojaAppSecret &&
          orderData?.outletId?.petPoojaApAccessToken &&
          orderData?.outletId?.petPoojaRestId
        ) {
          let payload = {
            app_key: orderData?.outletId?.petPoojaAppKey,
            app_secret: orderData?.outletId?.petPoojaAppSecret,
            access_token: orderData?.outletId?.petPoojaApAccessToken,
            restID: orderData?.outletId?.petPoojaRestId,
            orderID: orderId,
            clientorderID: orderData?.customerAuthUID,
            cancelReason: "",
            status: updatedOrderStatus,
          };

          const petPoojaUpdateOrderStatus = await axios.post(
            petpoojaconfig.config.update_order_status_api,
            payload
          );

          if (petPoojaUpdateOrderStatus.data) {
            resolve({ success: true, data: petPoojaUpdateOrderStatus.data });
          }
        } else {
          resolve({ success: false, error: "Petpooja config not found." });
        }
      } else {
        resolve({ success: false, error: "Order not found." });
      }
    } catch (error) {
      resolve({
        success: false,
        error: error?.message || error,
      });
    }
  });
}

// function getStoreStatus(outletId) {
//   return new Promise(async (resolve, reject) => {
//     try {
//       const outletQuery = await supabaseInstance.from("Outlet").select("*").eq("outletId", outletId).maybeSingle();

//       if (outletQuery?.data) {
//         const restID = outletQuery.data.petPoojaRestId;

//         if (restID) {
//           const petPoojaUpStoreStatus = await axios.post('https://private-anon-947573d619-onlineorderingapisv210.apiary-mock.com/get_store_status', restID);

//           if (petPoojaUpStoreStatus.data) {
//             resolve({ success: true, data: petPoojaUpStoreStatus.data })
//           } else {
//             console.log("Something Went Wrong.")
//             resolve({ success: false, error: "Something Went Wrong." })
//           }
//         } else {
//           console.log("restID not found.")
//           resolve({ success: false, error: "restID not found." })
//         }
//       } else {
//         console.log("Outlet not found.")
//         resolve({ success: false, error: "Outlet not found." })
//       }
//     } catch (error) {
//       resolve({ success: false, error: error?.message || error })
//     }
//   })
// };

function updateStoreStatus(outletId) {
  return new Promise(async (resolve, reject) => {
    try {
      const outletQuery = await supabaseInstance
        .from("Outlet")
        .select("petPoojaRestId,isOutletOpen,Timing!left(openTime,dayId(day))")
        .eq("outletId", outletId)
        .maybeSingle();
      if (outletQuery?.data) {
        const restID = outletQuery.data.petPoojaRestId;
        const store_status = outletQuery.data.isOutletOpen ? 1 : 0;
        let turn_on_time = null;

        outletQuery.data?.Timing.forEach((_ele, _index) => {
          if (!turn_on_time) {
            const _day = moment()
              .tz("Asia/Kolkata")
              .add(_index + 1, "days")
              .format("dddd");
            const pickedDayObject = outletQuery.data?.Timing?.find(
              (f) => f.dayId?.day === _day
            );
            if (pickedDayObject.openTime) {
              turn_on_time = moment()
                .tz("Asia/Kolkata")
                .add(_index + 1, "days")
                .format(`YYYY-MM-DD ${pickedDayObject.openTime}`);
            }
          }
        });

        if (
          restID &&
          (store_status === 0 || store_status === 1) &&
          turn_on_time
        ) {
          try {
            let payload = {
              restID: restID,
              store_status: String(store_status),
              reason: store_status ? "Store On" : "Store off",
            };
            if (store_status === 0) {
              payload["turn_on_time"] = turn_on_time;
            }
            const petPoojaUpUpdateStoreStatus = await axios.post(
              petpoojaconfig.config.update_store_status_api,
              payload
            );
            if (petPoojaUpUpdateStoreStatus.data) {
              console.log(
                "petPoojaUpUpdateStoreStatus.data===>",
                petPoojaUpUpdateStoreStatus.data
              );
              resolve({
                success: true,
                data: petPoojaUpUpdateStoreStatus.data,
              });
            } else {
              console.log("Something Went Wrong.");
              resolve({
                success: false,
                error: "Something Went Wrong.",
                petpoojaApiError: true,
              });
            }
          } catch (axiosError) {
            console.log(
              "update_store_status axiosError => ",
              axiosError?.response?.data
            );
            resolve({
              success: false,
              error: axiosError?.response?.data,
              petpoojaApiError: true,
            });
          }
        } else {
          console.log("restID not found.");
          resolve({
            success: false,
            error: "restID not found.",
            petpoojaApiError: false,
          });
        }
      } else {
        resolve({
          success: false,
          error: "Restaurent Data Not Found.",
          petpoojaApiError: false,
        });
      }
    } catch (error) {
      logger.error("Error in updateStoreStatus", error);
      resolve({
        success: false,
        error: error?.message || error,
        petpoojaApiError: false,
      });
    }
  });
}

// updateStoreStatus("08d06cbe-27d1-4f4b-87e8-38e341622625")

// function updateItemAddon(outletId, itemID) {
//   return new Promise(async (resolve, reject) => {
//     try {
//       const outletQuery = await supabaseInstance.from("Outlet").select("petPoojaRestId,Menu_Item!left(itemid,status)").eq("outletId", outletId).eq("Menu_Item.itemid", itemID).maybeSingle();

//       if (outletQuery?.data) {

//         const restID = outletQuery?.data?.petPoojaRestId;
//         const inStock = outletQuery?.data?.Menu_Item[0]?.status;

//         if (restID && inStock) {
//           let payload = {
//             "restID": restID,
//             "type": "item",
//             "inStock": inStock,
//             "itemID": [itemID],
//             "autoTurnOnTime": "custom",
//             "customTurnOnTime": "2020-02-24 18:00"
//           }

//           const petPoojaUpUpdateStoreStatus = await axios.post('https://private-anon-947573d619-onlineorderingapisv210.apiary-mock.com/item_stock_off', payload);

//           if (petPoojaUpUpdateStoreStatus.data) {
//             resolve({ success: true, data: petPoojaUpUpdateStoreStatus.data })
//           } else {
//             resolve({ success: false, error: "Something Went Wrong." })
//           }
//         } else {
//           resolve({ success: false, error: "restID not found." })
//         }
//       } else {
//         resolve({ success: false, error: "Restaurent Data Not Found." })
//       }
//     } catch (error) {
//       resolve({ success: false, error: error?.message || error })
//     }
//   })
// };
/**
 * @description This api is used to sync menu from petpooja to the database.
 */
router.post("/dev/sync-menu-from-petpooja", async (req, res) => {
  console.log("Starting sync-menu-from-petpooja endpoint");
  const { outletId } = req.body;
  console.log("Outlet ID received:", outletId);
  
  try {
    // 1. Validate and fetch outlet data
    console.log("Fetching outlet data from Supabase...");
    const { data: outlet, error: outletError } = await supabaseInstance
      .from("Outlet")
      .select("*")
      .eq("outletId", outletId)
      .maybeSingle();

    if (outletError) {
      logger.error("Error fetching outlet data:", outletError);
      throw new Error("Error fetching outlet data");
    }
    if (!outlet?.petPoojaRestId) {
      console.warn("Petpooja RestId not found for outlet:", outletId);
      return res.status(400).json({ 
        success: false, 
        message: "Petpooja RestId not found for this outlet." 
      });
    }
    console.log("Successfully fetched outlet data");

    // delete the existing menu from the database in the outlet
    const { data: deleteMenu, error: deleteMenuError } = await supabaseInstance
      .from("Menu_Parent_Categories")
      .delete()
      .eq("outletId", outletId);
    
    if (deleteMenuError) {
      logger.error("Error deleting menu:", deleteMenuError);
      throw new Error("Error deleting menu");
    }

    const { data : deleteAddonGroup, error : deleteAddonGroupError } = await supabaseInstance
      .from("AddonGroup")
      .delete()
      .eq("outletId", outletId);
    
    if (deleteAddonGroupError) {
      logger.error("Error deleting addon group:", deleteAddonGroupError);
      throw new Error("Error deleting addon group");
    }

    console.log("Successfully deleted menu");
      

    // 2. Fetch menu from Petpooja API
    console.log("Fetching menu from Petpooja API...");
    // const menuResponse = await axios.post(
    //   "https://private-anon-d96473aa3c-onlineorderingapisv210.apiary-mock.com/fetchmenu",
    //   { restID: "pb9at4f5" },
    //   {
    //     headers: {
    //       "Content-Type": "application/json",
    //       "app-key": " z5w1mpaq37rhk6tnxc4u2jisygob9efd",
    //       "app-secret": "913bd50b6892d573ac9d2bcb9d0df48ff553d289",
    //       "access-token": "43567059669fc7dbb3453a2d01fba500e5287f2c",
    //     }
    //   }
    // );

    const menu = {
      "success": "1",
      "restaurants": [
        {
          "restaurantid": "xxxxx",
          "active": "1",
          "details": {
            "menusharingcode": "xxxxxx",
            "currency_html": "₹",
            "country": "India",
            "images": [],
            "restaurantname": "Heaven",
            "address": "nearsargasan,sghighway,Gandhinagar",
            "contact": "9998696995",
            "latitude": "23.190394",
            "longitude": "72.610591",
            "landmark": "",
            "city3": "Ahmedabad",
            "state": "Gujarat",
            "minimumorderamount": "0",
            "minimumdeliverytime": "60Minutes",
            "minimum_prep_time": "30",
            "deliverycharge": "50",
            "deliveryhoursfrom1": "",
            "deliveryhoursto1": "",
            "deliveryhoursfrom2": "",
            "deliveryhoursto2": "",
            "sc_applicable_on": "H,P,D",
            "sc_type": "2",
            "sc_calculate_on": "2",
            "sc_value": "5",
            "tax_on_sc": "1",
            "calculatetaxonpacking": 1,
            "pc_taxes_id": "11213,20375",
            "calculatetaxondelivery": 1,
            "dc_taxes_id": "11213,20375",
            "packaging_applicable_on": "ORDER",
            "packaging_charge": "20",
            "packaging_charge_type": ""
          }
        }
      ],
      "ordertypes": [
        {
          "ordertypeid": 1,
          "ordertype": "Delivery"
        },
        {
          "ordertypeid": 2,
          "ordertype": "PickUp"
        },
        {
          "ordertypeid": 3,
          "ordertype": "DineIn"
        }
      ],
      "categories": [
        {
          "categoryid": "500773",
          "active": "1",
          "categoryrank": "16",
          "parent_category_id": "0",
          "categoryname": "Pizzaandsides",
          "categorytimings": "",
          "category_image_url": ""
        },
        {
          "categoryid": "500774",
          "active": "1",
          "categoryrank": "17",
          "parent_category_id": "0",
          "categoryname": "Cakes",
          "categorytimings": "",
          "category_image_url": ""
        }
      ],
      "parentcategories": [],
      "items": [
        {
          "itemid": "*********",
          "itemallowvariation": "0",
          "itemrank": "52",
          "item_categoryid": "500773",
          "item_ordertype": "1,2,3",
          "item_tags": [
            "vegan",
            "new",
            "chef-special"
          ],
          "item_packingcharges": "",
          "itemallowaddon": "1",
          "itemaddonbasedon": "0",
          "item_favorite": "0",
          "ignore_taxes": "0",
          "ignore_discounts": "0",
          "in_stock": "2",
          "variation_groupname": "",
          "variation": [],
          "addon": [
            {
              "addon_group_id": "135699",
              "addon_item_selection_min": "0",
              "addon_item_selection_max": "1"
            },
            {
              "addon_group_id": "135707",
              "addon_item_selection_min": "0",
              "addon_item_selection_max": "4"
            }
          ],
          "itemname": "Veg Loaded Pizza",
          "item_attributeid": "1",
          "itemdescription": "",
          "minimumpreparationtime": "",
          "price": "100",
          "active": "1",
          "item_image_url": "",
          "item_tax": "11213,20375",
          "nutrition": {
            "foodAmount": {
              "amount": 1,
              "unit": "g"
            },
            "calories": {
              "amount": 1,
              "unit": "kcal"
            },
            "protien": {
              "amount": 1,
              "unit": "g"
            },
            "minerals": [
              {
                "name": "Sample",
                "amount": 1,
                "unit": "g"
              }
            ],
            "sodium": {
              "amount": 1,
              "unit": "mg"
            },
            "carbohydrate": {
              "amount": 1,
              "unit": "g"
            },
            "totalSugar": {
              "amount": 1,
              "unit": "g"
            },
            "addedSugar": {
              "amount": 1,
              "unit": "g"
            },
            "totalFat": {
              "amount": 1,
              "unit": "g"
            },
            "saturatedFat": {
              "amount": 1,
              "unit": "g"
            },
            "transFat": {
              "amount": 1,
              "unit": "g"
            },
            "cholesterol": {
              "amount": 1,
              "unit": "g"
            },
            "vitamins": [
              {
                "name": "a",
                "amount": 1,
                "unit": "g"
              }
            ],
            "additionalInfo": {
              "info": "dsfsdf",
              "remark": "dsfdsfds"
            },
            "fiber": {
              "amount": 1,
              "unit": "g"
            },
            "servingInfo": "1to2people",
            "additiveMap": {
              "Polyols": {
                "amount": 1,
                "unit": "g"
              }
            },
            "allergens": [
              {
                "allergen": "gluten",
                "allergenDesc": "dfsdfds"
              }
            ]
          }
        },
        {
          "itemid": "*********",
          "itemallowvariation": "0",
          "itemrank": "53",
          "item_categoryid": "500774",
          "item_ordertype": "1,2,3",
          "item_tags": [],
          "item_packingcharges": "",
          "itemallowaddon": "0",
          "itemaddonbasedon": "0",
          "item_favorite": "0",
          "ignore_taxes": "0",
          "ignore_discounts": "0",
          "in_stock": "2",
          "variation_groupname": "",
          "variation": [],
          "addon": [],
          "itemname": "Chocolate cake",
          "item_attributeid": "1",
          "itemdescription": "",
          "minimumpreparationtime": "",
          "price": "310",
          "active": "1",
          "item_image_url": "",
          "item_tax": "21866,21867",
          "nutrition": {
            "sodium": {
              "amount": 1,
              "unit": "Mg"
            },
            "carbohydrate": {
              "amount": 1,
              "unit": "G"
            },
            "totalSugar": {
              "amount": 1,
              "unit": "G"
            },
            "addedSugar": {
              "amount": 1,
              "unit": "G"
            },
            "cholesterol": {
              "amount": 1,
              "unit": "G"
            },
            "vitamins": [
              {
                "name": "a",
                "amount": 1,
                "unit": "G"
              }
            ],
            "additionalInfo": {
              "info": "dsfsdf",
              "remark": "dsfdsfds"
            },
            "fiber": {
              "amount": 1,
              "unit": "G"
            },
            "servingInfo": "1to2people"
          }
        },
        {
          "itemid": "7765809",
          "itemallowvariation": "0",
          "itemrank": "52",
          "item_categoryid": "500773",
          "item_ordertype": "1,2,3",
          "item_tags": [],
          "item_packingcharges": "",
          "itemallowaddon": "0",
          "itemaddonbasedon": "0",
          "item_favorite": "0",
          "ignore_taxes": "0",
          "ignore_discounts": "0",
          "in_stock": "2",
          "variation_groupname": "",
          "variation": [
            {
              "id": "7765862",
              "variationid": "89058",
              "name": "3Pieces",
              "groupname": "Quantity",
              "price": "140",
              "active": "1",
              "item_packingcharges": "20",
              "variationrank": "1",
              "addon": [],
              "variationallowaddon": 0
            },
            {
              "id": "7765097",
              "variationid": "89059",
              "name": "6Pieces",
              "groupname": "Quantity",
              "price": "160",
              "active": "1",
              "item_packingcharges": "20",
              "variationrank": "3",
              "addon": [],
              "variationallowaddon": 0
            }
          ],
          "addon": [],
          "itemname": "Garlic Bread",
          "item_attributeid": "1",
          "itemdescription": "",
          "minimumpreparationtime": "",
          "price": "140",
          "active": "1",
          "item_image_url": "",
          "item_tax": "11213,20375",
          "nutrition": {}
        }
      ],
      "variations": [
        {
          "variationid": "104220",
          "name": "Large",
          "groupname": "Quantity",
          "status": "1"
        },
        {
          "variationid": "104221",
          "name": "Small",
          "groupname": "Quantity",
          "status": "1"
        },
        {
          "variationid": "89058",
          "name": "3Pieces",
          "groupname": "Quantity",
          "status": "1"
        },
        {
          "variationid": "89059",
          "name": "6Pieces",
          "groupname": "Quantity",
          "status": "1"
        }
      ],
      "addongroups": [
        {
          "addongroupid": "135699",
          "addongroup_rank": "3",
          "active": "1",
          "addongroupitems": [
            {
              "addonitemid": "1150783",
              "addonitem_name": "Mojito",
              "addonitem_price": "0",
              "active": "1",
              "attributes": "1",
              "addonitem_rank": "1"
            },
            {
              "addonitemid": "1150784",
              "addonitem_name": "Hazelnut Mocha",
              "addonitem_price": "10",
              "active": "1",
              "attributes": "1",
              "addonitem_rank": "1"
            }
          ],
          "addongroup_name": "Add Beverage"
        },
        {
          "addongroupid": "135707",
          "addongroup_rank": "15",
          "active": "1",
          "addongroupitems": [
            {
              "addonitemid": "1150810",
              "addonitem_name": "Egg",
              "addonitem_price": "20",
              "active": "1",
              "attributes": "24",
              "addonitem_rank": "1"
            },
            {
              "addonitemid": "1150811",
              "addonitem_name": "Jalapenos",
              "addonitem_price": "20",
              "active": "1",
              "attributes": "1",
              "addonitem_rank": "1"
            },
            {
              "addonitemid": "1150812",
              "addonitem_name": "Onion Rings",
              "addonitem_price": "20",
              "active": "1",
              "attributes": "1",
              "addonitem_rank": "1"
            },
            {
              "addonitemid": "1150813",
              "addonitem_name": "Cheese",
              "addonitem_price": "10",
              "active": "1",
              "attributes": "1",
              "addonitem_rank": "1"
            }
          ],
          "addongroup_name": "Extra Toppings"
        }
      ],
      "attributes": [
        {
          "attributeid": "1",
          "attribute": "veg",
          "active": "1"
        },
        {
          "attributeid": "2",
          "attribute": "non-veg",
          "active": "1"
        },
        {
          "attributeid": "24",
          "attribute": "egg",
          "active": "1"
        }
      ],
      "discounts": [
        {
          "discountid": "363",
          "discountname": "Introductory Off",
          "discounttype": "1",
          "discount": "10",
          "discountordertype": "1,2,3",
          "discountapplicableon": "Items",
          "discountdays": "All",
          "active": "1",
          "discountontotal": "0",
          "discountstarts": "",
          "discountends": "",
          "discounttimefrom": "",
          "discounttimeto": "",
          "discountminamount": "",
          "discountmaxamount": "",
          "discounthascoupon": "0",
          "discountcategoryitemids": "7765809,7765862,7765097,*********",
          "discountmaxlimit": ""
        }
      ],
      "taxes": [
        {
          "taxid": "11213",
          "taxname": "CGST",
          "tax": "2.5",
          "taxtype": "1",
          "tax_ordertype": "1,2,3",
          "active": "1",
          "tax_coreortotal": "2",
          "tax_taxtype": "1",
          "rank": "1",
          "consider_in_core_amount": "0",
          "description": ""
        },
        {
          "taxid": "20375",
          "taxname": "SGST",
          "tax": "2.5",
          "taxtype": "1",
          "tax_ordertype": "1,2,3",
          "active": "1",
          "tax_coreortotal": "2",
          "tax_taxtype": "1",
          "rank": "2",
          "consider_in_core_amount": "0",
          "description": ""
        },
        {
          "taxid": "21866",
          "taxname": "CGST",
          "tax": "9",
          "taxtype": "1",
          "tax_ordertype": "1",
          "active": "1",
          "tax_coreortotal": "2",
          "tax_taxtype": "1",
          "rank": "5",
          "consider_in_core_amount": "0",
          "description": ""
        },
        {
          "taxid": "21867",
          "taxname": "SGST",
          "tax": "9",
          "taxtype": "1",
          "tax_ordertype": "1",
          "active": "1",
          "tax_coreortotal": "2",
          "tax_taxtype": "1",
          "rank": "6",
          "consider_in_core_amount": "0",
          "description": ""
        }
      ],
      "serverdatetime": "2022-01-1811:33:13",
      "db_version": "1.0",
      "application_version": "4.0",
      "http_code": 200
    };


    if (!menu || menu.success !== "1") {
      console.error("Failed to fetch menu from Petpooja. Response:", menu);
      return res.status(500).json({ 
        success: false, 
        message: "Failed to fetch menu from Petpooja." 
      });
    }
    console.log("Successfully fetched menu from Petpooja");

    // 3. Batch process all menu data in correct order
    console.log("Starting batch processing of menu data...");
    await Promise.all([
      // Process parent categories
      Array.isArray(menu.parentcategories) && 
      Promise.all(menu.parentcategories.map(pc => {
        console.log("Processing parent category:", pc.name);
        return supabaseInstance
          .from("Menu_Parent_Categories")
          .upsert({
            outletId,
            parentCategoryName: pc.name,
            status: pc.active === "1" || pc.active === 1,
            parent_category_image_url: pc.image_url,
            petpoojaParentCategoryId: pc.id,
          }, { onConflict: ["outletId", "petpoojaParentCategoryId"] })
      })),

      // Process categories
      Array.isArray(menu.categories) &&
      Promise.all(menu.categories.map(cat => {
        console.log("Processing category:", cat.categoryname);
        return supabaseInstance
          .from("Menu_Categories")
          .upsert({
            outletId,
            categoryname: cat.categoryname,
            status: cat.active === "1" || cat.active === 1,
            parent_category_id: cat.parent_category_id !== "0" ? cat.parent_category_id : null,
            category_image_url: cat.category_image_url,
            petpoojaCategoryId: cat.categoryid,
          }, { onConflict: ["outletId", "petpoojaCategoryId"] })
      })),

      // Process addon groups
      Array.isArray(menu.addongroups) &&
      Promise.all(menu.addongroups.map(group => {
        console.log("Processing addon group:", group.addongroup_name);
        return supabaseInstance
          .from("AddonGroup")
          .upsert({
            name: group.addongroup_name,
            rank: group.addongroup_rank,
            active: group.active,
            outletId,
            petpoojaGroupId: group.addongroupid,
          }, { onConflict: ["outletId", "petpoojaGroupId"] })
      })),
    ]);
    console.log("Completed batch processing of menu data");

    // 4. Process menu items with optimized DB queries
    if (Array.isArray(menu.items)) {
      console.log("Starting menu items processing...");
      // Fetch all category and attribute mappings at once
      const [categoryMappings, attributeMappings] = await Promise.all([
        supabaseInstance
          .from("Menu_Categories")
          .select("categoryid,petpoojaCategoryId,outletId")
          .eq("outletId", outletId),
        supabaseInstance
          .from("Menu_Item_Attributes")
          .select("attributeid,petpoojaAttributeId")
      ]);

      // Create lookup maps
      const categoryMap = new Map(categoryMappings.data?.map(c => [c.petpoojaCategoryId, c.categoryid]));
      const attributeMap = new Map(attributeMappings.data?.map(a => [a.petpoojaAttributeId, a.attributeid]));

      // Batch process menu items
      await Promise.all(menu.items.map(item => {
        console.log("Processing menu item:", item.itemname);
        return supabaseInstance
          .from("Menu_Item")
          .upsert({
            itemname: item.itemname,
            outletId,
            item_categoryid: categoryMap.get(item.item_categoryid) || null,
            attributeid: attributeMap.get(item.item_attributeid) || null,
            price: Number(item.price),
            itemdescription: item.itemdescription,
            minimumpreparationtime: item.minimumpreparationtime ? Number(item.minimumpreparationtime) : null,
            status: item.active === "1" || item.active === 1,
            item_image_url: item.item_image_url,
            petpoojaItemId: item.itemid,
          }, { onConflict: ["outletId", "petpoojaItemId"] })
      }));
      console.log("Completed menu items processing");
    }

    // 5. Process variations with optimized mapping
    if (Array.isArray(menu.variations)) {
      console.log("Starting variations processing...");
      const variationToItems = menu.items?.reduce((acc, item) => {
        item.variation?.forEach(v => {
          if (!acc[v.variationid]) acc[v.variationid] = [];
          acc[v.variationid].push(item.itemid);
        });
        return acc;
      }, {});

      await Promise.all(menu.variations.map(variation => {
        console.log("Processing variation:", variation.name);
        return supabaseInstance
          .from("Variation")
          .upsert({
            outletId,
            name: variation.name,
            groupname: variation.groupname,
            status: variation.status === "1" || variation.status === 1,
            itemArray: variationToItems[variation.variationid] || [],
            petpoojaVariationId: variation.variationid,
          }, { onConflict: ["outletId", "petpoojaVariationId"] })
      }));
      console.log("Completed variations processing");
    }

    // 6. Process addon groups and items with optimized queries
    if (Array.isArray(menu.addongroups)) {
      console.log("Starting addon groups and items processing...");
      const addonGroupMappings = await supabaseInstance
        .from("AddonGroup")
        .select("id,petpoojaGroupId,name,outletId")
        .eq("outletId", outletId);

      const addonGroupMap = new Map((addonGroupMappings.data || []).map(ag => [ag.petpoojaGroupId, ag]));

      // Upsert all addon items for each group
      await Promise.all(
        menu.addongroups.flatMap(group =>
          (group.addongroupitems || []).map(item => {
            const groupObj = addonGroupMap.get(group.addongroupid);
            if (!groupObj) {
              console.warn(`Addon group not found for groupId ${group.addongroupid}, skipping addon item ${item.addonitem_name}`);
              return Promise.resolve();
            }
            const upsertObj = {
              name: item.addonitem_name,
              price: item.addonitem_price ? String(item.addonitem_price) : "0",
              active: item.active,
              addonGroupId: groupObj.id,
              rank: item.addonitem_rank ? String(item.addonitem_rank) : null,
              petpoojaAddonItemId: item.addonitemid,
            };
            return supabaseInstance
              .from("Addongroupitems")
              .upsert(upsertObj, { onConflict: ["petpoojaAddonItemId"] })
              .then(({ error }) => {
                if (error) {
                  console.error(`Error upserting addon item ${item.addonitem_name}:`, error);
                }
              });
          })
        )
      );

      // 7. Process item addon group mappings (no change, but can add similar logging if desired)
      console.log("Starting item addon group mappings...");
      const menuItemMappings = await supabaseInstance
        .from("Menu_Item")
        .select("itemid,petpoojaItemId,outletId")
        .eq("outletId", outletId);

      const menuItemMap = new Map((menuItemMappings.data || []).map(mi => [mi.petpoojaItemId, mi.itemid]));

      await Promise.all(
        (menu.items || []).flatMap(item =>
          (item.addon || []).map(addon => {
            return supabaseInstance
              .from("ItemAddonGroups")
              .upsert({
                itemId: menuItemMap.get(item.itemid) || item.itemid,
                groupId: addonGroupMap.get(addon.addon_group_id)?.id || addon.addon_group_id,
                min_selection: Number(addon.addon_item_selection_min),
                max_selection: Number(addon.addon_item_selection_max),
                addongroup_name: addonGroupMap.get(addon.addon_group_id)?.name || "",
              }, { onConflict: ["itemId", "groupId"] })
              .then(({ error }) => {
                if (error) {
                  console.error(`Error upserting ItemAddonGroups for item ${item.itemname}:`, error);
                }
              });
          })
        )
      );
      console.log("Completed addon groups and items processing");
    }

    console.log("Menu sync completed successfully");
    return res.status(200).json({ 
      success: true, 
      message: "Menu synced from Petpooja successfully." 
    });

  } catch (error) {
    console.error("Error syncing menu from Petpooja:", error);
    console.error("Error stack trace:", error.stack);
    return res.status(500).json({ 
      success: false, 
      message: "Error syncing menu from Petpooja.", 
      error: error?.message || error 
    });
  }
});

module.exports = {
  router,
  saveOrderToPetpooja,
  updateOrderStatus,
  fetchMenuCard,
  updateStoreStatus,
};
