let express = require("express");
let router = express.Router();
let supabaseInstance = require("../../services/supabaseClient").supabase;
const {getWeekInfo,getDayNameFromNumber} = require("../../services/dateTimeService");
const moment = require("moment");
/*
RSVP Table

rsvpId	
bigint
number

created_at	
timestamp with time zone
string	

customerAuthUID	
uuid
string	

mealTypeId	
bigint
number	

rsvpDate	
date
string	

response	
boolean
boolean	

menuId	
bigint
number
*/

router.get("/", async (req, res) => {
  return res.send({ success: true, message: "Response from mess/rsvp.js" });
});

// router.post("/", async (req, res) => {
//   const { customerAuthUID, response, menuId } = req.body;
//   if (!customerAuthUID || !menuId) {
//     return res
//       .status(400)
//       .send({ success: false, message: "Missing required fields" });
//   }
//   //get date only
//   let rsvpDate = new Date().toISOString().split("T")[0];

//   try {
//     // check if the user has already RSVP'd for the day for a particular menuId. If yes, update the response else insert a new record
//     const { data: existingRSVP, error: existingRSVPError } =
//       await supabaseInstance
//         .from("RSVP")
//         .select("*")
//         .eq("customerAuthUID", customerAuthUID)
//         .eq("rsvpDate", rsvpDate)
//         .eq("menuId", menuId)
//         .single();

//     if (existingRSVPError) throw existingRSVPError;

//     if (existingRSVP) {
//       let { data, error: updateError } = await supabaseInstance
//         .from("RSVP")
//         .update({ response })
//         .eq("rsvpId", existingRSVP.rsvpId)
//         .select("*")
//         .single();

//       if (updateError) throw updateError;
//       data.isChoice = data.response;
//       data.response = undefined;

//       return res.send({ success: true, data });
//     }

//     let { data, error: rsvpError } = await supabaseInstance
//       .from("RSVP")
//       .insert([
//         {
//           customerAuthUID,
//           rsvpDate,
//           response,
//           menuId,
//         },
//       ])
//       .select("*")
//       .single();

//     if (rsvpError) throw rsvpError;

//     data.isChoice = data.response;
//     data.response = undefined;

//     return res.send({ success: true, data });
//   } catch (error) {
//     console.error("Error:", error); // Log the full error for debugging purposes
//     return res.status(500).json({ success: false, error: error.message });
//   }
// });

router.post("/", async (req, res) => {
  const { customerAuthUID, response, menuId, isTomorrow, outletId } = req.body;
  if (!customerAuthUID || !menuId) {
    return res
      .status(400)
      .send({ success: false, message: "Missing required fields" });
  }

  // Get date only (assuming the rsvpDate is the current date)
  let rsvpDate = !isTomorrow
    ? new Date().toISOString().split("T")[0]
    : new Date(new Date().getTime() + 24 * 60 * 60 * 1000)
        .toISOString()
        .split("T")[0];

  try {
    // Check if the user has already RSVP'd for the day for a particular menuId
    const { data: existingRSVP, error: existingRSVPError } =
      await supabaseInstance
        .from("RSVP")
        .select("*")
        .eq("customerAuthUID", customerAuthUID)
        .eq("rsvpDate", rsvpDate)
        .eq("menuId", menuId)
        .single();

    if (existingRSVPError && existingRSVPError.code !== "PGRST116") {
      // Handling if the error is not a "No data found" error
      throw existingRSVPError;
    }

    if (existingRSVP) {
      // If RSVP exists, update the response
      const { data, error: updateError } = await supabaseInstance
        .from("RSVP")
        .update({ response })
        .eq("rsvpId", existingRSVP.rsvpId)
        .select("*")
        .single();

      if (updateError) throw updateError;
      data.isChoice = data.response;
      data.response = undefined;

      return res.send({ success: true, data });
    }

    // If RSVP does not exist, get the mealTypeId and outletId from Weekly_Menu
    const { data: weeklyMenuData, error: weeklyMenuError } =
      await supabaseInstance
        .from("Weekly_Menu")
        .select("*")
        .eq("menuId", menuId)
        .single();

    if (weeklyMenuError) throw weeklyMenuError;

    mealTypeId = weeklyMenuData.mealTypeId;
    // If RSVP does not exist, insert a new record
    const { data, error: rsvpError } = await supabaseInstance
      .from("RSVP")
      .insert([
        {
          customerAuthUID,
          rsvpDate,
          response,
          menuId,
          mealTypeId,
          outletId,
        },
      ])
      .select("*")
      .single();

    if (rsvpError) throw rsvpError;

    data.isChoice = data.response;
    data.response = undefined;

    return res.send({ success: true, data });
  } catch (error) {
    console.error("Error:", error); // Log the full error for debugging purposes
    return res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/serveMeal", async (req, res) => {
  /*
  Table Name : Meals_Served
  mealServedId : bigint
  customerAuthUID : uuid
  mealTypeId : bigint
  menuId : bigint
  outletId : uuid
  served_date : date
  served_time : timestamp with time zone
*/

  let { customerAuthUID, menuId, outletId } = req.body;
  if (!customerAuthUID || !menuId || !outletId) {
    return res
      .status(400)
      .send({ success: false, message: "Missing required fields" });
  }

  // Get date and timestamp with timezone current
  let served_date = new Date().toISOString().split("T")[0];
  let served_time = new Date().toISOString();

  try {
    // Access the io instance
    const io = req.app.get('io');
    console.log('IO instance exists:', !!io);

    // check if outlet has messId. If yes then allocate messId to outletId
    const { data: outletData, error: outletError } = await supabaseInstance
      .from("Outlet")
      .select("messId")
      .eq("outletId", outletId)
      .single();

      if(outletError) throw outletError;

      if(outletData.messId) {
        outletId = outletData.messId;
      }
    
    // check if the meal to be served is for the current day or not
    const { data: messMenuData, error: messMenuError } = await supabaseInstance
      .from("Weekly_Menu")
      .select("*")
      .eq("menuId", menuId)
      .single();
    
      if (messMenuError) throw messMenuError;
    
    const { weekNumber, dayOfWeek } = getWeekInfo(new Date());
    
    // check for the weekNumber and dayOfWeek
    if (messMenuData.weekNumber !== weekNumber || messMenuData.dayOfWeek !== dayOfWeek) {
      console.log('Emitting meal not served on other day status');
      // emit the socket event for Not served meal
      if (io) {
        io.emitMealStatus(customerAuthUID, {
          success: false,
          message: "No serving other day meal",
          data: null
        });
      } else {
        console.error('IO instance is not available');
      }
      return res.status(200).send({ success: false, message: "No serving other day meal", data: null });
    }


    const { data: existingRSVP, error: existingRSVPError } =
      await supabaseInstance
        .from("Meals_Served")
        .select("*, Customer(customerName, email, photo)")
        .eq("customerAuthUID", customerAuthUID)
        .eq("served_date", served_date)
        .eq("menuId", menuId)
        .eq("outletId", outletId)
        .single();

    if (existingRSVPError && existingRSVPError.code !== "PGRST116") {
      throw existingRSVPError;
    }

    if (existingRSVP) {
      // update the menuId field of the existing record (for emitting realtime event for the user)
      const { error: updateError } = await supabaseInstance
        .from("Meals_Served")
        .update({ menuId })
        .eq("mealServedId", existingRSVP.mealServedId)
        .single();

      if (updateError) throw updateError;

      // Emit socket event for already served meal
      console.log('Emitting meal already served status');
      if (io) {
        io.emitMealStatus(customerAuthUID, {
          success: false,
          message: "Meal already served",
          data: existingRSVP
        });
      } else {
        console.error('IO instance is not available');
      }

      return res.send({
        success: false,
        message: "Meal already served",
        data: existingRSVP,
      });
    }

    // get the mealTypeId from Weekly_Menu
    const { data: weeklyMenuData, error: weeklyMenuError } =
      await supabaseInstance
        .from("Weekly_Menu")
        .select("*")
        .eq("menuId", menuId)
        .single();

    if (weeklyMenuError) throw weeklyMenuError;

    const mealTypeId = weeklyMenuData.mealTypeId;

    // If RSVP does not exist, insert a new record
    const { data, error: rsvpError } = await supabaseInstance
      .from("Meals_Served")
      .insert([
        {
          customerAuthUID,
          served_date,
          served_time,
          mealTypeId,
          menuId,
          outletId,
        },
      ])
      .select("*, Customer(customerName, email, photo)")
      .single();

    if (rsvpError) throw rsvpError;

    // Emit socket event for successful meal serve
    console.log('Emitting successful meal serve status');
    if (io) {
      io.emitMealStatus(customerAuthUID, {
        success: true,
        message: "Meal Served Successfully",
        data
      });
    } else {
      console.error('IO instance is not available');
    }

    return res.send({
      success: true,
      message: "Meal Served Successfully",
      data,
    });
  } catch (error) {
    console.error("Error:", error); // Log the full error for debugging purposes
    return res.status(500).json({ success: false, error: error.message });
  }
});

router.get("/realtimeServe/:outletId", async function (req, res) {
  console.log("realtimeServe");
  const { outletId } = req.params;
  const customerAuthUID = req.query.cuid;

  if(!customerAuthUID || !outletId) {
    return res.status(400).json({ success: false, message: "Missing required fields" });
  }

  res.statusCode = 200;
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader("Cache-Control", "no-cache");
  res.setHeader("connection", "keep-alive");
  res.setHeader("Content-Type", "text/event-stream");

  const channelName = `mealServedChannel-${outletId}-${customerAuthUID}-${new Date().getTime()}`;

  try {
    const channel = supabaseInstance
      .channel(channelName)
      .on(
        "postgres_changes",
        {
          event: "INSERT",
          schema: "public",
          table: "Meals_Served",
          filter: `customerAuthUID=eq.${customerAuthUID}`,
        },
        (payload) => {
          res.write(`data: ${JSON.stringify({ success: true })}\n\n`);
        }
      )
      .on(
        "postgres_changes",
        {
          event: "UPDATE",
          schema: "public",
          table: "Meals_Served",
          filter: `customerAuthUID=eq.${customerAuthUID}`,
        },
        (payload) => {
          res.write(`data: ${JSON.stringify({ success: false })}\n\n`);
        }
      )
      .subscribe();

    channel.on("SUBSCRIBED", () => {
      console.log("Successfully subscribed to channel");
    });

    channel.on("CLOSED", () => {
      console.log("Subscription closed");
      res.end();
    });

    channel.on("ERROR", (error) => {
      console.error("Subscription error:", error);
      res.write(
        `data: ${JSON.stringify({ success: false, error: error.message })}\n\n`
      );
    });

    // Handle client disconnection
    req.on("close", () => {
      console.log("Client disconnected");
      channel.unsubscribe();
    });
  } catch (error) {
    console.error("Error setting up channel:", error);
    return res.status(500).json({ success: false, error: error.message });
  }
});

/* 
2024-10-29
--- Get the menuId for the given mealTypeIds, outletId and date ---

- get mealTypeIds, outletId, date from body
- get messId for the outletId
- get weekNumber and dayOfWeek from getWeekInfo function
- for each mealTypeId, get the menuId from Weekly_Menu
- return the menuId for each mealTypeId
*/
router.post("/getMenuId", async (req, res) => {
  let { mealTypeIds, outletId, date } = req.body;
  
  // Input validation
  if (!mealTypeIds || !Array.isArray(mealTypeIds) || !outletId) {
    return res
      .status(400)
      .send({ success: false, message: "Missing required fields or invalid mealTypeIds format" });
  }

  if (!date) {
    date = new Date().toISOString().split("T")[0];
  }

  try {
    // Get mess ID for the outlet
    const { data: outletData, error: outletError } = await supabaseInstance
      .from("Outlet")
      .select("messId")
      .eq("outletId", outletId)
      .single();

    if (outletError) throw outletError;

    if(!outletData.messId) {
      const {data, error} = await supabaseInstance
        .from("Outlet")
        .select("outletId")
        .eq("outletId", outletId)
        .single();
      if(error) throw error

      outletData.messId = data.outletId;

      if(!data) {
        return res.status(400).send({ success: false, message: "No Mess found with this name." });
      }
    }

    const weekInfo = getWeekInfo(new Date(date));
    const dayNumber = weekInfo.dayOfWeek;
    const weekNumber = weekInfo.weekNumber;

    // Get menu data for all meal types in parallel
    const menuPromises = mealTypeIds.map(async (mealTypeId) => {
      const { data: weeklyMenuData, error: weeklyMenuError } = await supabaseInstance
        .from("Weekly_Menu")
        .select("*")
        .eq("mealTypeId", mealTypeId)
        .eq("outletId", outletData.messId)
        .eq("dayOfWeek", dayNumber)
        .eq("weekNumber", weekNumber)
        .single();

      // Get meal name based on mealTypeId
      const getMealName = (id) => {
        const mealTypes = {
          1: "Breakfast",
          2: "Lunch",
          3: "High Tea",
          4: "Dinner"
        };
        return mealTypes[id] || "Unknown";
      };

      if (weeklyMenuError) {
        if (weeklyMenuError.code === "PGRST116") {
          return {
            mealTypeId,
            name: getMealName(mealTypeId),
            menuId: null,
            message: "No menu found"
          };
        }
        throw weeklyMenuError;
      }

      return {
        mealTypeId,
        name: getMealName(mealTypeId),
        menuId: weeklyMenuData.menuId
      };
    });

    const results = await Promise.all(menuPromises);

    return res.send({
      success: true,
      messId: outletData.messId,
      menus: results
    });

  } catch (error) {
    console.error("Error:", error);
    return res.status(500).json({ success: false, error });
  }
});


/* 
2024-10-29
--- Check if there is a previous mealType order for the given date ---

get cuid, menuId, served_date
check the RSVP table for the record
if the record exists, return message for already rsvpd meal
if the record does not exist, send "No previous Meals Served" message 
*/
router.post("/checkForPreviousMessOrder", async (req, res) => {
  const { customerAuthUID, menuId, served_date } = req.body;

  // Input validation
  if (!customerAuthUID?.trim() || !menuId?.trim() || !served_date?.trim()) {
    return res.status(400).json({
      success: false,
      message: "Missing required fields",
      errors: {
        ...((!customerAuthUID?.trim()) && { customerAuthUID: "Customer ID is required" }),
        ...((!menuId?.trim()) && { menuId: "Menu ID is required" }),
        ...((!served_date?.trim()) && { served_date: "Served date is required" })
      }
    });
  }

  // Validate date format
  const dateObj = new Date(served_date);
  if (dateObj.toString() === 'Invalid Date') {
    return res.status(400).json({
      success: false,
      message: "Invalid date format",
      errors: { served_date: "Please provide a valid date" }
    });
  }

  try {
    // Query Supabase with minimal field selection
    const { data: existingOrder, error: queryError } = await supabaseInstance
      .from("RSVP")
      .select("rsvpId, customerAuthUID, menuId, rsvpDate")
      .eq("customerAuthUID", customerAuthUID)
      .eq("menuId", menuId)
      .eq("rsvpDate", served_date)
      .maybeSingle();

    // Handle database errors
    if (queryError) {
      console.error("Database error:", queryError);
      return res.status(500).json({
        success: false,
        message: "Failed to check meal order",
        error: queryError
      });
    }

    // Return response based on order existence
    return res.status(200).json({
      success: !existingOrder,
      message: existingOrder 
        ? "Meal already RSVP'd for given date"
        : "No previous Meals Served",
      ...(existingOrder && { data: existingOrder })
    });

  } catch (error) {
    console.error("Unexpected error:", error);
    return res.status(500).json({
      success: false,
      message: "An unexpected error occurred",
      error
    });
  }
});


/* 
2024-10-29
---Adding the rsvp of cash and carry orders---

- get cuid, array of mealTypeIds, outletId, date from body
- for each mealTypeId find menuId like in getMenuId
- check the RSVP table for the record
- if the record exists, return message for already rsvpd meal
- if the record does not exist, add the record to the RSVP table
*/
router.post("/cncRSVP", async (req, res) => {
  const { customerAuthUID, mealTypeIds, outletId, date } = req.body;
  console.log(`CNC RSVP for ${customerAuthUID} at ${outletId} on ${date} for mealTypes ${mealTypeIds}`);

  // Input validation
  if (!customerAuthUID || !Array.isArray(mealTypeIds) || !outletId) {
    return res
      .status(400)
      .send({ success: false, message: "Missing required fields or invalid mealTypeIds format" });
  }

  const rsvpDate = date || new Date().toISOString().split("T")[0];

  try {
    // Get mess ID for the outlet
    const { data: outletData, error: outletError } = await supabaseInstance
      .from("Outlet")
      .select("messId")
      .eq("outletId", outletId)
      .single();

    if (outletError) {
      if(outletError.code === "PGRST116") {
        return res.status(400).send({ success: false, message: "No Mess associated with this outlet." });
      }
      throw outletError
    };

    if(!outletData.messId) {
      const {data, error} = await supabaseInstance
        .from("Outlet")
        .select("outletId")
        .eq("outletId", outletId)
        .single();
      if(error) throw error

      outletData.messId = data.outletId;

      if(!data) {
        return res.status(400).send({ success: false, message: "No Mess found with this name." });
      }
    }
    const weekInfo = getWeekInfo(new Date(rsvpDate));
    const dayNumber = weekInfo.dayOfWeek;
    const weekNumber = weekInfo.weekNumber;

    // Process each mealTypeId
    const rsvpPromises = mealTypeIds.map(async (mealTypeId) => {
      // First get the menuId for this mealType
      const { data: weeklyMenuData, error: weeklyMenuError } = await supabaseInstance
        .from("Weekly_Menu")
        .select("*")
        .eq("mealTypeId", mealTypeId)
        .eq("outletId", outletData.messId)
        .eq("dayOfWeek", dayNumber)
        .eq("weekNumber", weekNumber)
        .single();

      if (weeklyMenuError) {
        if (weeklyMenuError.code === "PGRST116") {
          return {
            mealTypeId,
            success: false,
            message: "No menu found for this meal type"
          };
        }
        throw weeklyMenuError;
      }

      const menuId = weeklyMenuData.menuId;

      // Check if RSVP already exists for this menu and date
      const { data: existingRSVP, error: existingRSVPError } = await supabaseInstance
        .from("RSVP")
        .select("*")
        .eq("customerAuthUID", customerAuthUID)
        .eq("rsvpDate", rsvpDate)
        .eq("menuId", menuId)
        .single();

      if (existingRSVPError && existingRSVPError.code !== "PGRST116") {
        throw existingRSVPError;
      }

      let rsvpResult;

      if (existingRSVP) {
        // Update existing RSVP
        const { data, error: updateError } = await supabaseInstance
          .from("RSVP")
          .update({ 
            response: true, 
            isCNC: true 
          })
          .eq("rsvpId", existingRSVP.rsvpId)
          .select("*")
          .single();

        if (updateError) throw updateError;
        console.log(`RSVP updated: ${existingRSVP.rsvpId} for ${customerAuthUID} for ${menuId}`);
        rsvpResult = data;
      } else {
        // Create new RSVP
        const { data, error: insertError } = await supabaseInstance
          .from("RSVP")
          .insert([
            {
              customerAuthUID,
              rsvpDate,
              response: true, 
              menuId,
              mealTypeId,
              outletId : outletData.messId,
              isCNC: true
            },
          ])
          .select("*")
          .single();

        if (insertError) throw insertError;
        console.log(`RSVP created: ${data.rsvpId} for ${customerAuthUID} for ${menuId}`);
        rsvpResult = data;
      }

      rsvpResult.isChoice = rsvpResult.response;
      rsvpResult.response = undefined;

      return {
        mealTypeId,
        success: true,
        data: rsvpResult
      };
    });

    const results = await Promise.all(rsvpPromises);

    return res.send({
      success: true,
      rsvps: results
    });

  } catch (error) {
    console.error("Error:", error);
    return res.status(500).json({ success: false, error });
  }
});

/**
 * Check for pending feedback for previous meals from 25 Decemeber 2024
 * - Check the Meal_Served table for meals served previously
 * - Check if feedback is already given for the served meals
 * - Return the meals for which feedback is pending
 */
router.post("/checkPendingFeedback", async (req, res) => {
  return res.status(200).json({
    success: true,
    feedbackcompulsory: false,
    isPendingFeedback: false,
    reviewcompulsory: false,
  });
  
  
  // const { customerAuthUID } = req.body;

// if (!customerAuthUID) {
//   return res.status(400).json({ success: false, message: "Missing required fields" });
// }

  // try {
  //   // Get the outlet's feedback requirements
  //   const { data: outletData, error: outletError } = await supabaseInstance
  //     .from("Outlet")
  //     .select("hasMess, feedbackCompulsory, reviewCompulsory")
  //     .eq("outletId", req.body.outletId)
  //     .single();

  //   if (outletError) {
  //     throw outletError;
  //   }
  //   if(outletData.hasMess)
  //   {

  //   // If feedback is compulsory, check if review is compulsory
  //   const reviewCompulsory = outletData.reviewCompulsory;

  //   // Get the meals served for the customer after December 25, 2024
  //   const { data: servedMeals, error: servedMealsError } = await supabaseInstance
  //     .from("Meals_Served")
  //     .select("*")
  //     .eq("customerAuthUID", customerAuthUID)
  //     .eq("outletId", req.body.outletId) 
  //     .gte("served_date", "2024-12-16")
  //     .order("served_date", { ascending: false });

  //   if (servedMealsError) {
  //     throw servedMealsError;
  //   }

  //   if(servedMeals.length === 0) {
  //     return res.status(200).json({ 
  //       success: true, 
  //       feedbackCompulsory: outletData.feedbackCompulsory, 
  //       isPendingFeedback: false, 
  //       reviewCompulsory: outletData.reviewCompulsory 
  //     });
  //   }

  //   // Check if feedback has been given for these meals
  //   const feedbackGiven = await supabaseInstance
  //     .from("Mess_Meal_Review")
  //     .select("menuId, reviewDate, mealServedId, Meals_Served(*)")
  //     .eq("customerAuthUID", customerAuthUID);

  //   const pendingFeedback = servedMeals.filter(meal => {
  //     return !feedbackGiven.data.some(feedback => feedback?.Meals_Served?.menuId === meal?.menuId);
  //   });

  //   // Add mealName to pendingFeedback based on mealTypeId
  //   pendingFeedback.forEach(meal => {
  //     switch (meal.mealTypeId) {
  //       case 1:
  //         meal.mealName = "Breakfast";
  //         break;
  //       case 2:
  //         meal.mealName = "Lunch";
  //         break;
  //       case 3:
  //         meal.mealName = "High Tea";
  //         break;
  //       case 4:
  //         meal.mealName = "Dinner";
  //         break;
  //       default:
  //         meal.mealName = "Unknown";
  //     }
  //   });

  //   return res.status(200).json({
  //     success: true,
  //     feedbackCompulsory: outletData.feedbackCompulsory,
  //     reviewCompulsory,
  //     isPendingFeedback: pendingFeedback.length > 0,
  //     pendingFeedback: pendingFeedback[0],
  //   });}
  //   return res.status(200).json({ success: true, feedbackCompulsory: false, isPendingFeedback: false });
  // } catch (error) {
  //   console.error("Error:", error);
  //   return res.status(500).json({ success: false, error });
  // }
});

router.post("/giveFeedback", async (req, res) => {
  const { customerAuthUID, rating, review, menuId, outletId, mealServedId, isSkipped } = req.body;

  if (!customerAuthUID || !rating || !menuId) {
    return res.status(400).json({ success: false, message: "Missing required fields" });
  }

  try {
    // Get feedback requirements for the outlet
    const { data: outletData, error: outletError } = await supabaseInstance
      .from("Outlet")
      .select("feedbackCompulsory, reviewCompulsory")
      .eq("outletId", outletId)
      .single();

    if (outletError) throw outletError;

    // Validate feedback based on outlet requirements
    if (outletData.feedbackCompulsory) {
      if (outletData.reviewCompulsory && !review) {
        return res.status(400).json({ success: false, message: "Review is required." });
      }
      if (rating < 1 || rating > 5) {
        return res.status(400).json({ success: false, message: "Rating must be between 1 and 5." });
      }
    }

    const date = new Date().toISOString().split("T")[0];

    // Check if review is already given for this menu and date
    const { data: existingReview, error: existingReviewError } = await supabaseInstance
      .from("Mess_Meal_Review")
      .select("*")
      .eq("customerAuthUID", customerAuthUID)
      .eq("mealServedId", mealServedId)
      .maybeSingle();

    if (existingReviewError && existingReviewError.code !== "PGRST116") {
      throw existingReviewError;
    }

    if (existingReview) {
      return res.status(400).json({ success: false, message: "Review already given for this menu and date" });
    }

    const { data, error } = await supabaseInstance
      .from("Mess_Meal_Review")
      .insert([
        { customerAuthUID, rating, review, menuId, reviewDate: date, outletId, mealServedId, isSkipped }
      ])
      .select("*")
      .single();

    if (error) throw error;

    return res.status(200).json({ success: true, data });
  } catch (error) {
    console.error("Error:", error);
    return res.status(500).json({ success: false, error });
  }
});

/**
 * - get from the body, for which mealTypeId, outletId the dates are to be fetched
 * - get the timings and dates from the Weekly_Menu table
 * - check for already served meals for the customer and make disabled flag as we have in cafeteriaDetails
 * - return the dates and timings
 */
router.post("/getAvailableScheduleableDates", async (req, res) => { 
  const { mealTypeId, outletId, customerAuthUID } = req.body;

  if (!mealTypeId || !outletId || !customerAuthUID) {
    return res.status(400).json({ success: false, message: "Missing required fields" });
  }

  try {
    const { data: weeklyMenuData, error: weeklyMenuError } = await supabaseInstance
      .from("Weekly_Menu")
      .select("menuId, mealTypeId, outletId, dayOfWeek, weekNumber, price")
      .eq("mealTypeId", mealTypeId)
      .eq("outletId", outletId);

    if (weeklyMenuError) {
      console.error("Error fetching weekly menu data:", weeklyMenuError);
      return res.status(500).json({ success: false, error: weeklyMenuError });
    }

    // get the meal timings 
    const { data: mealTimings, error: mealTimingsError } = await supabaseInstance
      .from("Meal_Timings")
      .select("mealTypeId, outletId, startTime, endTime")
      .eq("mealTypeId", mealTypeId)
      .eq("outletId", outletId);
    
    if (mealTimingsError) {
      console.error("Error fetching meal timings:", mealTimingsError);
      return res.status(500).json({ success: false, error: mealTimingsError });
    }


    // get the rsvp for the next 7 days including today
    const { data: rsvpData, error: rsvpError } = await supabaseInstance
      .from("RSVP")
      .select("rsvpId, mealTypeId, customerAuthUID, menuId, rsvpDate")
      .eq("customerAuthUID", customerAuthUID)
      .eq("mealTypeId", mealTypeId)
      .eq("outletId", outletId)
      .gte("rsvpDate", new Date().toISOString().split("T")[0])
      .lte("rsvpDate", new Date(new Date().setDate(new Date().getDate() + 7)).toISOString().split("T")[0]);
    
    if (rsvpError) {
      console.error("Error fetching RSVP data:", rsvpError);
      return res.status(500).json({ success: false, error: rsvpError });
    }

    // Get the menu of next 7 days including today
    const scheduleableDates = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date();
      date.setDate(date.getDate() + i);
      const {dayOfWeek, weekNumber} = getWeekInfo(date);
      const menuItem = weeklyMenuData.find(item => item.dayOfWeek === dayOfWeek && item.weekNumber === weekNumber);
      const mealTiming = mealTimings.find(timing => timing.mealTypeId === mealTypeId);
      
      const alreadyPurchased = rsvpData.find(item => item.rsvpDate.split("T")[0] === date.toISOString().split("T")[0] && item.menuId === menuItem.menuId);

      // Check for timesUp only for today
      let timesUp = false;
      if (i === 0) { // Check for today
        const mealEndTime = new Date(mealTiming.endTime);

        // Extract hours, minutes, seconds from both dates
        const mealEndHours = mealEndTime.getUTCHours();
        const mealEndMinutes = mealEndTime.getUTCMinutes();
        const mealEndSeconds = mealEndTime.getUTCSeconds();

        const currentHours = date.getUTCHours();
        const currentMinutes = date.getUTCMinutes();
        const currentSeconds = date.getUTCSeconds();

        // Compare time parts only
        timesUp =
          (currentHours > mealEndHours) ||
          (currentHours === mealEndHours && currentMinutes > mealEndMinutes) ||
          (currentHours === mealEndHours && currentMinutes === mealEndMinutes && currentSeconds > mealEndSeconds);
      }

      if (menuItem) {
        scheduleableDates.push({
          formattedDate: moment(date).format("MMMM D, YYYY"),
          orderDate: moment(date).format("DD-MM-YYYY"),
          dayOfWeek,
          dayName: getDayNameFromNumber(dayOfWeek),
          weekNumber,
          mealTypeId,
          outletId,
          menuId: menuItem.menuId,
          price: menuItem.price,
          startTime: mealTiming.startTime,
          endTime: mealTiming.endTime,
          orderTime: moment(mealTiming.startTime).utcOffset("+05:30").format("hh:mm A"), //11:16 am
          alreadyPurchased: alreadyPurchased ? true : false,
          timesUp: timesUp,
        });
      }
    }

    console.log()
    return res.status(200).json({ success: true, data: scheduleableDates });
  } catch (error) {
    console.error("Error:", error);
    return res.status(500).json({ success: false, error });
  }
});

router.get("/getMessReadyOrders/:outletId", async (req, res) => {
  const { outletId } = req.params;
  const { fromDate, toDate } = req.query;
  const { page=1, perPage=20 } = req.query;

  try {
    let query = supabaseInstance
      .rpc("get_orders_for_outlet", { outlet_uuid: outletId })
      .eq("order_status_id", 5)
      .order("order_schedule_date", { ascending: false })
      .order("order_schedule_time", { ascending: false });

    if (fromDate) {
      const from = new Date(fromDate);
      query = query.gte("order_schedule_date", from.toISOString().split("T")[0]);
    }

    if(toDate) {
      const to = new Date(toDate);
      query = query.lte("order_schedule_date", to.toISOString().split("T")[0]);
    }
    
    // Pagination
    const offset = (page - 1) * perPage;
    query = query.range(offset, offset + perPage - 1);

    let { data, error } = await query;

    if (data) {
      // add the transaction details to the response. fetch from Transaction Table. get txnid from order table using order_id
      const response = await Promise.all(data.map(async (order) => {
        const { data: transactionData, error: transactionError } = await supabaseInstance
          .from("Order")
          .select("txnid")
          .eq("orderId", order.order_id)
          .single();

        if (transactionError) {
          console.log(`Error fetching transaction data for order ${order.order_id}: ${JSON.stringify(transactionError, null, 2)}`);
        };

        if (transactionData) {
          return {
            ...order,
            txnid: transactionData.txnid
          }
        }
      }));

      res.status(200).json({
        success: true,
        data: response,
      });
    } else {
      throw error;
    }
  } catch (error) {
    console.log(error);
    res.status(500).json({ success: false, error: error });
  }
});


module.exports = router;
