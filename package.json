{"name": "mealpe-backend", "version": "0.0.0", "private": true, "scripts": {"start": "nodemon ./bin/www"}, "dependencies": {"@supabase/supabase-js": "^2.26.0", "axios": "^1.4.0", "cookie-parser": "~1.4.4", "cors": "^2.8.5", "crypto-js": "^4.1.1", "debug": "~2.6.9", "dotenv": "^16.4.5", "express": "~4.16.1", "firebase-admin": "^12.4.0", "http-errors": "~1.6.3", "jade": "~1.11.0", "moment-timezone": "^0.5.43", "morgan": "~1.9.1", "multer": "^1.4.5-lts.1", "node-schedule": "^2.1.1", "nodemailer": "^6.9.15", "nodemon": "^2.0.22", "razorpay": "^2.9.6", "sha256": "^0.2.0", "socket.io": "^4.8.1", "uniqid": "^5.4.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "ws": "^8.14.0"}}