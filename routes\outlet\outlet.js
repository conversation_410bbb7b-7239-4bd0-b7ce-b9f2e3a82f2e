var express = require("express");
var router = express.Router();
var supabaseInstance = require("../../services/supabaseClient").supabase;
var { outletSelectString } =
  require("../../services/supabaseCommonValues").value;
const multer = require("multer");
const upload = multer();
const axios = require("axios");
var msg91config = require("../../configs/msg91Config");
const { sendMobileSMS, sendEmail } = require("../../services/msf91Service");
var { outletSelectString } =
  require("../../services/supabaseCommonValues").value;
const moment = require("moment-timezone");
const { updateStoreStatus } = require("../petpooja/pushMenu");
const { supabase } = require("../../services/supabaseClient");
const sendOutletNotifications = require("../../routes/firebase").sendOutletNotifications;
const fetchFCMTokens = require("../../routes/firebase").fetchFCMTokens;
// const { updateStoreStatus } = require("../petpooja/pushMenu");
const {
  sendMobileOtp,
  verifyMobileOtp,
} = require("../../services/msf91Service");

router.post("/createOutlet", async (req, res) => {
  const {
    outletName,
    email,
    isPrimaryOutlet,
    primaryOutletId,
    password,
    bankDetailsId,
    outletAdminId,
    cityId,
    restaurantName,
    mobile,
    GSTIN,
    campusId,
    address,
    openTime,
    closeTime,
    Restaurant_category,
    Timing,
    isDelivery,
    isDineIn,
    isPickUp,
    isGSTShow,
    logo,
    FSSAI_number,
    convenienceFee,
    commissionFee,
    bankLabel,
    isVeg,
    isNonVeg,
    deliveryCharge,
  } = req.body;

  let { hasMess } = req.body;
  // convert string to boolean
  hasMess = hasMess === true;

  try {
    const { data, error } = await supabaseInstance.auth.signUp({
      email: email,
      password: password,
      options: {
        data: {
          isRestaurant: false,
          isOutlet: true,
        },
      },
    });
    if (data?.user) {
      const outletId = data.user.id;

      const bankDetails = await supabaseInstance
        .from("BankDetails")
        .insert({
          accountNumber: bankDetailsId?.accountNumber || null,
          BankName: bankDetailsId?.BankName || null,
          IFSCCode: bankDetailsId?.IFSCCode || null,
          bankId: bankDetailsId?.bankId || null,
        })
        .select()
        .maybeSingle();
      const _bankDetailsId = bankDetails.data.bankDetailsId;

      const outletDetails = await supabaseInstance
        .from("Outlet_Admin")
        .insert({
          name: outletAdminId?.name || null,
          mobile: outletAdminId?.mobile || null,
          email: outletAdminId?.email || null,
          address: outletAdminId?.address || null,
          pancard: outletAdminId?.pancard || null,
        })
        .select()
        .maybeSingle();
      const _outletAdminId = outletDetails.data.outletAdminId;

      let postObject = {
        outletId,
        outletName,
        restaurantName,
        email,
        mobile,
        GSTIN,
        bankDetailsId: _bankDetailsId,
        outletAdminId: _outletAdminId,
        campusId,
        address,
        cityId,
        isPrimaryOutlet,
        primaryOutletId,
        isGSTShow,
        isVeg,
        isNonVeg,
        hasMess,
      };
      if (openTime) {
        postObject.openTime = openTime;
      }
      if (closeTime) {
        postObject.closeTime = closeTime;
      }
      if (isDelivery) {
        postObject.isDelivery = true;
      }
      if (isDineIn) {
        postObject.isDineIn = true;
      }
      if (isPickUp) {
        postObject.isPickUp = true;
      }
      if (logo) {
        postObject.logo = logo;
      }

      if (FSSAI_number) {
        postObject.FSSAI_number = FSSAI_number;
      }

      if (convenienceFee) {
        postObject.convenienceFee = convenienceFee;
      }

      if (commissionFee) {
        postObject.commissionFee = commissionFee;
      }

      if (bankLabel) {
        postObject.bankLabel = bankLabel;
      }

      if (deliveryCharge) {
        postObject.deliveryCharge = deliveryCharge;
      }

      if (!isPrimaryOutlet) {
        postObject.primaryOutletId = primaryOutletId;
      } else {
        postObject.primaryOutletId = null;
      }
      const inserRestaurentNewkDetails = await supabaseInstance
        .from("Outlet")
        .insert(postObject)
        .select("*")
        .maybeSingle();

      const taxPostBody = [
        { outletId, taxname: "CGST" },
        { outletId, taxname: "SGST" },
      ];
      const taxResponse = await supabaseInstance
        .from("Tax")
        .insert(taxPostBody)
        .select();

      const outletRole = await supabaseInstance
        .from("Outlet_Role")
        .insert({
          role: "Order Management",
          outletId: outletId,
          access: [
            "Dashboard",
            "Orders",
            "Menu",
            "Customer",
            "Outlets",
            "KOT",
            "Settings",
          ],
        })
        .select("*");

      console.log("outletRole -> ", outletRole);

      for (let outletItem of Restaurant_category) {
        const outletCategoryResponse = await supabaseInstance
          .from("Restaurant_category")
          .insert({ outletId, categoryId: outletItem })
          .select("*")
          .maybeSingle();
      }

      for (let data of Timing) {
        const outletTimeResponse = await supabaseInstance
          .from("Timing")
          .insert({
            outletId,
            dayId: data.dayId,
            openTime: data.openTime,
            closeTime: data.closeTime,
          })
          .select("*")
          .maybeSingle();
      }

      if (inserRestaurentNewkDetails.data) {
        res.send({
          success: true,
          message: "Outlet created successfully",
          data: inserRestaurentNewkDetails.data,
        });
      } else {
        throw inserRestaurentNewkDetails.error;
      }
    } else {
      throw error;
    }
  } catch (error) {
    console.log(error);
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post(
  "/upsertFssaiLicensePhoto",
  upload.single("file"),
  async (req, res) => {
    const { outletId } = req.body;
    // console.log("outletId--->", outletId)

    try {
      let query = supabaseInstance.storage.from("fssai-license");

      if (req?.file?.mimetype.includes("image")) {
        query = query.upload(outletId + ".webp", req.file.buffer, {
          cacheControl: "3600",
          upsert: true,
          contentType: "image/webp",
        });
      } else if (req?.file?.mimetype === "application/pdf") {
        query = query.upload(outletId + ".pdf", req.file.buffer, {
          cacheControl: "3600",
          upsert: true,
          contentType: "application/pdf",
        });
      } else {
        throw req?.file?.mimetype?.error;
      }

      const { data, error } = await query;

      if (data?.path) {
        const publickUrlresponse = await supabaseInstance.storage
          .from("fssai-license")
          .getPublicUrl(data?.path);
        if (publickUrlresponse?.data?.publicUrl) {
          const publicUrl = publickUrlresponse?.data?.publicUrl;
          const outletData = await supabaseInstance
            .from("Outlet")
            .update({ FSSAI_License: `${publicUrl}?${new Date().getTime()}` })
            .eq("outletId", outletId)
            .select(outletSelectString)
            .maybeSingle();
          res.status(200).json({
            success: true,
            data: outletData.data,
          });
        } else {
          throw publickUrlresponse.error || "Getting Error in PublicUrl";
        }
      } else {
        throw error;
      }
    } catch (error) {
      res.status(500).json({ success: false, error: error });
    }
  }
);

router.get("/getPrimaryOutletList", async (req, res) => {
  const { page, perPage, searchText, searchCity, sortBy } = req.query;
  const pageNumber = parseInt(page) || 1;
  const itemsPerPage = parseInt(perPage) || 10;
  try {
    let query = supabaseInstance
      .from("Outlet")
      .select(
        "*, restaurantId(*), campusId(*),outletAdminId(*), bankDetailsId (*),cityId(*)), Tax!left(taxid, taxname, tax)",
        { count: "exact" }
      )
      .range((pageNumber - 1) * itemsPerPage, pageNumber * itemsPerPage - 1)
      // .order("outletName", { ascending: true })
      .eq("isPrimaryOutlet", true);

    if (searchText) {
      query = query.or(
        `address.ilike.%${searchText}%,outletName.ilike.%${searchText}%`
      );
    }

    if (searchCity) {
      query = query.eq("cityId", searchCity);
    }

    if (sortBy === "name") {
      query = query.order("outletName", { ascending: true });
    } else if (sortBy === "date") {
      query = query.order("created_at", { ascending: false });
    }

    const { data, error, count } = await query;

    if (data) {
      const totalPages = Math.ceil(count / itemsPerPage);
      res.status(200).json({
        success: true,
        data,
        meta: {
          page: pageNumber,
          perPage: itemsPerPage,
          totalPages,
          totalCount: count,
        },
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

router.get("/getOutletListByRestaurantId/:outletId", async (req, res) => {
  const { outletId } = req.params;
  const { page, perPage, searchText } = req.query;
  const pageNumber = parseInt(page) || 1;
  const itemsPerPage = parseInt(perPage) || 10;
  try {
    let query = supabaseInstance
      .from("Outlet")
      .select(
        "*, restaurantId(*), campusId(*),outletAdminId(*), bankDetailsId (*),cityId(*),Timing(*)), Tax!left(taxid, taxname, tax)",
        { count: "exact" }
      )
      .range((pageNumber - 1) * itemsPerPage, pageNumber * itemsPerPage - 1)
      .order("outletName", { ascending: true })
      .eq("outletId", outletId);
    if (searchText) {
      query = query.or(
        `address.ilike.%${searchText}%,outletName.ilike.%${searchText}%`
      );
      // query = query.ilike('outletName', `%${searchText}%`);
    }
    const { data, error, count } = await query;

    if (data) {
      const totalPages = Math.ceil(count / itemsPerPage);
      res.status(200).json({
        success: true,
        data,
        meta: {
          page: pageNumber,
          perPage: itemsPerPage,
          totalPages,
          totalCount: count,
        },
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

router.get(
  "/getOutletListByPrimaryOutletId/:primaryOutletId",
  async (req, res) => {
    const { primaryOutletId } = req.params;
    const { page, perPage, searchText } = req.query;
    const pageNumber = parseInt(page) || 1;
    const itemsPerPage = parseInt(perPage) || 10;
    try {
      let query = supabaseInstance
        .from("Outlet")
        .select(
          "*, primaryOutletId(*),restaurantId(*), campusId(*),outletAdminId(*), bankDetailsId (*),cityId(*)), Tax!left(taxid, taxname, tax)",
          { count: "exact" }
        )
        .range((pageNumber - 1) * itemsPerPage, pageNumber * itemsPerPage - 1)
        .order("outletName", { ascending: true })
        .eq("primaryOutletId", primaryOutletId);
      if (searchText) {
        query = query.or(
          `address.ilike.%${searchText}%,outletName.ilike.%${searchText}%`
        );
        // query = query.ilike('outletName', `%${searchText}%`);
      }
      const { data, error, count } = await query;

      if (data) {
        const totalPages = Math.ceil(count / itemsPerPage);
        res.status(200).json({
          success: true,
          data,
          meta: {
            page: pageNumber,
            perPage: itemsPerPage,
            totalPages,
            totalCount: count,
          },
        });
      } else {
        throw error;
      }
    } catch (error) {
      res.status(500).json({ success: false, error: error.message });
    }
  }
);

router.post("/updateOutlet/:outletId", async (req, res) => {
  const { outletId } = req.params;
  const outletData = req.body;
  const bankDetailsData = outletData?.bankDetailsId;
  const outletAdminId = outletData?.outletAdminId;
  const timeDetailsData = outletData?.Timing;
  const categoryDetailsData = outletData?.Restaurant_category;
  delete outletAdminId?.email;
  delete outletData?.bankDetailsId;
  delete outletData?.outletAdminId;
  delete outletData?.Restaurant_category;
  delete outletData?.Timing;
  delete outletData?.isBoth;
  delete outletData?.isBothFood;
  delete outletData?.password;

  try {
    if (bankDetailsData) {
      const bankDetails = await supabaseInstance
        .from("BankDetails")
        .update({
          accountNumber: bankDetailsData?.accountNumber || null,
          BankName: bankDetailsData?.BankName,
          IFSCCode: bankDetailsData?.IFSCCode,
          bankId: bankDetailsData?.bankId || null,
        })
        .eq("bankDetailsId", bankDetailsData.bankDetailsId)
        .select("*");
    }

    if (categoryDetailsData?.length > 0) {
      const categoryDataDelete = await supabaseInstance
        .from("Restaurant_category")
        .delete()
        .eq("outletId", outletId);
      for (let category of categoryDetailsData) {
        const categoryData = await supabaseInstance
          .from("Restaurant_category")
          .insert({ categoryId: category, outletId })
          .select("*");
      }
    }

    // if (timeDetailsData?.length > 0) {
    //   const timingDataDelete = await supabaseInstance.from("Timing").delete().eq("outletId",outletId);
    //   for(let data of timeDetailsData){
    //     const timingData = await supabaseInstance.from("Timing").insert({outletId, dayId: data.dayId, openTime: data.openTime, closeTime: data.closeTime }).select("*");
    //   }
    // }
    if (timeDetailsData?.length > 0) {
      for (let data of timeDetailsData) {
        const timingData = await supabaseInstance
          .from("Timing")
          .update({ openTime: data.openTime, closeTime: data.closeTime })
          .eq("timeId", data.timeId)
          .select("*");
      }
    }

    if (outletAdminId) {
      const outletAdminDetails = await supabaseInstance
        .from("Outlet_Admin")
        .update({ ...outletAdminId })
        .eq("outletAdminId", outletAdminId.outletAdminId)
        .select("*");
    }

    if (req?.body?.convenienceFee) {
      outletData.convenienceFee = req?.body?.convenienceFee;
    } else {
      delete outletData.convenienceFee;
    }

    if (req?.body?.commissionFee) {
      outletData.commissionFee = req?.body?.commissionFee;
    } else {
      delete outletData.commissionFee;
    }

    if (req?.body?.packaging_charge) {
      outletData.packaging_charge = req?.body?.packaging_charge;
    } else {
      delete outletData.packaging_charge;
    }

    if (req?.body?.FSSAI_number) {
      outletData.FSSAI_number = req?.body?.FSSAI_number;
    } else {
      delete outletData.FSSAI_number;
    }

    if (req?.body?.bankLabel) {
      outletData.bankLabel = req?.body?.bankLabel;
    } else {
      delete outletData.bankLabel;
    }

    if(req?.body?.mfaEnabled){
      outletData.mfaEnabled = req?.body?.mfaEnabled;
    }

    if(req?.body?.canInviteUser){
      outletData.canInviteUser = req?.body?.canInviteUser;
    }

    if(req?.body?.cashAndCarry){
      outletData.cashAndCarry = req?.body?.cashAndCarry;
    }

    if(req?.body?.messId){
      outletData.messId = req?.body?.messId;
    }

    if(req?.body?.hasMess){
      outletData.hasMess = req.body.hasMess;
    }
    const { data, error } = await supabaseInstance
      .from("Outlet")
      .update({ ...outletData })
      .eq("outletId", outletId)
      .select(
        "*,bankDetailsId(*),outletAdminId(*),Timing!left(*),Restaurant_category!left(categoryId)"
      );

    let childOutletBody = {};

    if (outletData.convenienceFee) {
      childOutletBody.convenienceFee = outletData.convenienceFee;
    }
    if (outletData.commissionFee) {
      childOutletBody.commissionFee = outletData.commissionFee;
    }
    if (Object.keys(childOutletBody).length > 0) {
      const updateChildOutlet = await supabaseInstance
        .from("Outlet")
        .update(childOutletBody)
        .eq("primaryOutletId", outletId);
      // console.log("updateChildOutlet => ", updateChildOutlet);
    }

    if (data) {
      console.log("data-->", data);
      res.status(200).json({
        success: true,
        message: "Data updated succesfully",
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/updatePackagingCharge/:outletId", async (req, res) => {
  const { outletId } = req.params;
  const { packaging_charge } = req.body;

  try {
    const { data, error } = await supabaseInstance
      .from("Outlet")
      .update({ packaging_charge })
      .eq("outletId", outletId)
      .select("*");

    if (data) {
      console.log("data-->", data);
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/publishOutlet/:outletId", async (req, res) => {
  const { outletId } = req.params;
  try {
    const { data, error } = await supabaseInstance
      .from("Outlet")
      .update({ isPublished: true, publishProcessingStep: 3 })
      .eq("outletId", outletId)
      .select("*")
      .maybeSingle();

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/updateTaxCharge", async (req, res) => {
  const { tax } = req.body;
  try {
    for (let data of tax) {
      taxData = await supabaseInstance
        .from("Tax")
        .update({ tax: data.tax })
        .select("*")
        .eq("taxid", data.taxid);
    }
    if (taxData) {
      res.status(200).json({
        success: true,
        message: "Data updated succesfully",
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/updatePetPooja/:outletId", async (req, res) => {
  const { outletId } = req.params;
  const {
    petPoojaAppKey,
    petPoojaAppSecret,
    petPoojaApAccessToken,
    petPoojaRestId,
    publishProcessingStep,
    isOrderHandlingFromPetpooja,
    isOrderDropInPetpooja,
  } = req.body;

  try {
    const postbody = {
      petPoojaAppKey,
      petPoojaAppSecret,
      petPoojaApAccessToken,
      petPoojaRestId,
      isOrderHandlingFromPetpooja,
      isOrderDropInPetpooja,
    };
    if (publishProcessingStep) {
      postbody.publishProcessingStep = publishProcessingStep;
    }
    const { data, error } = await supabaseInstance
      .from("Outlet")
      .update(postbody)
      .select(outletSelectString)
      .eq("outletId", outletId);

    if (data) {
      res.status(200).json({
        success: true,
        message: "Data updated succesfully",
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    console.log(error);
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/upsertLogoImage", upload.single("file"), async (req, res) => {
  const { outletId } = req.body;
  try {
    const { data, error } = await supabaseInstance.storage
      .from("logo-images")
      .upload(outletId + ".webp", req.file.buffer, {
        cacheControl: "3600",
        upsert: true,
        contentType: "image/webp",
      });

    if (data?.path) {
      const publickUrlresponse = await supabaseInstance.storage
        .from("logo-images")
        .getPublicUrl(data?.path);
      if (publickUrlresponse?.data?.publicUrl) {
        const publicUrl = publickUrlresponse?.data?.publicUrl;
        const outletData = await supabaseInstance
          .from("Outlet")
          .update({ logo: `${publicUrl}?${new Date().getTime()}` })
          .eq("outletId", outletId)
          .select(outletSelectString)
          .maybeSingle();

        if (outletData?.isPrimaryOutlet === true) {
          await supabaseInstance
            .from("Outlet")
            .update({ logo: `${publicUrl}?${new Date().getTime()}` })
            .eq("primaryOutletId", outletData.primaryOutletId)
            .select("*")
            .maybeSingle();
        }

        res.status(200).json({
          success: true,
          data: outletData.data,
        });
      } else {
        throw publickUrlresponse.error || "Getting Error in PublicUrl";
      }
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/upsertHeaderImage", upload.single("file"), async (req, res) => {
  const { outletId } = req.body;
  try {
    const { data, error } = await supabaseInstance.storage
      .from("header-images")
      .upload(outletId + ".webp", req.file.buffer, {
        cacheControl: "3600",
        upsert: true,
        contentType: "image/webp",
      });

    if (data?.path) {
      const publickUrlresponse = await supabaseInstance.storage
        .from("header-images")
        .getPublicUrl(data?.path);
      if (publickUrlresponse?.data?.publicUrl) {
        const publicUrl = publickUrlresponse?.data?.publicUrl;
        const outletData = await supabaseInstance
          .from("Outlet")
          .update({ headerImage: `${publicUrl}?${new Date().getTime()}` })
          .eq("outletId", outletId)
          .select(outletSelectString)
          .maybeSingle();
        res.status(200).json({
          success: true,
          data: outletData.data,
        });
      } else {
        throw publickUrlresponse.error || "Getting Error in PublicUrl";
      }
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
});

router.get("/getOutletData/:outletId", async (req, res) => {
  const { outletId } = req.params;
  try {
    const { data, error } = await supabaseInstance
      .from("Outlet")
      .select(outletSelectString)
      .eq("outletId", outletId)
      .maybeSingle();
    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error });
  }
});

router.post("/pushMenuData/:outletId", async (req, res) => {
  const { outletId } = req.params;
  const { category, subCategory, item } = req.body;

  let serverCategory = [];
  let serverSubCategory = [];
  let serverItem = [];

  try {
    if (category?.length > 0) {
      for (const categoryItem of category) {
        let postData = {
          parentCategoryName: categoryItem?.parentCategoryName,
          outletId,
          status: categoryItem?.status,
          parent_category_image_url:
            categoryItem?.parent_category_image_url || null,
          isFromPrimaryOutletId: true,
        };

        const categoryAddResponse = await supabaseInstance
          .from("Menu_Parent_Categories")
          .insert({ ...postData })
          .select("*")
          .maybeSingle();
        if (categoryAddResponse.data) {
          categoryItem.serverResponse = categoryAddResponse.data;
          serverCategory.push(categoryAddResponse.data);
        }
      }
    }

    if (subCategory?.length > 0) {
      for (const subcategoryItem of subCategory) {
        let postData = {
          categoryname: subcategoryItem.categoryname,
          outletId,
          category_image_url: subcategoryItem?.category_image_url || null,
          status: subcategoryItem.status,
          isFromPrimaryOutletId: true,
          parent_category_id:
            category?.find(
              (f) =>
                f?.parent_category_id === subcategoryItem?.parent_category_id
            )?.serverResponse?.parent_category_id || null,
        };

        const subCategoryData = await supabaseInstance
          .from("Menu_Categories")
          .insert(postData)
          .select("*")
          .maybeSingle();
        if (subCategoryData.data) {
          subcategoryItem.serverResponse = subCategoryData.data;
          serverSubCategory.push(subCategoryData.data);
        }
      }
    }

    if (item?.length > 0) {
      for (const itemItem of item) {
        const postData = {
          outletId,
          itemname: itemItem.itemname,
          attributeid: itemItem.attributeid || null,
          price: itemItem.price,
          itemdescription: itemItem.itemdescription,
          itemdescription: itemItem.itemdescription,
          minimumpreparationtime: itemItem.minimumpreparationtime,
          kcal: itemItem.kcal,
          servinginfo: itemItem.servinginfo,
          dietary_restriction_id: itemItem.dietary_restriction_id,
          spice_level_id: itemItem.spice_level_id || null,
          status: itemItem.status,
          isDelete: itemItem.isDelete,
          isFromPrimaryOutletId: true,
          item_categoryid:
            subCategory?.find(
              (f) => f?.categoryid === itemItem?.item_categoryid
            )?.serverResponse?.categoryid || null,
            item_image_url:itemItem.item_image_url
        };

        const menuItemData = await supabaseInstance
          .from("Menu_Item")
          .insert(postData)
          .select("*")
          .maybeSingle();
        if (menuItemData.data) {
          serverItem.push(menuItemData.data);
        } else {
          console.log(menuItemData);
        }
      }
    }

    res.status(200).json({
      success: true,
      data: {
        menuItemData: serverItem || [],
        categoryData: serverCategory || [],
        subCategoryData: serverSubCategory || [],
      },
    });
  } catch (error) {
    console.log(error);
    res.status(500).json({ success: false, error: error.message || error });
  }
});

router.get("/getOutletList", async (req, res) => {
  try {
    const { data, error } = await supabaseInstance
      .from("Outlet")
      .select(
        "outletId,outletName, hasMess,campusId(campusId,campusName),cityId(cityId,city))"
      );
    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

router.get("/realtimeOutletWeb/:outletId", function (req, res) {
  const { outletId } = req.params;
  res.statusCode = 200;
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader("Cache-Control", "no-cache");
  res.setHeader("connection", "keep-alive");
  res.setHeader("Content-Type", "text/event-stream");

  const channelName = `outletWeb-update-channel-${outletId}-${Date.now()}`;

  supabaseInstance
    .channel(channelName)
    .on(
      "postgres_changes",
      {
        event: "UPDATE",
        schema: "public",
        table: "Outlet",
        filter: `outletId=eq.${outletId}`,
      },
      async (payload) => {
        const outletData = await supabaseInstance
          .from("Outlet")
          .select(outletSelectString)
          .eq("outletId", payload.new.outletId)
          .maybeSingle();
        res.write("event: updateOutlet\n");
        res.write(`data: ${JSON.stringify(outletData.data)}\n\n`);
        res.write("\n\n");
      }
    )
    .on(
      "postgres_changes",
      {
        event: "UPDATE",
        schema: "public",
        table: "Timing",
        filter: `outletId=eq.${outletId}`,
      },
      async (payload) => {
        const outletData = await supabaseInstance
          .from("Outlet")
          .select(outletSelectString)
          .eq("outletId", payload.new.outletId)
          .maybeSingle();
        res.write("event: updateOutlet\n");
        res.write(`data: ${JSON.stringify(outletData.data)}\n\n`);
        res.write("\n\n");
      }
    )
    .subscribe(async (status, err) => {
      console.log(`outletWeb-update-channel-${outletId} status => `, status);
      if (status === "CHANNEL_ERROR") {
        console.error(`realtimeCurrentOrder/:outletId err => `, err);
      }
      if (status === "SUBSCRIBED") {
        const outletData = await supabaseInstance
          .from("Outlet")
          .select(outletSelectString)
          .eq("outletId", outletId)
          .maybeSingle();
        res.write("event: updateOutlet\n");
        res.write(`data: ${JSON.stringify(outletData.data)}\n\n`);
        res.write("\n\n");
      } else {
        console.log("err => ", err);
      }
      console.log("subscribe status for outletId => ", outletId);
    });

  res.write("retry: 10000\n\n");
  req.on("close", () => {
    supabaseInstance
      .channel(channelName)
      .unsubscribe()
      .then((res) => {
        console.log(".then => ", res);
      })
      .catch((err) => {
        console.log(".catch => ", err);
      })
      .finally(() => {
        console.log(`${channelName} Connection closed`);
      });
  });
});

router.post("/outletIsOpenStatusChange", async (req, res) => {
  const { isOutletOpen, outletId } = req.body;

  if ((isOutletOpen === true || isOutletOpen === false) && outletId) {
    try {
      const isOutletOpenTimestamp = moment().tz("Asia/Kolkata");
      console.log("body -> ", { isOutletOpen, isOutletOpenTimestamp });
      const outletUpdateResponse = await supabaseInstance
        .from("Outlet")
        .update({ isOutletOpen, isOutletOpenTimestamp })
        .eq("outletId", outletId)
        .select("outletId, isOutletOpen, isOutletOpenTimestamp")
        .maybeSingle();
      if (outletUpdateResponse?.data) {
        // let updateStoreStatusResponse = updateStoreStatus(outletId);
        // if (updateStoreStatusResponse?.success === false && updateStoreStatusResponse?.petpoojaApiError) {
        //   console.log("updateStoreStatusResponse => ",updateStoreStatusResponse);
        // }
        res.status(200).json({
          status: false,
          data: outletUpdateResponse.data,
          message: "Outlet status changes successfully.",
        });
      } else {
        throw outletUpdateResponse.error;
      }
    } catch (error) {
      console.error(error);
      res.status(500).json({
        status: false,
        error: error,
      });
    }
  } else {
    res.status(500).json({
      status: false,
      error: {
        message: "Please pass Boolean(isOutletOpen), UID(outletId) in body.",
      },
    });
  }
});

// router.post("/resetOutletPassword/:outletId", async (req, res) => {
//   const { outletId } = req.params;
//   try {
//     const _password = (Math.random() + 1).toString(36).substring(4);
//     console.log("_password==>",_password)
//     const { data, error } = await supabaseInstance.auth.admin.updateUserById(outletId, {password: _password});
//     if (data) {
//       const outletData = await supabaseInstance.from("Outlet").select("*").eq("outletId", outletId).maybeSingle();
//       if(outletData?.data){
//         // const sendMobileSMSResponse = await sendMobileSMS([{ mobiles: outletData.data.mobile, name: outletData.data.outletName, outletId: outletId }], msg91config.config);
//         // console.log("sendMobileSMSResponse => ", sendMobileSMSResponse);
//         // console.log("outletData.mobile => ", outletData.data.mobile);
//         // console.log("outletData.outletName => ", outletData.data.outletName);
//         // console.log("outletData.outletData.email => ", outletData.data.email);

//         const _email_to = [{name: outletData.data.outletName, email:  outletData.data.email}];
//         // const _email_cc =  []
//         // const _email_bcc =  []
//         // const _template_id =msg91config.config.email_otp_template_id
//         // const sendEmailResponse = await sendEmail(_email_to, _email_cc, _email_bcc, {}, _template_id);
//         // console.log("sendEmailResponse => ", sendEmailResponse);
//         res.status(200).json({
//           success: true,
//           message: "Password Send succesfully",
//         });
//       }else{
//         throw outletData.error
//       }
//     } else {
//       throw error;
//     }
//   } catch (error) {
//     res.status(500).json({ success: false, error: error.message });
//   }
// });

router.get("/getOutletsbyCampusId/:campusId", async (req, res) => {
  const { campusId } = req.params;
  try {
    const { data, error } = await supabaseInstance
      .from("Outlet")
      .select("outletName, outletId, sequence, campusId")
      .eq("campusId", campusId);
    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/addSequence", async (req, res) => {
  const sequences = req.body; // Assuming req.body is an array of objects with properties sequence, outletId, and campusId

  // Check if any required property is missing in any object
  const missingProps = sequences.some(
    ({ sequence, outletId, campusId }) => !sequence || !outletId || !campusId
  );
  if (missingProps) {
    return res.status(400).json({
      success: false,
      error: "Please provide sequence, outletId, and campusId for all items",
    });
  }

  try {
    for (const { sequence, outletId, campusId } of sequences) {
      // Check if the sequence already exists for the given campus and is not the same outlet
      // const { data: existingSequence, error: sequenceError } = await supabaseInstance
      //   .from("Outlet")
      //   .select("sequence")
      //   .eq("sequence", sequence)
      //   .eq("campusId", campusId)
      //   .neq("outletId", outletId) // Exclude the provided outletId
      //   .single();

      // // If a sequence already exists, return an error
      // if (existingSequence) {
      //   return res.status(400).json({ success: false, error: `Sequence ${sequence} already exists for another outlet` });
      // }

      // Update the sequence if it doesn't already exist
      const { data, error } = await supabaseInstance
        .from("Outlet")
        .update({ sequence: sequence })
        .eq("outletId", outletId)
        .select("sequence");

      if (!data) {
        throw error;
      }
    }

    return res.status(200).json({ success: true, data: sequences });
  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});

// ===================reset password with supabase flow===================

router.post("/send-reset-password-otp", async (req, res) => {
  const { email } = req.body;
  
  // Validate email presence
  if (!email) {
    return res.status(400).json({ success: false, error: "Email is required" });
  }

  try {
    
    //get the outletId from the email
    const { data: outletData, error: outletError } = await supabaseInstance
      .from("Outlet")
      .select("outletId, mobile, outletName")
      .eq("email", email)
      .maybeSingle();
    
    if (outletError) { 
      console.error("Error getting outlet by email", outletError);
      throw outletError;
    }

    if (!outletData) {
      return res.status(200).json({ success: false, error: "Outlet not found" });
    }

    // throw error if multiple outlets are found
    if (outletData.length > 1) {
      return res.status(200).json({ success: false, error: "Multiple outlets found for the same email" });
    }

    const mobile = "91" + outletData.mobile;

    // send otp on mobile
    const otpResponse = await sendMobileOtp(mobile, msg91config.config.otp_template_id, outletData.outletName)

    if (otpResponse.type != "success") {
      return res.status(200).json({ success: false, error: "Failed to send OTP" });
    }

    return res.status(200).json({ success: true, mobile: outletData.mobile, message: "Reset password OTP sent successfully" });

  } catch (error) {
    console.error("Error sending reset password OTP", error);
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/verify-reset-password-otp", async (req, res) => { 
  const { otp, email, mobile } = req.body;
 
  if (!otp || !email) {
    return res.status(400).json({ success: false, error: "OTP and email are required" });
  }

  try {
    const mobileWith91 = "91" + mobile;
    const verifyOtpResponse = await verifyMobileOtp(mobileWith91, otp);

    if (verifyOtpResponse.type != "success") {
      return res.status(200).json({ success: false, error: "Failed to verify OTP" });
    }

    // get the outletId from the email
    const { data: outletData, error: outletError } = await supabaseInstance
      .from("Outlet")
      .select("outletId")
      .eq("email", email)
      .eq("mobile", mobile)
      .maybeSingle();
    
    if (outletError) {
      console.error("Error getting outlet by email", outletError);
      throw outletError;
    }

    if (!outletData) {
      return res.status(200).json({ success: false, error: "Outlet not found" });
    }

    // throw error if multiple outlets are found
    if (outletData.length > 1) {
      return res.status(200).json({ success: false, error: "Multiple outlets found for the same email and mobile" });
    }

    return res.status(200).json({ success: true, outletId: outletData.outletId, message: "OTP verified successfully" });

  } catch (error) {
    console.error("Error verifying reset password OTP", error);
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/updateOutletPassword", async (req, res) => {
  const { password, outletId } = req.body;
  
  // Validate presence of password and token
  if (!password || !outletId) {
    return res.status(400).json({
      success: false,
      error: "Password and outletId are required",
    });
  }
  
  try {
    const { error } = await supabaseInstance.auth.admin.updateUserById(outletId, { password: password });
    if (error) { 
      console.error("Error updating outlet password", error);
      throw error;
    }

    res.status(200).json({ success: true, message: "Password updated successfully" });

  } catch (error) {
    // Handle unexpected errors
    res.status(500).json({
      success: false,
      error: error.message,
      message: "Failed to reset password",
    });
  }
});
// ===================/resetPassword with supabase flow===================

router.get("/getOutletName/:outletId" , async(req, res) => {
  const {outletId} = req.params;

  if(!outletId){
    return res.status(400).json({
      success: false,
      error: "outletId is required"
    });
  }

  try {
    const {data, error} = await supabaseInstance
      .from("Outlet")
      .select("outletName")
      .eq("outletId", outletId)
      .single();

    if(data){
      return res.status(200).json({
        success: true,
        data
      });
    }else{
      throw error;
    }
  } catch (error) {
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

router.post("/deleteOutlet/:outletId", async (req, res) => {
  const { outletId } = req.params;
  try {
    const { data, error } = await supabaseInstance
      .from("Outlet")
      .update({ isDelete: true })
      .eq("outletId", outletId);
    
      if(error) throw error;

      return res.status(200).json({success:true, message: "Outlet deleted successfully"});
  } catch (error) {
    console.log('error' , error);
    
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/sendNotifications", async (req, res) => {
  try {
    const { title, body, selectedCustomers, selectedApps, outletName } = req.body;

    if(!title || !body || !outletName){
      return res.status(400).json({
        success: false,
        error: "Title, Body and Outlet Name are required"
      });
    }


    console.log('req.body', req.body);
    
    if (!selectedCustomers?.length || !selectedApps?.length) {
      return res.status(400).json({ 
        success: false, 
        error: "Selected customers and apps are required" 
      });
    }

    // Fetch FCM tokens for all apps
    const { success, tokens, error: fetchError } = await fetchFCMTokens(
      selectedCustomers, 
      selectedApps
    );

    if (!success) {
      throw fetchError;
    }

    // Send notifications for each app
    const results = [];
    for (const appName of selectedApps) {
      const appTokens = tokens[appName].map(t => t.fcmToken);
      if (appTokens.length > 0) {
        const result = await sendOutletNotifications(
          title,
          body,
          appTokens,
          appName,
          outletName,
        );
        results.push({ appName, ...result });
      }
    }

    // Aggregate results
    const aggregateResults = {
      totalProcessed: results.reduce((sum, r) => sum + r.totalProcessed, 0),
      totalSuccess: results.reduce((sum, r) => sum + r.successCount, 0),
      totalFailures: results.reduce((sum, r) => sum + r.failureCount, 0),
      totalInvalidTokens: results.reduce((sum, r) => sum + r.invalidTokensRemoved, 0),
      resultsByApp: results
    };

    return res.status(200).json({ 
      success: true, 
      message: "Notifications sent successfully",
      ...aggregateResults
    });

  } catch (error) {
    console.error("error", error);
    return res.status(500).json({ success: false, error });
  }
});

/**
 * @description Toggle the autoAcceptOrder setting for an outlet
 * @param {string} outletId - The ID of the outlet
 * @param {boolean} autoAcceptOrder - The new value for autoAcceptOrder
 * @returns {object} - The updated outlet data
 */
router.post("/toggleAutoAcceptOrder/:outletId", async (req, res) => {
  const { outletId } = req.params;
  const { autoAcceptOrder } = req.body;
  try {
    // Validate outletId and autoAcceptOrder
    if (!outletId || typeof outletId !== 'string') {
      throw new Error('Invalid outletId');
    }
    if (typeof autoAcceptOrder !== 'boolean') {
      throw new Error('Invalid autoAcceptOrder type');
    }

    const { error } = await supabaseInstance
      .from("Outlet")
      .update({ autoAcceptOrder })
      .eq("outletId", outletId)
      .single();

    if (error) {
      throw error;
    }

    return res.status(200).json({
      success: true,
      message: "Auto accept order setting updated successfully",
    });
  } catch (error) {
    console.error("Error toggling autoAcceptOrder:", error);
    return res.status(500).json({ success: false, error: error.message });
  }
});


/**
 * @description Get the autoAcceptOrder setting for an outlet
 * @param {string} outletId - The ID of the outlet
 * @return {object} - The autoAcceptOrder setting
 */
router.get("/getAutoAcceptOrderValue/:outletId", async (req, res) => {
  const { outletId } = req.params;
  try {
    // Validate outletId
    if (!outletId || typeof outletId !== 'string') {
      throw new Error('Invalid outletId');
    }

    const { data, error } = await supabaseInstance
      .from("Outlet")
      .select("autoAcceptOrder")
      .eq("outletId", outletId)
      .single();

    if (error) {
      throw error;
    }

    return res.status(200).json({
      success: true,
      data: data,
    });
  } catch (error) {
    console.error("Error getting autoAcceptOrder:", error);
    return res.status(500).json({ success: false, error: error.message });
  }
});


//===============================================Delivery Address================================================

router.post("/addDeliveryAddress", async (req, res) => {
  const { outletId, address, locality, city, state, pincode, country, label, notes } = req.body;
  if (!outletId || !address ) {
    return res.status(400).json({
      success: false,
      error: "Missing required fields.",
      message: `Missing : ${!outletId ? "outletId, " : ""}${!address ? "address, " : ""}`
    });
  }
  try {
    const { error } = await supabaseInstance
      .from("Outlet_Delivery_Addresses")
      .insert({ outletId, address, locality, city, state, pincode, country, label, notes })
      .single();

    if (error) throw error;

    return res.status(200).json({ success: true, message: "Delivery address added successfully" });
  } catch (error) {
    console.error("Error adding delivery address:", error);
    return res.status(500).json({ success: false, error: error.message });
  }
});

router.get("/getDeliveryAddresses/:outletId", async (req, res) => { 
  try {
    const { outletId } = req.params;
    if (!outletId) {
      return res.status(400).json({ success: false, error: "Outlet ID is required" });
    }

    const { data, error } = await supabaseInstance
      .from("Outlet_Delivery_Addresses")
      .select("*")
      .eq("outletId", outletId);

    if (error) throw error;

    return res.status(200).json({ success: true, data: data });
  } catch (error) {
    console.error("Error getting delivery addresses:", error);
    return res.status(500).json({ success: false, error: error.message });
  }
});

router.get("/getDeliveryAddressById/:outlet_delivery_address_id", async (req, res) => { 
  const { outlet_delivery_address_id } = req.params;
  if (!outlet_delivery_address_id) {
    return res.status(400).json({ success: false, error: "Delivery address ID is required" });
  }
  
  try {
    const { data, error } = await supabaseInstance
      .from("Outlet_Delivery_Addresses")
      .select("*")
      .eq("outlet_delivery_address_id", outlet_delivery_address_id)
      .single();

    if (error) throw error;

    return res.status(200).json({ success: true, data: data });
  } catch (error) {
    console.error("Error getting delivery address by ID:", error);
    return res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/updateDeliveryAddress/:outlet_delivery_address_id", async (req, res) => { 
  const { outlet_delivery_address_id } = req.params;
  if(!outlet_delivery_address_id) {
    return res.status(400).json({ success: false, error: "Delivery address ID is required" });
  }

  try {
    const { address, locality, city, state, pincode, country, label, notes } = req.body;

    const { error } = await supabaseInstance
      .from("Outlet_Delivery_Addresses")
      .update({ address, locality, city, state, pincode, country, label, notes })
      .eq("outlet_delivery_address_id", outlet_delivery_address_id)
      .single();

    if (error) throw error;

    return res.status(200).json({ success: true, message: "Delivery address updated successfully" });
  } catch (error) {
    console.error("Error updating delivery address:", error);
    return res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/deleteDeliveryAddress/:outlet_delivery_address_id", async (req, res) => { 
  const { outlet_delivery_address_id } = req.params;
  if(!outlet_delivery_address_id) {
    return res.status(400).json({ success: false, error: "Delivery address ID is required" });
  }

  try {
    const { error } = await supabaseInstance
      .from("Outlet_Delivery_Addresses")
      .delete()
      .eq("outlet_delivery_address_id", outlet_delivery_address_id)
      .single();

    if (error) throw error;

    return res.status(200).json({ success: true, message: "Delivery address deleted successfully" });
  } catch (error) {
    console.error("Error deleting delivery address:", error);
    return res.status(500).json({ success: false, error: error.message });
  }
});

//===============================================/Delivery Address================================================
module.exports = router;

// const { data, error } = await supabase
// .storage
// .from('avatars')
// .upload('public/avatar1.png', avatarFile, {
//   cacheControl: '3600',
//   upsert: false
// })
