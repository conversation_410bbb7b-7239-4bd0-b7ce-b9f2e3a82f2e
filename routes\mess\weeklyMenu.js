var express = require("express");
var router = express.Router();
var supabaseInstance = require("../../services/supabaseClient").supabase;
var moment  = require('moment');
const { getWeekInfo, getDayNameFromNumber, getMaximumWeekNumberForMonth } = require("../../services/dateTimeService");
const multer = require("multer");
const upload = multer();
const storage = multer.memoryStorage();

const {sendMessNotification} = require("../firebase");


function getMealTypeFromNumber(mealTypeId) {
  switch (mealTypeId) {
    case 1:
      return "Breakfast";
    case 2:
      return "Lunch";
    case 3:
      return "High Tea";
    case 4:
      return "Dinner";
    default:
      return "Invalid meal type";
  }
}

function checkIfCurrent(startTime, endTime) {
  // Extract only the time part using moment and format it as 'HH:mm:ss'
  const start = moment(startTime).format('HH:mm:ss');
  const end = moment(endTime).format('HH:mm:ss');
  const now = moment().format('HH:mm:ss');

  // Check if the current time is between startTime and endTime
  let isCurrent;
  if (start < end) {
      // If end time is after start time on the same day
      isCurrent = now >= start && now <= end;
  } else {
      // If end time is before start time, it means the end time is on the next day
      isCurrent = now >= start || now <= end;
  }

  return isCurrent;
}
/*
Table: Weekly_Menu

menuId	bigint	number	
created_at	timestamp with time zone	string	
weekNumber	bigint	number	
dayOfWeek	bigint	number	
mealTypeId	bigint	number	
menuDescription	text	string	
outletId	uuid	string	The id of mess (outlet) of which the menu is

*/
router.get("/", async (req, res) => {
  return res.send("Response from weeklyMenu route");
});

// router.post("/addWeeklyMenu", async (req, res) => {
//   try {
//     let {
//       dayOfWeek,
//       selectedWeeks,
//       outletId,
//       breakfast,
//       lunch,
//       highTea,
//       dinner,
//     } = req.body;

//     // convert selectedWeeks elements to number from string
//     selectedWeeks = selectedWeeks.map((week) => parseInt(week));

//     let weeklyMenuToInsert = [];
//     selectedWeeks.forEach((weekNumber) => {
//       weeklyMenuToInsert.push({
//         weekNumber,
//         dayOfWeek,
//         mealTypeId: 1,
//         menuDescription: breakfast,
//         outletId,
//       });
//       weeklyMenuToInsert.push({
//         weekNumber,
//         dayOfWeek,
//         mealTypeId: 2,
//         menuDescription: lunch,
//         outletId,
//       });
//       weeklyMenuToInsert.push({
//         weekNumber,
//         dayOfWeek,
//         mealTypeId: 3,
//         menuDescription: highTea,
//         outletId,
//       });
//       weeklyMenuToInsert.push({
//         weekNumber,
//         dayOfWeek,
//         mealTypeId: 4,
//         menuDescription: dinner,
//         outletId,
//       });
//     });

//     // check if the menu already exists for the given week and day and mealType. If yes, then update the menu else insert the menu
//     for (let i = 0; i < weeklyMenuToInsert.length; i++) {
//       const { weekNumber, dayOfWeek, mealTypeId, menuDescription, outletId } =
//         weeklyMenuToInsert[i];

//       const { data, error } = await supabaseInstance
//         .from("Weekly_Menu")
//         .select("*")
//         .eq("weekNumber", weekNumber)
//         .eq("dayOfWeek", dayOfWeek)
//         .eq("mealTypeId", mealTypeId)
//         .eq("outletId", outletId);

//       if (error) throw error;

//       if (data.length > 0) {
//         // update the menu
//         const { error: updateError } = await supabaseInstance
//           .from("Weekly_Menu")
//           .update({
//             weekNumber,
//             dayOfWeek,
//             mealTypeId,
//             menuDescription,
//             outletId,
//           })
//           .eq("menuId", data[0].menuId);

//         if (updateError) throw updateError;
//       } else {
//         // insert the menu
//         const { error: insertError } = await supabaseInstance
//           .from("Weekly_Menu")
//           .insert(weeklyMenuToInsert[i]);

//         if (insertError) throw insertError;
//       }
//     }

//     if (error) throw error;
//     return res.status(200).json({ success: true, data });
//   } catch (error) {
//     return res.status(500).json({ success:true,error });
//   }
// });

// router.post("/addWeeklyMenu", async (req, res) => {
//   try {
//     let { dayOfWeek, selectedWeeks, outletId, breakfast, lunch, highTea, dinner } = req.body;

//     // Convert selectedWeeks elements to number from string
//     selectedWeeks = selectedWeeks.map(week => parseInt(week));

//     let weeklyMenuToInsert = [];
//     selectedWeeks.forEach(weekNumber => {
//       if(breakfast) weeklyMenuToInsert.push({ weekNumber, dayOfWeek, mealTypeId: 1, menuDescription: breakfast, outletId });
//       if(lunch) weeklyMenuToInsert.push({ weekNumber, dayOfWeek, mealTypeId: 2, menuDescription: lunch, outletId });
//       if(highTea) weeklyMenuToInsert.push({ weekNumber, dayOfWeek, mealTypeId: 3, menuDescription: highTea, outletId });
//       if(dinner) weeklyMenuToInsert.push({ weekNumber, dayOfWeek, mealTypeId: 4, menuDescription: dinner, outletId });
//     });

//     const result = [];
//     const menuPromises = weeklyMenuToInsert.map(async menu => {
//       const { weekNumber, dayOfWeek, mealTypeId, menuDescription, outletId } = menu;

//       const { data, error } = await supabaseInstance
//         .from("Weekly_Menu")
//         .select("menuId")
//         .eq("weekNumber", weekNumber)
//         .eq("dayOfWeek", dayOfWeek)
//         .eq("mealTypeId", mealTypeId)
//         .eq("outletId", outletId)
//         .single();

//       if (error && error.code !== "PGRST116") throw error; // "PGRST116" is Supabase error for no rows returned

//       if (data) {
//         // Update the menu if it exists
//         const { data:updateData , error: updateError } = await supabaseInstance
//           .from("Weekly_Menu")
//           .update({ menuDescription })
//           .eq("menuId", data.menuId)
//           .select("*")
//           .single();

//         if (updateError) throw updateError;
//         result.push({ updateData });
//       } else {
//         // Insert the menu if it does not exist
//         const { data: insertData, error: insertError } = await supabaseInstance
//           .from("Weekly_Menu")
//           .insert(menu)
//           .single();

//         if (insertError) throw insertError;
//         result.push({insertData});
//       }
//     });

//     await Promise.all(menuPromises);

//     return res.status(200).json({ success: true, message: "Weekly menu added/updated successfully", result });
//   } catch (error) {
//     return res.status(500).json({ success: false, error: error.message || "An error occurred" });
//   }
// });



// Define the fields we expect
const uploadFields = [
  { name: 'breakfastImage', maxCount: 1 },
  { name: 'lunchImage', maxCount: 1 },
  { name: 'highTeaImage', maxCount: 1 },
  { name: 'dinnerImage', maxCount: 1 }
];

// Helper function to upload image to Supabase storage
const uploadImageToSupabase = async (file, outletId, key=new Date()) => {
  if (!file) return null;

  try {
    // Generate unique filename
    const uniqueFileName = `${outletId}/${key}`;

    // remove the file if it already exists
    await supabaseInstance.storage.from('menu-item-image').remove([uniqueFileName]);

    // Upload buffer to Supabase storage
    const { data, error } = await supabaseInstance
      .storage
      .from('menu-item-image')
      .upload(uniqueFileName, file.buffer, {
        contentType: file.mimetype,
        upsert: true
      });

    if (error) throw error;

    // Get public URL
    const { data: { publicUrl } } = supabaseInstance
      .storage
      .from('menu-item-image')
      .getPublicUrl(uniqueFileName);

    return publicUrl;
  } catch (error) {
    console.error('Error uploading image:', error);
    return null;
  }
};

router.post("/addWeeklyMenu", upload.fields(uploadFields), async (req, res) => {
  try {
    // Parse selectedWeeks from string to array if it's sent as a string
    let selectedWeeks = req.body.selectedWeeks;
    try {
      selectedWeeks = JSON.parse(selectedWeeks);
    } catch (e) {
      // If parsing fails, assume it's already in correct format
      console.log('selectedWeeks parsing error:', e);
      return res.status(400).json({ success: false, error: 'Invalid selectedWeeks format' });
    }

    let {
      dayOfWeek,
      outletId,
      breakfast,
      lunch,
      highTea,
      dinner,
      breakfastPrice,
      lunchPrice,
      highTeaPrice,
      dinnerPrice,
      isNotify,
      notificationTitle,
      notificationDescription,
    } = req.body;

    // convert price to number or 0 if not provided
    breakfastPrice = parseInt(breakfastPrice) || 0;
    lunchPrice = parseInt(lunchPrice) || 0;
    highTeaPrice = parseInt(highTeaPrice) || 0;
    dinnerPrice = parseInt(dinnerPrice) || 0;


    console.log(req.body)
    // Upload images to Supabase storage
    const breakfastImageUrl = await uploadImageToSupabase(req.files?.breakfastImage?.[0], req.body.outletId, "breakfast");
    const lunchImageUrl = await uploadImageToSupabase(req.files?.lunchImage?.[0], req.body.outletId, "lunch");
    const highTeaImageUrl = await uploadImageToSupabase(req.files?.highTeaImage?.[0], req.body.outletId, "hightea");
    const dinnerImageUrl = await uploadImageToSupabase(req.files?.dinnerImage?.[0], req.body.outletId, "dinner");

    console.log('Image URLs:', { breakfastImageUrl, lunchImageUrl, highTeaImageUrl, dinnerImageUrl });
    // Convert selectedWeeks elements to number from string
    selectedWeeks = selectedWeeks?.map((week) => parseInt(week));
    console.log(selectedWeeks);

    /*
    // if 1 is present in selectedWeeks then also add 5 as a week number
    if (selectedWeeks?.includes(1)) {
      if (!selectedWeeks?.includes(5)) {
        selectedWeeks?.push(5);
      }
    }

    if (selectedWeeks?.includes(2)) {
      if (!selectedWeeks?.includes(6)) {
        selectedWeeks?.push(6);
      }
    }
    */
    let weeklyMenuToInsert = [];
    selectedWeeks?.forEach((weekNumber) => {
      // condition for week 5 as a month has only max 31 days
      if (breakfast)
        weeklyMenuToInsert.push({
          weekNumber,
          dayOfWeek,
          mealTypeId: 1,
          menuDescription: breakfast,
          outletId,
          price: breakfastPrice || 0,
        });
      if (lunch)
        weeklyMenuToInsert.push({
          weekNumber,
          dayOfWeek,
          mealTypeId: 2,
          menuDescription: lunch,
          outletId,
          price: lunchPrice || 0,
        });
      if (highTea)
        weeklyMenuToInsert.push({
          weekNumber,
          dayOfWeek,
          mealTypeId: 3,
          menuDescription: highTea,
          outletId,
          price: highTeaPrice || 0,
        });
      if (dinner)
        weeklyMenuToInsert.push({
          weekNumber,
          dayOfWeek,
          mealTypeId: 4,
          menuDescription: dinner,
          outletId,
          price: dinnerPrice || 0,
        });
    });

    const result = [];
    const menuPromises = weeklyMenuToInsert?.map(async (menu) => {
      const {
        weekNumber,
        dayOfWeek,
        mealTypeId,
        menuDescription,
        outletId,
        price,
      } = menu;

      const { data, error } = await supabaseInstance
        .from("Weekly_Menu")
        .select("menuId")
        .eq("weekNumber", weekNumber)
        .eq("dayOfWeek", dayOfWeek)
        .eq("mealTypeId", mealTypeId)
        .eq("outletId", outletId)
        .single();

      if (error && error.code !== "PGRST116") throw error; // "PGRST116" is Supabase error for no rows returned

      if (data) {
        // Update the menu if it exists
        const { data: updateData, error: updateError } = await supabaseInstance
          .from("Weekly_Menu")
          .update({ menuDescription, price })
          .eq("menuId", data.menuId)
          .select("*")
          .single();

        if (updateError) throw updateError;
        result.push(updateData);
      } else {
        // Insert the menu if it does not exist
        const { data: insertData, error: insertError } = await supabaseInstance
          .from("Weekly_Menu")
          .insert(menu)
          .select("*")
          .single();

        if (insertError) throw insertError;
        result.push(insertData);
      }
    });

    await Promise.all(menuPromises);

    const { data: outletData, error: outletError } = await supabaseInstance
      .from("Outlet")
      .select("cashAndCarry, outletName")
      .eq("outletId", outletId)
      .single();

    if (outletError) throw outletError;

    console.log(outletData);
    if (outletData.cashAndCarry) {
      // also add or update the menu in the menu table
      let itemToInsert = [];
      if (breakfast) {
        itemToInsert.push({
          outletId,
          itemname: "Breakfast",
          price: breakfastPrice,
          item_image_url: breakfastImageUrl,
          status: true,
          isMessItem: true,
        });
      }
  
        if (lunch) {
          itemToInsert.push({
            outletId,
            itemname: "Lunch",
            price: lunchPrice,
            item_image_url: lunchImageUrl,
            status: true,
            isMessItem: true,
          });
        }
  
        if (highTea) {
          itemToInsert.push({
            outletId,
            itemname: "High Tea",
            price: highTeaPrice,
            item_image_url: highTeaImageUrl,
            status: true,
            isMessItem: true,
          });
        }
  
        if (dinner) {
          itemToInsert.push({
            outletId,
            itemname: "Dinner",
            price: dinnerPrice,
            item_image_url: dinnerImageUrl,
            status: true,
            isMessItem: true,
          });
        }
        

      const itemPromises = itemToInsert?.map(async (item) => {
        // check if the item with same name and outletId exists. If exist then update the data else insert the data
        const { itemname, price, item_image_url, status, isMessItem } = item;
        const { data, error } = await supabaseInstance
          .from("Menu_Item")
          .select("itemid")
          .eq("itemname", itemname)
          .eq("outletId", outletId)
          .single();

        if (error && error.code !== "PGRST116") throw error; // "PGRST116" is Supabase error for no rows returned

        if (data) {
          console.log("item found. Updating item");
          // Update the item if it exists
          if (item_image_url) {
            const { data: updateData, error: updateError } =
              await supabaseInstance
                .from("Menu_Item")
                .update({ price, item_image_url, status, isMessItem })
                .eq("itemid", data.itemid)
                .select("*")
                .single();

            if (updateError) throw updateError;
            result.push(updateData);
          } else {
            const { data: updateData, error: updateError } =
              await supabaseInstance
                .from("Menu_Item")
                .update({ price, status, isMessItem })
                .eq("itemid", data.itemid)
                .select("*")
                .single();

            if (updateError) throw updateError;
            result.push(updateData);
          }
        } else {
          console.log("item not found. Inserting item");
          // Insert the item if it does not exist
          const { data: insertData, error: insertError } =
            await supabaseInstance
              .from("Menu_Item")
              .insert(item)
              .select("*")
              .single();

          if (insertError) throw insertError;
          result.push(insertData);
        }
      });

      await Promise.all(itemPromises);
    }

    if (isNotify) {
      // Step 1: Fetch whitelisted users for the outlet
      const { data: whitelistUsers, error: whitelistError } =
        await supabaseInstance
          .from("Mess_Whitelist")
          .select("customerAuthUID")
          .eq("outletId", outletId);

      if (whitelistError) throw whitelistError;

      // Step 2: Fetch the FCM tokens of the users from the customers table
      const customerUIDs = whitelistUsers.map((user) => user.customerAuthUID);
      const { data: fcmTokens, error: tokenError } = await supabaseInstance
        .from("FCM_Tokens")
        .select("fcmToken")
        .in("customerAuthUID", customerUIDs);

      if (tokenError) throw tokenError;

      const tokens = fcmTokens.map((token) => token.fcmToken).filter(Boolean);

      // Step 3: Send notifications to whitelisted users
      for (const token of tokens) {
        await sendMessNotification(
          notificationTitle=='' ? "New Menu Added" : notificationTitle,
          notificationDescription=='' ? "New menu has been added to the mess. Check it out now!" : notificationDescription,
          token,
          outletData.outletName,
          outletId
        );
      }
    }

    return res.status(200).json({
      success: true,
      message: "Weekly menu added successfully",
      result,
    });
  } catch (error) {
    console.log(error);
    return res
      .status(500)
      .json({ success: false, error: error.message || "An error occurred" });
  }
});

// router.get("/getMessMenu/:outletId", async (req, res) => {
//   try {
//     const { outletId } = req.params;
//     const { weekNumber, dayOfWeek, tomorrowDayOfWeek, tomorrowWeekNumber } =
//       getWeekInfo();

//     let { data: today, error } = await supabaseInstance
//       .from("Weekly_Menu")
//       .select("*")
//       .eq("weekNumber", weekNumber)
//       .eq("dayOfWeek", dayOfWeek)
//       .eq("outletId", outletId);

//     if (error) throw error;

//     let { data: tomorrow, error: error2 } = await supabaseInstance
//       .from("Weekly_Menu")
//       .select("*")
//       .eq("weekNumber", tomorrowWeekNumber)
//       .eq("dayOfWeek", tomorrowDayOfWeek)
//       .eq("outletId", outletId)
//       .eq("mealTypeId", 1);

//     if (error2) throw error2;

//     if (today.length > 0) {
//       today.forEach((menu) => {
//         menu.dayOfWeek = getDayNameFromNumber(menu.dayOfWeek);
//         menu.mealTypeId = getMealTypeFromNumber(menu.mealTypeId);
//         // remove created_at from the response
//         delete menu.created_at;
//       });
//     }

//     if (tomorrow.length > 0) {
//       tomorrow.forEach((menu) => {
//         menu.dayOfWeek = getDayNameFromNumber(menu.dayOfWeek);
//         menu.mealTypeId = getMealTypeFromNumber(menu.mealTypeId);
//         // remove created_at from the response
//         delete menu.created_at;
//       });
//     }

//     return res.status(200).json({ success: true, today, tomorrow });
//   } catch (error) {
//     return res.status(500).json({ success: false, error });
//   }
// });

/*optimized code for getMessMenu*/
// router.get("/getMessMenu/:outletId", async (req, res) => {
//   try {
//     const { outletId } = req.params;
//     const { weekNumber, dayOfWeek, tomorrowDayOfWeek, tomorrowWeekNumber } =
//       getWeekInfo();

//     const customerAuthUID = req.query.cuid;

//     // Parallelize the database queries
//     const [todayResult, tomorrowResult] = await Promise.all([
//       supabaseInstance
//         .from("Weekly_Menu")
//         .select("*")
//         .eq("weekNumber", weekNumber)
//         .eq("dayOfWeek", dayOfWeek)
//         .eq("outletId", outletId),
//       supabaseInstance
//         .from("Weekly_Menu")
//         .select("*")
//         .eq("weekNumber", tomorrowWeekNumber)
//         .eq("dayOfWeek", tomorrowDayOfWeek)
//         .eq("outletId", outletId)
//         .eq("mealTypeId", 1),
//     ]);

//     let { data: today, error: errorToday } = todayResult;
//     let { data: tomorrow, error: errorTomorrow } = tomorrowResult;

//     if (errorToday) throw errorToday;
//     if (errorTomorrow) throw errorTomorrow;

//     // Process data efficiently
//     const processMenu = (menu) => {
//       return menu.map((item) => ({
//         ...item,
//         dayOfWeek: getDayNameFromNumber(item.dayOfWeek),
//         mealTypeId: getMealTypeFromNumber(item.mealTypeId),
//         created_at: undefined, // Removes created_at field
//       }));
//     };

//     let processedToday = today.length > 0 ? processMenu(today) : [];
//     let processedTomorrow = tomorrow.length > 0 ? processMenu(tomorrow) : [];

//     // add response field to processedToday from RSVP table
//     const { data: rsvpData, error: rsvpError } = await supabaseInstance
//       .from("RSVP")
//       .select("*")
//       .eq("rsvpDate", new Date().toISOString().split("T")[0])
//       .eq("customerAuthUID", customerAuthUID);

//     if (rsvpError) throw rsvpError;
//     console.log(new Date().toISOString().split("T")[0]);

//     const rsvpMap = {};
//     rsvpData.forEach((rsvp) => {
//       rsvpMap[rsvp.menuId] = rsvp.response;
//     });

//     processedToday.forEach((menu) => {
//       menu.isChoice = rsvpMap[menu.menuId];
//     });

//     const { data: rsvpDataTomorrow, error: rsvpErrorTomorrow } =
//       await supabaseInstance
//         .from("RSVP")
//         .select("*")
//         .eq(
//           "rsvpDate",
//           new Date(new Date().getTime() + 24 * 60 * 60 * 1000)
//             .toISOString()
//             .split("T")[0]
//         )
//         .eq("customerAuthUID", customerAuthUID);

//     if (rsvpErrorTomorrow) throw rsvpErrorTomorrow;

//     const rsvpMapTomorrow = {};
//     rsvpDataTomorrow.forEach((rsvp) => {
//       rsvpMapTomorrow[rsvp.menuId] = rsvp.response;
//     });

//     processedTomorrow.forEach((menu) => {
//       menu.isChoice = rsvpMapTomorrow[menu.menuId];
//     });

//     return res.status(200).json({
//       success: true,
//       today: processedToday,
//       tomorrow: processedTomorrow,
//     });
//   } catch (error) {
//     return res.status(500).json({ success: false, error: error.message });
//   }
// });

/* mess menu with timings (production)*/
// router.get("/getMessMenu/:outletId", async (req, res) => {
//   try {
//     const { outletId } = req.params;
//     const { weekNumber, dayOfWeek, tomorrowDayOfWeek, tomorrowWeekNumber } =
//       getWeekInfo();

//     const customerAuthUID = req.query.cuid;

//     // Parallelize the database queries
//     const [todayResult, tomorrowResult, menuTimingsResult] = await Promise.all([
//       supabaseInstance
//         .from("Weekly_Menu")
//         .select("*")
//         .eq("weekNumber", weekNumber)
//         .eq("dayOfWeek", dayOfWeek)
//         .eq("outletId", outletId),
//       supabaseInstance
//         .from("Weekly_Menu")
//         .select("*")
//         .eq("mealTypeId", 1)
//         .eq("weekNumber", tomorrowWeekNumber)
//         .eq("dayOfWeek", tomorrowDayOfWeek)
//         .eq("outletId", outletId),
//       supabaseInstance
//         .from("Meal_Timings")
//         .select("*")
//         .eq("outletId", outletId),
//     ]);

//     let { data: today, error: errorToday } = todayResult;
//     let { data: tomorrow, error: errorTomorrow } = tomorrowResult;
//     let { data: menuTimings, error: errorMenuTimings } = menuTimingsResult;

//     if (errorToday) throw errorToday;
//     if (errorTomorrow) throw errorTomorrow;
//     if (errorMenuTimings) throw errorMenuTimings;

//     // Create a map for menu timings
//     const menuTimingsMap = {};
//     menuTimings.forEach((timing) => {
//       menuTimingsMap[timing.mealTypeId] = {
//         startTime: timing.startTime,
//         endTime: timing.endTime,
//         rsvpDeadline: timing.rsvpDeadline,
//       };
//     });

//     // Process data efficiently
//     const processMenu = (menu) => {
//       return menu.map((item) => ({
//         ...item,
//         dayOfWeek: getDayNameFromNumber(item.dayOfWeek),
//         mealTypeId: getMealTypeFromNumber(item.mealTypeId),
//         startTime: menuTimingsMap[item.mealTypeId]?.startTime || null,
//         endTime: menuTimingsMap[item.mealTypeId]?.endTime || null,
//         rsvpDeadline: menuTimingsMap[item.mealTypeId]?.rsvpDeadline || null,
//         created_at: undefined, // Removes created_at field
//       }));
//     };

//     let processedToday = today.length > 0 ? processMenu(today) : [];
//     let processedTomorrow = tomorrow.length > 0 ? processMenu(tomorrow) : [];

//     // Add response field to processedToday from RSVP table
//     const { data: rsvpData, error: rsvpError } = await supabaseInstance
//       .from("RSVP")
//       .select("*")
//       .eq("rsvpDate", new Date().toISOString().split("T")[0])
//       .eq("customerAuthUID", customerAuthUID);

//     if (rsvpError) throw rsvpError;

//     const rsvpMap = {};
//     rsvpData.forEach((rsvp) => {
//       rsvpMap[rsvp.menuId] = rsvp.response;
//     });

//     processedToday.forEach((menu) => {
//       menu.isChoice = rsvpMap[menu.menuId];
//     });

//     const { data: rsvpDataTomorrow, error: rsvpErrorTomorrow } =
//       await supabaseInstance
//         .from("RSVP")
//         .select("*")
//         .eq(
//           "rsvpDate",
//           new Date(new Date().getTime() + 24 * 60 * 60 * 1000)
//             .toISOString()
//             .split("T")[0]
//         )
//         .eq("customerAuthUID", customerAuthUID);

//     if (rsvpErrorTomorrow) throw rsvpErrorTomorrow;

//     const rsvpMapTomorrow = {};
//     rsvpDataTomorrow.forEach((rsvp) => {
//       rsvpMapTomorrow[rsvp.menuId] = rsvp.response;
//     });

//     processedTomorrow.forEach((menu) => {
//       menu.isChoice = rsvpMapTomorrow[menu.menuId];
//     });

//     return res.status(200).json({
//       success: true,
//       today: processedToday,
//       tomorrow: processedTomorrow,
//     });
//   } catch (error) {
//     return res.status(500).json({ success: false, error: error.message });
//   }
// });

router.get("/getMessMenu/:outletId", async (req, res) => {
  try {
    const { outletId } = req.params;
    const { weekNumber, dayOfWeek, tomorrowDayOfWeek, tomorrowWeekNumber } = getWeekInfo();

    const customerAuthUID = req.query.cuid;
    const currentTime = new Date();

    // Parallelize the database queries
    const [todayResult, tomorrowResult, menuTimingsResult] = await Promise.all([
      supabaseInstance
        .from("Weekly_Menu")
        .select("*")
        .eq("weekNumber", weekNumber)
        .eq("dayOfWeek", dayOfWeek)
        .eq("outletId", outletId)
        .order("mealTypeId", { ascending: true }),
      supabaseInstance
        .from("Weekly_Menu")
        .select("*")
        .eq("weekNumber", tomorrowWeekNumber)
        .eq("dayOfWeek", tomorrowDayOfWeek)
        .eq("outletId", outletId)
        .in("mealTypeId", [1, 2])
        .order("mealTypeId", { ascending: true }),
      supabaseInstance
        .from("Meal_Timings")
        .select("*")
        .eq("outletId", outletId),
    ]);

    let { data: today, error: errorToday } = todayResult;
    let { data: tomorrow, error: errorTomorrow } = tomorrowResult;
    let { data: menuTimings, error: errorMenuTimings } = menuTimingsResult;

    if (errorToday) throw errorToday;
    if (errorTomorrow) throw errorTomorrow;
    if (errorMenuTimings) throw errorMenuTimings;

    // Create a map for menu timings
    const menuTimingsMap = {};
    menuTimings.forEach((timing) => {
      menuTimingsMap[timing.mealTypeId] = {
        startTime: timing.startTime,
        endTime: timing.endTime,
        rsvpDeadline: timing.rsvpDeadline,
        enabled: timing.enabled,
      };
    });

    // Process data efficiently
    const processMenu = (menu) => {
      return menu.filter((item) => menuTimingsMap[item.mealTypeId]?.enabled).map((item) => {
        const startTime = menuTimingsMap[item.mealTypeId]?.startTime || null;
        const endTime = menuTimingsMap[item.mealTypeId]?.endTime || null;

        return {
          ...item,
          dayOfWeek: getDayNameFromNumber(item.dayOfWeek),
          mealTypeId: getMealTypeFromNumber(item.mealTypeId),
          startTime,
          endTime,
          rsvpDeadline: menuTimingsMap[item.mealTypeId]?.rsvpDeadline || null,
          isCurrent : checkIfCurrent(startTime, endTime),
          created_at: undefined, // Removes created_at field
        };
      });
    };

    let processedToday = today.length > 0 ? processMenu(today) : [];
    let processedTomorrow = tomorrow.length > 0 ? processMenu(tomorrow) : [];

    // Add response field to processedToday from RSVP table
    const { data: rsvpData, error: rsvpError } = await supabaseInstance
      .from("RSVP")
      .select("*")
      .eq("rsvpDate", new Date().toISOString().split("T")[0])
      .eq("customerAuthUID", customerAuthUID);

    if (rsvpError) throw rsvpError;

    const rsvpMap = {};
    rsvpData.forEach((rsvp) => {
      rsvpMap[rsvp.menuId] = rsvp.response;
    });

    processedToday.forEach((menu) => {
      menu.isChoice = rsvpMap[menu.menuId];
    });

    const { data: rsvpDataTomorrow, error: rsvpErrorTomorrow } = await supabaseInstance
      .from("RSVP")
      .select("*")
      .eq("rsvpDate", new Date(new Date().getTime() + 24 * 60 * 60 * 1000).toISOString().split("T")[0])
      .eq("customerAuthUID", customerAuthUID);

    if (rsvpErrorTomorrow) throw rsvpErrorTomorrow;

    const rsvpMapTomorrow = {};
    rsvpDataTomorrow.forEach((rsvp) => {
      rsvpMapTomorrow[rsvp.menuId] = rsvp.response;
    });

    processedTomorrow.forEach((menu) => {
      menu.isChoice = rsvpMapTomorrow[menu.menuId];
    });

    return res.status(200).json({
      success: true,
      today: processedToday,
      tomorrow: processedTomorrow,
    });
  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});


router.post("/updateWeeklyMenu/:menuId", async (req, res) => {
  const { weekNumber, dayOfWeek, mealTypeId, menuDescription, outletId } =
    req.body;
  const { menuId } = req.params;

  try {
    const { error } = await supabaseInstance
      .from("Weekly_Menu")
      .update({ weekNumber, dayOfWeek, mealTypeId, menuDescription, outletId })
      .eq("menuId", menuId);

    if (error) throw error;
  } catch (error) {
    return res.status(500).json({ success: false, error });
  }
});

router.post("/deleteWeeklyMenu/:menuId", async (req, res) => {
  const { menuId } = req.params;

  try {
    const { error } = await supabaseInstance
      .from("Weekly_Menu")
      .delete()
      .eq("menuId", menuId);

    if (error) throw error;
  } catch (error) {
    return res.status(500).json({ success: false, error });
  }

  return res.status(200).json({ success: true });
});

// router.get("/getWeeklyMenu/:outletId", async (req, res) => {
//   const { outletId } = req.params;

//   /* send response in form of array of objects with name of weekNumber each object containing the menu for a week
//   */
//   try {
//     const { data, error } = await supabaseInstance
//       .from("Weekly_Menu")
//       .select("*")
//       .eq("outletId", outletId);

//     if (error) throw error;

//     const weeklyMenu = {};
//     data.forEach((menu) => {
//       if (!weeklyMenu[menu.weekNumber]) {
//         weeklyMenu[menu.weekNumber] = [];
//       }
//       weeklyMenu[menu.weekNumber].push(menu);
//     });

//     return res.status(200).json({ success: true, data: weeklyMenu });
//   } catch (error) {
//     return res.status(500).json({ success: false, error: error.message });
//   }
// });

router.get("/getWeeklyMenu/:outletId", async (req, res) => {
  const { outletId } = req.params;
  let { weekNumber } = req.query;

  // If weekNumber is not provided, get set it to 1
  if (!weekNumber) weekNumber = 1;

  try {
    const { data, error } = await supabaseInstance
      .from("Weekly_Menu")
      .select("*")
      .eq("outletId", outletId)
      .eq("weekNumber", weekNumber)
      .order("weekNumber", { ascending: true })
      .order("dayOfWeek", { ascending: true })
      .order("mealTypeId", { ascending: true });

    if (error) throw error;

    const formattedData = [];

    data.forEach((item) => {
      let entry = formattedData.find(
        (d) =>
          d.dayOfWeek === item.dayOfWeek && d.weekNumber === item.weekNumber
      );

      if (!entry) {
        entry = {
          dayOfWeek: item.dayOfWeek,
          weekNumber: item.weekNumber,
          menuId1: null,
          menuId2: null,
          menuId3: null,
          menuId4: null,
          breakfast: null,
          lunch: null,
          highTea: null,
          dinner: null,
        };
        formattedData.push(entry);
      }

      switch (item.mealTypeId) {
        case 1:
          entry.menuId1 = item.menuId;
          entry.breakfast = item.menuDescription;
          break;
        case 2:
          entry.menuId2 = item.menuId;
          entry.lunch = item.menuDescription;
          break;
        case 3:
          entry.menuId3 = item.menuId;
          entry.highTea = item.menuDescription;
          break;
        case 4:
          entry.menuId4 = item.menuId;
          entry.dinner = item.menuDescription;
          break;
        default:
          break;
      }
    });

    return res.status(200).json({ data: formattedData });
  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * Get the enabled meals for a given outlet
 * @param {String} outletId
 * @returns {Array} enabledMealTypes
 */
router.get("/getEnabledMeals/:outletId", async (req, res) => {
  const { outletId } = req.params;

  try {
    const { data, error } = await supabaseInstance
      .from("Meal_Timings")
      .select("*")
      .eq("outletId", outletId);

    if (error) throw error;

    // send only the enabled mealTypeId in array
    const enabledMeals = data
      .filter((meal) => meal.enabled)
      .map((meal) => meal.mealTypeId);
    
    return res.status(200).json({ success: true, data: enabledMeals });
  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});

router.get("/getSingleMenu/:menuId", async (req, res) => {
  const { menuId } = req.params;

  if(!menuId || menuId == null || menuId == undefined){
    // check if menuId is present in the request{
    return res.status(400).json({ success: false, message: "menuId is required" });
  }

  try {
    const { data, error } = await supabaseInstance
      .from("Weekly_Menu")
      .select("*")
      .eq("menuId", menuId)
      .single();

    if (error) throw error;
    
    const getMealName = (mealTypeId) => {
      switch (mealTypeId) {
        case 1:
          return "Breakfast";
        case 2:
          return "Lunch";
        case 3:
          return "High Tea";
        case 4:
          return "Dinner";
        default:
          return "";
      }
    };
    // get the image url for the Menu_Item table
    const { data: itemData, error: itemError } = await supabaseInstance
      .from("Menu_Item")
      .select("item_image_url")
      .eq("outletId", data.outletId)
      .eq("itemname", getMealName(data.mealTypeId))
      .maybeSingle();

    if (itemError) throw itemError;
    if(data.mealTypeId == 1){
      console.log("getting image for breakfast");
      data.breakfastImage = itemData?.item_image_url;
    }else if(data.mealTypeId == 2){
      console.log("getting image for lunch");
      data.lunchImage = itemData?.item_image_url;
    }else if(data.mealTypeId == 3){
      console.log("getting image for high tea");
      data.highTeaImage = itemData?.item_image_url;
    }else if(data.mealTypeId == 4){
      console.log("getting image for dinner");
      data.dinnerImage = itemData?.item_image_url;
    }


    return res.status(200).json({ success: true, data });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ success: false, error });
  }
});

router.post("/deleteMenu/:menuId", async (req, res) => {
  const { menuId } = req.params;

  try {
    const { data, error } = await supabaseInstance
      .from("Weekly_Menu")
      .select("*")
      .eq("menuId", menuId)
      .single();

    if (error) throw error;

    if (data.length === 0) {
      return res
        .status(404)
        .json({ success: false, message: "Menu not found" });
    }

    const { error: deleteError } = await supabaseInstance
      // delete the menus for whole day
      .from("Weekly_Menu")
      .delete()
      .eq("weekNumber", data.weekNumber)
      .eq("dayOfWeek", data.dayOfWeek)
      .eq("outletId", data.outletId);

    if (deleteError) throw deleteError;

    return res.status(200).json({ success: true });
  } catch (error) {
    return res.status(500).json({ success: false, error });
  }
});

const { scheduleNewNotificationJobs } = require("./messNotifications");
// todo : call the funtion in production
router.post("/setMealTime", async (req, res) => {
  try {
    // let { breakfast, lunch, highTea, dinner, outletId } = req.body;
    let breakfast = req.body.Breakfast;
    let lunch = req.body.Lunch;
    let highTea = req.body.Hightea;
    let dinner = req.body.Dinner;
    let outletId = req.body.outletId;

    if(outletId){
      const { data: outletData, error: outletError } = await supabaseInstance
        .from("Outlet")
        .select("outletName")
        .eq("outletId", outletId)
        .single();

      if (outletError) throw outletError;
      var outeletName = outletData.outletName;

    }

    // check the enabled flag for each meal type. If true then set the timings else remove the timings 
    const mealTimings = [
      {
        mealTypeId: 1,
        startTime: breakfast.startTime,
        endTime: breakfast.endTime,
        rsvpDeadline: breakfast.rsvpDeadline,
        outletId,
        enabled: breakfast.enabled,
      },
      {
        mealTypeId: 2,
        startTime: lunch.startTime,
        endTime: lunch.endTime,
        rsvpDeadline: lunch.rsvpDeadline,
        outletId,
        enabled: lunch.enabled,
      },
      {
        mealTypeId: 3,
        startTime: highTea.startTime,
        endTime: highTea.endTime,
        rsvpDeadline: highTea.rsvpDeadline,
        outletId,
        enabled: highTea.enabled,
      },
      {
        mealTypeId: 4,
        startTime: dinner.startTime,
        endTime: dinner.endTime,
        rsvpDeadline: dinner.rsvpDeadline,
        outletId,
        enabled: dinner.enabled,
      },
    ];

    const result = [];

    const mealTimingsPromises = mealTimings.map(async (mealTiming) => {
      const { mealTypeId, startTime, endTime, rsvpDeadline, outletId, enabled } =
        mealTiming;

      const { data, error } = await supabaseInstance
        .from("Meal_Timings")
        .select("*")
        .eq("outletId", outletId)
        .eq("mealTypeId", mealTypeId)
        .single();

      if (error && error.code !== "PGRST116") throw error; // "PGRST116" is Supabase error for no rows returned

      if (data) {
        // Update the meal timing if it exists
        const { data: updateData, error: updateError } = await supabaseInstance
          .from("Meal_Timings")
          .update({ startTime, endTime, rsvpDeadline, enabled })
          .eq("mealTimingId", data.mealTimingId)
          .select("*")
          .single();

        if (updateError) throw updateError;

        // calling the function to send the notifications
        scheduleNewNotificationJobs(outletId, mealTypeId, rsvpDeadline, outeletName);

        result.push(updateData);
      } else {
        // Insert the meal timing if it does not exist
        const { data: insertData, error: insertError } = await supabaseInstance
          .from("Meal_Timings")
          .insert(mealTiming)
          .select("*")
          .single();

        if (insertError) throw insertError;

        // calling the function to send the notifications
        scheduleNewNotificationJobs(outletId, mealTypeId, rsvpDeadline, outeletName);
        result.push(insertData);
      }
    });

    await Promise.all(mealTimingsPromises);

    return res.status(200).json({
      success: true,
      message: "Meal timings added/updated successfully",
      result,
    });
  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});

router.get("/getMealTime/:outletId", async (req, res) => {
  const { outletId } = req.params;

  try {
    const { data, error } = await supabaseInstance
      .from("Meal_Timings")
      .select("*")
      .eq("outletId", outletId)
      .order("mealTypeId", { ascending: true });

    if (error) throw error;

    return res.status(200).json({ success: true, data });
  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/duplicateMenu/:outletId", async (req, res) => {
  const weekNumber = req.body.weekNumber;
  const toWeekNumber = req.body.toWeekNumber;
  const { outletId } = req.params;

  try {
    // duplicate the menu for the given weekNumber to the toWeekNumber
    const { data, error } = await supabaseInstance
    .from("Weekly_Menu")
    .select("*")
    .eq("weekNumber", weekNumber)
    .eq("outletId", outletId);

    if (error) throw error;
    

    const menuToInsert = data.map((menu) => ({
      weekNumber: toWeekNumber,
      dayOfWeek: menu.dayOfWeek,
      mealTypeId: menu.mealTypeId,
      menuDescription: menu.menuDescription,
      outletId,
    }));

    const result = [];

    const menuPromises = menuToInsert.map(async (menu) => {
      const { weekNumber, dayOfWeek, mealTypeId, menuDescription, outletId } = menu;
     
      const { data, error } = await supabaseInstance
        .from("Weekly_Menu")
        .select("menuId")
        .eq("weekNumber", weekNumber)
        .eq("dayOfWeek", dayOfWeek)
        .eq("mealTypeId", mealTypeId)
        .eq("outletId", outletId)
        .single();

      if (error && error.code !== "PGRST116") throw error; // "PGRST116" is Supabase error for no rows returned
      
      if (data) {
        // Update the menu if it exists
        const { data:updateData , error: updateError } = await supabaseInstance
          .from("Weekly_Menu")
          .update({ menuDescription })
          .eq("menuId", data.menuId)
          .select("*")
          .single();

        if (updateError) throw updateError;
        result.push({ updateData });
      } else {
        // Insert the menu if it does not exist
        const { data: insertData, error: insertError } = await supabaseInstance
          .from("Weekly_Menu")
          .insert(menu)
          .select("*")
          .single();

        if (insertError) throw insertError;
        result.push({insertData});
      }
    });

    await Promise.all(menuPromises);

    return res.status(200).json({ success: true, message: "Weekly menu duplicated successfully", result });
  } catch (error) {
    console.log('error', error);
    return res.status(500).json({ success: false, error});
  }
});

/**
 * Duplicate the weekly menu from one outlet to another
 * @param {fromOutletId} - Outlet ID from which the menu is to be duplicated
 * @param {toOutletId} - Outlet ID to which the menu is to be duplicated
 * @returns {success} - true if the weekly menu is duplicated successfully
 * @returns {message} - Weekly menu duplicated successfully
 * @returns {result} - Result of the weekly menu duplication
 * @returns {error} - Error message if the weekly menu duplication fails
 */
router.post("/duplicateOutletMenu", async (req, res) => {
  const {fromOutletId, toOutletId} = req.body;

  try {
    const { data, error } = await supabaseInstance
    .from("Weekly_Menu")
    .select("*")
    .eq("outletId", fromOutletId);

    if (error) throw error;

    const menuToInsert = data.map((menu) => ({
      weekNumber: menu.weekNumber,
      dayOfWeek: menu.dayOfWeek,
      mealTypeId: menu.mealTypeId,
      menuDescription: menu.menuDescription,
      outletId: toOutletId,
      price: menu.price? menu.price : null,
    }));

    const result = [];

    const menuPromises = menuToInsert.map(async (menu) => {
      const { weekNumber, dayOfWeek, mealTypeId, menuDescription, outletId } = menu;
     
      const { data, error } = await supabaseInstance
        .from("Weekly_Menu")
        .select("menuId")
        .eq("weekNumber", weekNumber)
        .eq("dayOfWeek", dayOfWeek)
        .eq("mealTypeId", mealTypeId)
        .eq("outletId", outletId)
        .single();

      if (error && error.code !== "PGRST116") throw error; // "PGRST116" is Supabase error for no rows returned
      
      if (data) {
        // Update the menu if
        const { data:updateData , error: updateError } = await supabaseInstance
          .from("Weekly_Menu")
          .update({ menuDescription })
          .eq("menuId", data.menuId)
          .select("*")
          .single();
          
        if (updateError) throw updateError;

        result.push({ updateData });
      }
      else {
        // Insert the menu if it does not exist
        const { data: insertData, error: insertError } = await supabaseInstance
          .from("Weekly_Menu")
          .insert(menu)
          .select("*")
          .single();

        if (insertError) throw insertError;
        result.push({insertData});
      }
    });

    await Promise.all(menuPromises);

    // also duplicate the meal timings
    const { data: mealTimings, error: mealTimingsError } = await supabaseInstance
      .from("Meal_Timings")
      .select("*")
      .eq("outletId", fromOutletId);

    if (mealTimingsError) throw mealTimingsError;

    const mealTimingsToInsert = mealTimings.map((timing) => ({
      mealTypeId: timing.mealTypeId,
      startTime: timing.startTime,
      endTime: timing.endTime,
      rsvpDeadline: timing.rsvpDeadline,
      outletId: toOutletId,
      enabled: timing.enabled,
    }));

    const mealTimingResult = [];

    const mealTimingPromises = mealTimingsToInsert.map(async (timing) => {
      const { mealTypeId, startTime, endTime, rsvpDeadline, outletId, enabled } = timing;

      const { data, error } = await supabaseInstance
        .from("Meal_Timings")
        .select("*")
        .eq("outletId", outletId)
        .eq("mealTypeId", mealTypeId)
        .single();

      if (error && error.code !== "PGRST116") throw error; // "PGRST116" is Supabase error for no rows returned

      if (data) {
        // Update the meal timing if it exists
        const { data:updateData , error: updateError } = await supabaseInstance
          .from("Meal_Timings")
          .update({ startTime, endTime, rsvpDeadline, enabled })
          .eq("mealTimingId", data.mealTimingId)
          .select("*")
          .single();

        if (updateError) throw updateError;
        mealTimingResult.push(updateData);
      } else {
        // Insert the meal timing if it does not exist
        const { data: insertData, error: insertError } = await supabaseInstance
          .from("Meal_Timings")
          .insert(timing)
          .select("*")
          .single();

        if (insertError) throw insertError;
        mealTimingResult.push(insertData);
      }
    });

    await Promise.all(mealTimingPromises);
    
    return res.status(200).json({ success: true, message: "Weekly menu duplicated successfully", result });

  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});

/*
Table: Mess_Meal_Review

reviewId          bigint      number    Primary Key, Auto-increment
menuId            bigint      number    Foreign Key -> Weekly_Menu.menuId
customerAuthUID   uuid        string    Foreign Key -> Customer.customerAuthUID  
rating           integer     number    Rating between 1-5
review          text        string    Optional review comment
created_at       timestamp   datetime  Default: now()
updated_at       timestamp   datetime  Default: now()

Constraints:
- Unique constraint on (menuId, customerAuthUID) to prevent multiple reviews
- Check constraint on rating to be between 1 and 5
- Foreign key constraint ensuring customerAuthUID exists in Customer table
- Foreign key constraint ensuring menuId exists in Weekly_Menu table
*/

router.post("/addReview", async (req, res) => {
  const { menuId, customerAuthUID, rating, review, date } = req.body;
  if (rating < 1 || rating > 5)
    return res
      .status(400)
      .json({ success: false, message: "Rating must be between 1 and 5" });

  try {
    // check for existing review for menuId and customerAuthUID for that date
    const { data, error } = await supabaseInstance
      .from("Mess_Meal_Review")
      .select("*")
      .eq("menuId", menuId)
      .eq("customerAuthUID", customerAuthUID)
      .eq("date", date)
      .maybeSingle();

    if (error) throw error;

    // if review exists, update it
    if (data) {
      const { data: updateData, error: updateError } = await supabaseInstance
        .from("Mess_Meal_Review")
        .update({ rating, review })
        .eq("reviewId", data.reviewId)
        .select("*")
        .single();

      if (updateError) throw updateError;
      return res
        .status(200)
        .json({
          success: true,
          message: "Review updated successfully",
          data: updateData,
        });
    }

    // if review does not exist, insert it
    const { data: insertData, error: insertError } = await supabaseInstance
      .from("Mess_Meal_Review")
      .insert({ menuId, customerAuthUID, rating, review, date })
      .select("*")
      .single();

    if (insertError) throw insertError;
    return res
      .status(200)
      .json({
        success: true,
        message: "Review added successfully",
        data: insertData,
      });
  } catch (error) {
    return res.status(500).json({ success: false, error });
  }
});

router.get("/viewMenu/:outletId", async (req, res) => {
  const { outletId } = req.params;

  const { weekNumber, dayOfWeek } = getWeekInfo();
  try {
    // Step 1: Fetch enabled meal timings
    const { data: mealTimings, error: mealTimingsError } = await supabaseInstance
      .from("Meal_Timings")
      .select("*")
      .eq("outletId", outletId)
      .eq("enabled", true);

    if (mealTimingsError) throw mealTimingsError;

    const enabledMealTypeIds = mealTimings.map(timing => timing.mealTypeId);

    // Step 2: Fetch today's menu
    const { data: todayData, error: todayError } = await supabaseInstance
      .from("Weekly_Menu")
      .select("*")
      .eq("outletId", outletId)
      .eq("dayOfWeek", dayOfWeek)
      .eq("weekNumber", weekNumber)
      .in("mealTypeId", enabledMealTypeIds); // Filter by enabled meal types

    if (todayError) throw todayError;

    // Process today's data to match the expected response
    const processedTodayData = todayData.reduce((acc, item) => {
      const mealType = getMealTypeFromNumber(item.mealTypeId);
      if (!acc[mealType]) {
        acc[mealType] = {};
      }
      // Convert dayOfWeek from number to day name
      const dayNames = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];
      const dayName = dayNames[item.dayOfWeek - 1];
      acc[mealType][dayName] = item.menuDescription;
      return acc;
    }, {});

    // Get full week menu (from current day to next week's same day - 1)
    const fullWeekMenu = {
      Breakfast: [],
      Lunch: [],
      HighTea: [],
      Dinner: []
    };

    // Calculate the days we need to fetch
    const daysToFetch = [];
    let currentDayOfWeek = dayOfWeek;
    let currentWeekNumber = weekNumber;

    // Add current day and remaining days of this week
    for (let i = currentDayOfWeek; i <= 7; i++) {
      daysToFetch.push({ dayOfWeek: i, weekNumber: currentWeekNumber });
    }

    // Add days from next week until we reach the day before current day
    if (currentDayOfWeek > 1) {
      const nextWeekNumber = (currentWeekNumber % 5) + 1; // Assuming weekNumber cycles from 1 to 5
      for (let i = 1; i < currentDayOfWeek; i++) {
        daysToFetch.push({ dayOfWeek: i, weekNumber: nextWeekNumber });
      }
    }

    // Fetch all required days
    for (const { dayOfWeek: day, weekNumber: week } of daysToFetch) {
      const { data: dayData, error: dayError } = await supabaseInstance
        .from("Weekly_Menu")
        .select("*")
        .eq("outletId", outletId)
        .eq("dayOfWeek", day)
        .eq("weekNumber", week)
        .in("mealTypeId", enabledMealTypeIds); // Filter by enabled meal types

      if (dayError) throw dayError;

      // Process the data for this day
      dayData.forEach(item => {
        const mealType = getMealTypeFromNumber(item.mealTypeId);
        const dayNames = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];
        const dayName = dayNames[item.dayOfWeek - 1];
        fullWeekMenu[mealType == "High Tea" ? "HighTea" : mealType].push({ [dayName]: item.menuDescription, weekNumber: item.weekNumber, dayofWeek : item.dayOfWeek });
      });
    }

    // Sort the arrays by week number and day number
    Object.keys(fullWeekMenu).forEach(mealType => {
      fullWeekMenu[mealType].sort((a, b) => {
        // Extract week number and day number from the object keys
        const aWeek = a.weekNumber;
        const bWeek = b.weekNumber;
        const aDay = Object.keys(a)[0];
        const bDay = Object.keys(b)[0];
        const dayOrder = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];
        // First, sort by week number
        if (aWeek !== bWeek) return aWeek - bWeek;
        // If week numbers are the same, sort by day order
        return dayOrder.indexOf(aDay) - dayOrder.indexOf(bDay);
      });
    });

    return res.status(200).json({
      success: true,
      weekNumber,
      data: processedTodayData,
      fullWeekMenu
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ success: false, error });
  }
});

router.get("/getMaximumWeekNumberForMonth", async (req, res) => {
  const maximumWeekNumber = getMaximumWeekNumberForMonth();
  return res.status(200).json({ success: true, maximumWeekNumber });
});

module.exports = router;
