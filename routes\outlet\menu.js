var express = require("express");
const { min } = require("moment-timezone");
var router = express.Router();
var supabaseInstance = require("../../services/supabaseClient").supabase;
const multer = require("multer");
const upload = multer();

router.post("/createCategory", async (req, res) => {
  const { outletId, status, categoryname, parent_category_id } = req.body;
  try {
    let postBody = { outletId, status, categoryname };
    if (parent_category_id) {
      postBody.parent_category_id = parent_category_id;
    }
    const { data, error } = await supabaseInstance
      .from("Menu_Categories")
      .insert(postBody)
      .select("*");

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/updateCategory/:categoryid", async (req, res) => {
  const { categoryid } = req.params;
  const menuCategoryData = req.body;
  try {
    const { data, error } = await supabaseInstance
      .from("Menu_Categories")
      .update({ ...menuCategoryData })
      .eq("categoryid", categoryid)
      .select("*");

    if (data) {
      res.status(200).json({
        success: true,
        message: "Data updated succesfully",
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

router.get("/category/:outletId", async (req, res) => {
  const { outletId } = req.params;
  try {
    const { data, error } = await supabaseInstance
      .from("Menu_Categories")
      .select("*, Menu_Item!left(itemid)")
      .eq("outletId", outletId);

    if (data) {
      res.status(200).json({
        success: true,
        data: data.map((m) => ({
          ...m,
          Menu_Item: null,
          Menu_Item_count: m?.Menu_Item?.length || 0,
        })),
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/upsertCategoryImage", upload.single("file"), async (req, res) => {
  const { categoryid } = req.body;
  console.log("categoryid", categoryid);
  try {
    const { data, error } = await supabaseInstance.storage
      .from("category-image")
      .upload(categoryid + ".webp", req.file.buffer, {
        cacheControl: "3600",
        upsert: true,
        contentType: "image/webp",
      });

    if (data?.path) {
      const publickUrlresponse = await supabaseInstance.storage
        .from("category-image")
        .getPublicUrl(data?.path);
      console.log("publickUrlresponse", publickUrlresponse);
      if (publickUrlresponse?.data?.publicUrl) {
        const publicUrl = publickUrlresponse?.data?.publicUrl;
        const menuCategoryData = await supabaseInstance
          .from("Menu_Categories")
          .update({
            category_image_url: `${publicUrl}?${new Date().getTime()}`,
          })
          .eq("categoryid", categoryid)
          .select("*")
          .maybeSingle();
        res.status(200).json({
          success: true,
          data: menuCategoryData.data,
        });
      } else {
        throw publickUrlresponse.error || "Getting Error in PublicUrl";
      }
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
});

router.get("/getAttributes", async (req, res) => {
  try {
    const { data, error } = await supabaseInstance
      .from("Menu_Item_Attributes")
      .select("*");

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/addMenu", async (req, res) => {
  const menuItemData = { ...req.body };
  try {
    if (!menuItemData?.dietary_restriction_id) {
      menuItemData.dietary_restriction_id = null;
    }
    if (!menuItemData?.spice_level_id) {
      menuItemData.spice_level_id = null;
    }
    if (!menuItemData?.petpoojaItemId) {
      menuItemData.petpoojaItemId = null;
    }

    if (menuItemData?.price == "") menuItemData.price = 0;
    if (menuItemData?.minimumpreparationtime == "") menuItemData.minimumpreparationtime = 0;
    if (menuItemData?.attributeid == "") menuItemData.attributeid = 1;
    if (menuItemData?.kcal == "") menuItemData.kcal = 0;
    if (menuItemData?.itemSequenceId == "") menuItemData.itemSequenceId = 0;
    if (menuItemData?.item_categoryid == "") menuItemData.item_categoryid = null;

    // Ensure price is set correctly
    if (!menuItemData.price || menuItemData.price < 0) {
      return res.status(400).json({ success: false, message: "Invalid price." });
    }

    const { data, error } = await supabaseInstance
      .from("Menu_Item")
      .insert(menuItemData)
      .select("*")
      .maybeSingle();

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/updateMenu/:itemid", async (req, res) => {
  const { itemid } = req.params;
  const menuItemData = { ...req.body };

  if (menuItemData?.price == "") menuItemData.price = 0;
  if (menuItemData?.minimumpreparationtime == "") menuItemData.minimumpreparationtime = 0;
  if (menuItemData?.attributeid == "") menuItemData.attributeid = 1;
  if (menuItemData?.kcal == "") menuItemData.kcal = 0;
  if (menuItemData?.itemSequenceId == "") menuItemData.itemSequenceId = 0;
  if (menuItemData?.item_categoryid == "") menuItemData.item_categoryid = null;

  // remove addonGroups and variations from menuItemData
  menuItemData.addonGroups = undefined;
  menuItemData.variations = undefined;
  try {
    if (!menuItemData?.dietary_restriction_id) {
      menuItemData.dietary_restriction_id = null;
    }
    if (!menuItemData?.spice_level_id) {
      menuItemData.spice_level_id = null;
    }

    const { data, error } = await supabaseInstance
      .from("Menu_Item")
      .update({ ...menuItemData })
      .select("*")
      .eq("itemid", itemid);

    if (data) {
      res.status(200).json({
        success: true,
        message: "Data updated succesfully",
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
});

router.get("/getItemList/:outletId", async (req, res) => {
  const { outletId } = req.params;
  try {
    const { data, error } = await supabaseInstance
      .from("Menu_Item")
      .select("*")
      .eq("outletId", outletId)
      .order("itemSequenceId", { ascending: true })
      .eq("isDelete", false);

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

router.get("/getItem/:itemid", async (req, res) => {
  const { itemid } = req.params;
  try {
    const { data: itemData, error: itemError } = await supabaseInstance
      .from("Menu_Item")
      .select("*")
      .eq("itemid", itemid)
      .single();

    if (itemError) throw itemError;

    const { data: variations, error: variationsError } = await supabaseInstance
      .from("Variation")
      .select("*")
      .eq("status", true)
      .eq("itemId", itemid);

    if (variationsError) throw variationsError;

    let variationIds = variations.map((v) => v.variationId);

    const { data: addonGroups, error: addonGroupsError } =
      await supabaseInstance
        .from("ItemAddonGroups")
        .select("*, AddonGroup!inner(*)")
        .or(`itemId.eq.${itemid},variationId.in.(${variationIds.join(",")})`)
        .eq("AddonGroup.active", 1);

    if (addonGroupsError) throw addonGroupsError;

    const addonGroupItems = await Promise.all(
      addonGroups.map(async (ag) => {
        const { data, error } = await supabaseInstance
          .from("Addongroupitems")
          .select("*")
          .eq("active", 1)
          .eq("addonGroupId", ag.AddonGroup.id);
        if (error) throw error;
        return { ...ag, items: data };
      })
    );

    let response = {
      ...itemData,
      variations: variations,
      addonGroups: addonGroups.map(
        (ag) =>
          ({
            id: ag.AddonGroup.id,
            name: ag.AddonGroup.name,
            rank: ag.AddonGroup.rank,
            active: ag.AddonGroup.active,
            groupId: ag.groupId,
            variationId: ag.variationId,
            min_selection: ag.min_selection,
            max_selection: ag.max_selection,
            items:
              addonGroupItems.find((agi) => agi.groupId === ag.AddonGroup.id)
                ?.items || [],
          } || [])
      ),
    };

    res.status(200).json({
      success: true,
      data: response,
    });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});
// try {
//   const { data, error } = await supabaseInstance
//     .from("Menu_Item")
//     .select("*")
//     .eq("itemid", itemid);

//   if (data) {
//     res.status(200).json({
//       success: true,
//       data: data,
//     });
//   } else {
//     throw error;
//   }
// } catch (error) {
//   res.status(500).json({ success: false, error: error.message });
// }
// });

router.post("/upsertMenuItemImage", upload.single("file"), async (req, res) => {
  const { itemid } = req.body;

  try {
    const { data, error } = await supabaseInstance.storage
      .from("menu-item-image")
      .upload(itemid + ".webp", req.file.buffer, {
        cacheControl: "3600",
        upsert: true,
        contentType: "image/webp",
      });

    if (data?.path) {
      const publickUrlresponse = await supabaseInstance.storage
        .from("menu-item-image")
        .getPublicUrl(data?.path);
      if (publickUrlresponse?.data?.publicUrl) {
        const publicUrl = publickUrlresponse?.data?.publicUrl;
        const menuData = await supabaseInstance
          .from("Menu_Item")
          .update({ item_image_url: `${publicUrl}?${new Date().getTime()}` })
          .eq("itemid", itemid)
          .select("*")
          .maybeSingle();
        res.status(200).json({
          success: true,
          data: menuData.data,
        });
      } else {
        throw publickUrlresponse.error || "Getting Error in PublicUrl";
      }
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
});

router.get("/getCategoryById/:categoryid", async (req, res) => {
  const { categoryid } = req.params;
  try {
    const { data, error } = await supabaseInstance
      .from("Menu_Categories")
      .select("*")
      .eq("categoryid", categoryid);

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/createParentCategory", async (req, res) => {
  const { outletId, status, parentCategoryName, category } = req.body;
  try {
    const { data, error } = await supabaseInstance
      .from("Menu_Parent_Categories")
      .insert({ outletId, status, parentCategoryName })
      .select("*");

    if (data) {
      const parent_category_id = data[0].parent_category_id;

      for (let value of category) {
        const updatedData = await supabaseInstance
          .from("Menu_Categories")
          .update({ parent_category_id: parent_category_id })
          .eq("categoryid", value)
          .select("*");
      }
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/updateParentCategory/:parent_category_id", async (req, res) => {
  const { parent_category_id } = req.params;
  const { status, parentCategoryName, category } = req.body;

  try {
    const { data, error } = await supabaseInstance
      .from("Menu_Parent_Categories")
      .update({ status, parentCategoryName })
      .eq("parent_category_id", parent_category_id)
      .select("*");

    if (data) {
      const parent_category_id = data[0].parent_category_id;

      if (category) {
        let updated = await supabaseInstance
          .from("Menu_Categories")
          .update({ parent_category_id: null })
          .eq("parent_category_id", parent_category_id)
          .select("*");

        for (let value of category) {
          const updatedData = await supabaseInstance
            .from("Menu_Categories")
            .update({ parent_category_id: parent_category_id })
            .eq("categoryid", value)
            .select("*");
        }
      }
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

router.get("/getParentCategory/:outletId", async (req, res) => {
  const { outletId } = req.params;
  try {
    const { data, error } = await supabaseInstance
      .from("Menu_Parent_Categories")
      .select("*,Menu_Categories!left(Menu_Item!left(itemid))")
      .eq("outletId", outletId);

    if (data) {
      res.status(200).json({
        success: true,
        data: data.map((m) => ({
          ...m,
          Menu_Categories: null,
          Menu_Item_count:
            m?.Menu_Categories?.map((m) => m?.Menu_Item?.flat())?.flat()
              ?.length || 0,
        })),
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

router.get("/getParentCategoryById/:parent_category_id", async (req, res) => {
  const { parent_category_id } = req.params;
  try {
    const { data, error } = await supabaseInstance
      .from("Menu_Parent_Categories")
      .select("*, Menu_Categories!left(*)")
      .eq("parent_category_id", parent_category_id);

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post(
  "/upsertParentCategoryImage",
  upload.single("file"),
  async (req, res) => {
    const { parent_category_id } = req.body;
    try {
      const { data, error } = await supabaseInstance.storage
        .from("category-image")
        .upload(parent_category_id + ".webp", req.file.buffer, {
          cacheControl: "3600",
          upsert: true,
          contentType: "image/webp",
        });

      if (data?.path) {
        const publickUrlresponse = await supabaseInstance.storage
          .from("category-image")
          .getPublicUrl(data?.path);
        if (publickUrlresponse?.data?.publicUrl) {
          const publicUrl = publickUrlresponse?.data?.publicUrl;
          const parentCategoryData = await supabaseInstance
            .from("Menu_Parent_Categories")
            .update({
              parent_category_image_url: `${publicUrl}?${new Date().getTime()}`,
            })
            .eq("parent_category_id", parent_category_id)
            .select("*")
            .maybeSingle();
          res.status(200).json({
            success: true,
            data: parentCategoryData.data,
          });
        } else {
          throw publickUrlresponse.error || "Getting Error in PublicUrl";
        }
      } else {
        throw error;
      }
    } catch (error) {
      res.status(500).json({ success: false, error: error });
    }
  }
);

router.delete("/deleteCategory/:categoryid", async (req, res) => {
  const { categoryid } = req.params;
  try {
    const { data, error } = await supabaseInstance
      .from("Menu_Categories")
      .delete()
      .eq("categoryid", categoryid)
      .select("*");

    if (data) {
      res.status(200).json({
        success: true,
        message: "Category Deleted",
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
});

router.post("/deleteMenuItem/:itemid", async (req, res) => {
  const { itemid } = req.params;
  try {
    const { data, error } = await supabaseInstance
      .from("Menu_Item")
      .update({ isDelete: true })
      .select("*")
      .eq("itemid", itemid);

    if (data) {
      res.status(200).json({
        success: true,
        message: "Menu Item Updated",
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error });
  }
});

// ======================Variation=======================

// router.post("/addVariation", async (req, res) => {
//   let { name, attributeId, price, status, itemIdArray, outletId } = req.body;

//   if (!name || !attributeId || !price || !status || !itemIdArray) {
//     return res.status(400).json({
//       success: false,
//       error: "Please provide all required fields",
//     });
//   }

//   // convert price and attributeId to integer
//   price = parseInt(price);
//   attributeId = parseInt(attributeId);

//   try {
//     let result = [];
//     for (let itemId of itemIdArray) {
//       const { data, error } = await supabaseInstance
//         .from("Variation")
//         .insert({ name, attributeId, price, status, itemId, outletId })
//         .select("*")
//         .single();

//       if (data) {
//         result.push(data);
//       } else {
//         throw error;
//       }
//     }

//     if (result.length > 0) {
//       res.status(200).json({
//         success: true,
//         data: data,
//       });
//     } else {
//       throw error;
//     }
//   } catch (error) {
//     res.status(500).json({ success: false, error: error.message });
//   }
// });

// router.post("/addVariation", async (req, res) => {
//   let { name, attributeId, price, status, itemIdArray, outletId } = req.body;

//   // Validate input
//   if (!name || !attributeId || !price || !status || !Array.isArray(itemIdArray) || itemIdArray.length === 0) {
//     return res.status(400).json({
//       success: false,
//       error: "Please provide all required fields",
//     });
//   }

//   // Convert price and attributeId to integers
//   price = parseInt(price);
//   attributeId = parseInt(attributeId);

//   // Create variations data for batch insert
//   const variations = itemIdArray.map(itemId => ({
//     name,
//     attributeId,
//     price,
//     status,
//     itemId,
//     outletId,
//     itemArray : itemIdArray
//   }));

//   try {
//     // Insert all variations in a single operation
//     const { data, error } = await supabaseInstance
//       .from("Variation")
//       .insert(variations)
//       .select("*");

//     if (error) throw error;

//     res.status(200).json({
//       success: true,
//       data,
//     });
//   } catch (error) {
//     res.status(500).json({
//       success: false,
//       error: error.message || "An error occurred",
//     });
//   }
// });

// router.post("/updateVariation/:variationId", async (req, res) => {
//   const { variationId } = req.params;
//   const variationData = req.body;

//   try {
//     const { data, error } = await supabaseInstance
//       .from("Variation")
//       .update({ ...variationData })
//       .eq("variationId", variationId)
//       .select("*")
//       .single();

//     if (data) {
//       res.status(200).json({
//         success: true,
//         message: "Data updated succesfully",
//         data: data,
//       });
//     } else {
//       throw error;
//     }
//   } catch (error) {
//     console.log('Not found...');

//     res.status(500).json({ success: false, error: "Variation Not Found..." });
//   }
// });

// router.post('/updateVariation/:variationId', async (req, res) => {
//   const { variationId } = req.params;
//   let { name, price, attributeId, status, isDefault, outletId, itemArray } = req.body;

//   if (!name || !price || !attributeId || status === undefined || isDefault === undefined || !outletId || !itemArray || !Array.isArray(itemArray)) {
//     return res.status(400).json({
//       success: false,
//       error: 'Please provide all required fields and ensure itemArray is an array'
//     });
//   }

//   // Ensure itemArray elements are numbers
//   itemArray = itemArray.map(Number);
//   console.log('itemArray ===> ', itemArray);

//   try {
//     // Fetch existing variation based on variationId
//     const { data: existingVariation, error: fetchError } = await supabaseInstance
//       .from('Variation')
//       .select('*')
//       .eq('variationId', variationId)
//       .single();

//     if (fetchError) throw fetchError;

//     // Ensure the itemArray is the same for all updates
//     const oldItemArray = existingVariation.itemArray;
//     console.log('oldItemArray ===> ', oldItemArray);

//     let formattedItemArray = `{${itemArray.join(',')}}`;

//     // Update the variation with the new values
//     const { data, error } = await supabaseInstance
//       .from('Variation')
//       .update({
//         name,
//         price,
//         attributeId,
//         status,
//         isDefault,
//         outletId,
//         itemArray:  formattedItemArray// Format array for PostgreSQL
//       })
//       .eq('name' , existingVariation.name)
//       .eq('itemArray', formattedItemArray)
//       .select();

//     console.log('data ===> ', data);

//     if (error) throw error;

//     res.status(200).json({
//       success: true,
//       message: 'Variations updated successfully',

//     });
//   } catch (error) {
//     res.status(500).json({ success: false, error: error.message });
//   }
// });

// router.post("/updateVariation/:variationId", async (req, res) => {
//   const { variationId } = req.params;
//   let { name, price, attributeId, status, isDefault, outletId, itemArray } =
//     req.body;

//   if (
//     !name ||
//     !price ||
//     !attributeId ||
//     status === undefined ||
//     isDefault === undefined ||
//     !outletId ||
//     !itemArray ||
//     !Array.isArray(itemArray)
//   ) {
//     return res.status(400).json({
//       success: false,
//       error:
//         "Please provide all required fields and ensure itemArray is an array",
//     });
//   }

//   // Ensure itemArray elements are numbers
//   itemArray = itemArray.map((num) => String(num));

//   try {
//     // Fetch existing variations based on variationId
//     const { data: existingVariations, error: fetchError } =
//       await supabaseInstance
//         .from("Variation")
//         .select("*")
//         .eq("variationId", variationId)
//         .single();

//     if (fetchError) throw fetchError;

//     const oldItemArray = existingVariations.itemArray;

//     const itemsToAdd = itemArray.filter(
//       (itemId) => !oldItemArray.includes(itemId)
//     );
//     const itemsToRemove = oldItemArray.filter(
//       (itemId) => !itemArray.includes(itemId)
//     );

//     // Add new variation records for new items
//     for (let itemId of itemsToAdd) {
//       const { error: insertError } = await supabaseInstance
//         .from("Variation")
//         .insert({
//           name,
//           price,
//           attributeId,
//           status,
//           isDefault,
//           outletId,
//           itemId,
//           itemArray: `{${itemArray.join(",")}}`, // Format array for PostgreSQL
//         });

//       if (insertError) throw insertError;

//       // fetch the item and update the isAllowVariation field
//       const { error: updateFlagError } = await supabaseInstance
//         .from("Menu_Item")
//         .update({ itemallowvariation: true })
//         .eq("itemid", itemId)
//         .single();

//       if (updateFlagError) throw updateFlagError;
//     }

//     // Remove variation records for items that are no longer in the array
//     for (let itemId of itemsToRemove) {
//       const { error: deleteError } = await supabaseInstance
//         .from("Variation")
//         .delete()
//         .eq("itemId", itemId)
//         .eq("name", existingVariations[0].name)
//         .eq("attributeId", existingVariations[0].attributeId);

//       if (deleteError) throw deleteError;

//       // fetch the item and update the isAllowVariation field
//       const { error: updateFlagError } = await supabaseInstance
//         .from("Menu_Item")
//         .update({ itemallowvariation: false })
//         .eq("itemid", itemId)
//         .single();

//       if (updateFlagError) throw updateFlagError;
//     }

//     return res.status(200).json({
//       success: true,
//       message: "Variations updated successfully",
//     });
//   } catch (error) {
//     res.status(500).json({ success: false, error: error.message });
//   }
// });

router.post("/addVariation", async (req, res) => {
  const { name, price, attributeId, status, isDefault, outletId, itemArray } =
    req.body;

  if (
    !name ||
    !price ||
    !attributeId ||
    status === undefined ||
    isDefault === undefined ||
    !outletId ||
    !itemArray ||
    !Array.isArray(itemArray)
  ) {
    return res.status(400).json({
      success: false,
      error:
        "Please provide all required fields and ensure itemArray is an array",
    });
  }

  try {
    const variations = itemArray.map((itemId) => ({
      name,
      price,
      attributeId,
      status,
      isDefault,
      outletId,
      itemId,
      itemArray,
    }));

    const { data, error } = await supabaseInstance
      .from("Variation")
      .insert(variations)
      .select();

    if (error) throw error;

    itemArray.map(async (itemId) => {
      // fetch the item and update the isAllowVariation field
      const { error: updateFlagError } = await supabaseInstance
        .from("Menu_Item")
        .update({ itemallowvariation: "1" })
        .eq("itemid", itemId);

      if (updateFlagError) throw updateFlagError;
    });

    res.status(200).json({
      success: true,
      message: "Variations created successfully",
      data: data,
    });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/updateVariation/:variationId", async (req, res) => {
  // aa work kare che
  const { variationId } = req.params;
  const { name, price, attributeId, status, isDefault, outletId, itemArray } =
    req.body;

  if (
    !name ||
    !price ||
    !attributeId ||
    status === undefined ||
    isDefault === undefined ||
    !outletId ||
    !itemArray ||
    !Array.isArray(itemArray)
  ) {
    return res.status(400).json({
      success: false,
      error:
        "Please provide all required fields and ensure itemArray is an array",
    });
  }

  try {
    // get the variation from the database
    const { data: variationData, error: fetchError } = await supabaseInstance
      .from("Variation")
      .select("*")
      .eq("variationId", variationId)
      .single();

    if (fetchError) throw fetchError;

    // get the old itemArray
    const oldItemArray = variationData.itemArray;
    let formattedItemArray = `{${oldItemArray.join(",")}}`;

    // return console.log('oldItemArray ===> ', formattedItemArray);

    // get the records which have same itemArray and name as the existing variationData
    const { data: existingVariations, error: fetchExistingVariationsError } =
      await supabaseInstance
        // .from("Variation")
        // .select("*")
        // .eq("name", variationData.name)
        // .eq("itemArray", formattedItemArray);
        // update
        .from("Variation")
        .update({
          name,
          price,
          attributeId,
          status: status == true ? 1 : 0,
          isDefault,
          outletId,
          itemArray: `{${itemArray.join(",")}}`,
        })
        .eq("name", variationData.name)
        .eq("itemArray", formattedItemArray)
        .select();

    if (fetchExistingVariationsError) throw fetchExistingVariationsError;

    // get the new itemArray
    const newItemArray = itemArray;
    let formattedNewItemArray = `{${newItemArray.join(",")}}`;

    // get the items to add
    const itemsToAdd = newItemArray.filter(
      (itemId) => !oldItemArray.includes(itemId)
    );

    // get the items to remove
    const itemsToRemove = oldItemArray.filter(
      (itemId) => !newItemArray.includes(itemId)
    );

    // add new variations for new items
    for (let itemId of itemsToAdd) {
      const { error: insertError } = await supabaseInstance
        .from("Variation")
        .insert({
          name,
          price,
          attributeId,
          status,
          isDefault,
          outletId,
          itemId,
          itemArray: formattedNewItemArray,
        });

      if (insertError) throw insertError;

      // fetch the item and update the isAllowVariation field
      const { error: updateFlagError } = await supabaseInstance
        .from("Menu_Item")
        .update({ itemallowvariation: "1" })
        .eq("itemid", itemId);

      if (updateFlagError) throw updateFlagError;
    }

    // remove variations for items that are no longer in the array
    for (let itemId of itemsToRemove) {
      const { error: deleteError } = await supabaseInstance
        .from("Variation")
        .delete()
        .eq("itemId", itemId)
        .eq("name", variationData.name)
        .eq("attributeId", variationData.attributeId);

      if (deleteError) throw deleteError;

      // fetch the item and update the isAllowVariation field
      const { error: updateFlagError } = await supabaseInstance
        .from("Menu_Item")
        .update({ itemallowvariation: "0" })
        .eq("itemid", itemId);

      if (updateFlagError) throw updateFlagError;
    }
    return res.status(200).json({
      success: true,
      message: "Variations updated successfully",
    });
  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});

router.get("/getVariationsForOutlet/:outletId", async (req, res) => {
  const { outletId } = req.params;
  try {
    const { data, error } = await supabaseInstance
      .from("Variation")
      .select("*")
      .eq("outletId", outletId);

      // remove the records with same (name and outletId and price and itemArray(int8[])) from the response
      // let filteredData = data.filter((v, i, a) => a.findIndex(t => (t.name === v.name && t.outletId === v.outletId && t.price === v.price)) === i);
      let filteredData = data.filter((v, i, a) => a.findIndex(t => (t.name === v.name && t.outletId === v.outletId && t.price === v.price)) === i);


    if (filteredData) {
      res.status(200).json({
        success: true,
        lengthact : data.length,
        lengthfilter : filteredData.length,
        data: filteredData,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

router.get("/getVariation/:variationId", async (req, res) => {
  const { variationId } = req.params;
  try {
    const { data, error } = await supabaseInstance
      .from("Variation")
      .select("*")
      .eq("variationId", variationId)
      .single();

    if (data) {
      res.status(200).json({
        success: true,
        data: data,
      });
    } else {
      throw error;
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/deleteVariation/:variationId", async (req, res) => {
  const { variationId } = req.params;

  try {
    // Fetch and delete in a single operation, but ensure deletion doesn't happen if isDefault
    const { data, error } = await supabaseInstance
      .from("Variation")
      .select("*")
      .eq("variationId", variationId)
      .single();

    if (error) throw error;

    let variationIds = [];

    let formattedItemArray = `{${data.itemArray.join(",")}}`;

    // find all variations with the same name and itemArray
    const { data: variationsdata, error: fetchError } = await supabaseInstance
      .from("Variation")
      .select("variationId")
      .eq("name", data.name)
      .eq("itemArray", formattedItemArray);

    if (fetchError) throw fetchError;

    variationsdata.map((v) => {
      variationIds.push(v.variationId);
    });
    // return res.status(200).json({ data: variationsdata });

    await Promise.all(
      variationIds.map(async (variationId) => {
        const { data:itemIdd ,error: deleteError } = await supabaseInstance
          .from("Variation")
          .delete()
          .eq("variationId", variationId)
          .select("itemId")
          .single();

        if (deleteError) throw deleteError;

        console.log("itemIdd ===> ", itemIdd)

        // fetch the item and update the isAllowVariation field if no other variations exist for that itemid
        const { data: itemIdVariationData, error: fetchItemError } = await supabaseInstance
          .from("Variation")
          .select("itemId")
          .eq("itemId", itemIdd.itemId)
        
        if (fetchItemError) throw fetchItemError;

        console.log('itemIdVariationData ===> ', itemIdVariationData);
        

        if (itemIdVariationData.length == 0){
          const { error: updateFlagError } = await supabaseInstance
          .from("Menu_Item")
          .update({ itemallowvariation: "0" })
          .eq("itemid", itemIdd.itemId)
          .single();

          if (updateFlagError) throw updateFlagError;
        }

      })
    );

    res.status(200).json({
      success: true,
      message: "Variation Deleted"
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message || "An error occurred",
    });
  }
});

router.get("/getCategoriesWithItems/:outletId", async (req, res) => {
  const { outletId } = req.params;
  try {
    const { data: itemData, error: itemError } = await supabaseInstance
      .from("Menu_Item")
      .select("*")
      .eq("isDelete", false)
      .eq("outletId", outletId);

    if (itemError) throw itemError;

    const { data: categoryData, error: categoryError } = await supabaseInstance
      .from("Menu_Categories")
      .select("*")
      .eq("outletId", outletId);

    if (categoryError) throw categoryError;

    const { data: variationData, error: variationError } =
      await supabaseInstance
        .from("Variation")
        .select("name , variationId, itemId")
        .eq("status", true)
        .eq("outletId", outletId);

    if (variationError) throw variationError;

    const result = categoryData.map((category) => {
      const items = itemData.filter(
        (item) => item.item_categoryid === category.categoryid
      );
      return { ...category, items };
    });

    // add variations to items
    for (let item of itemData) {
      item.variations = variationData.filter(
        (variation) => variation.itemId === item.itemid
      );
    }

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/toggleVariationStatus/:variationId", async (req, res) => {
  const { variationId } = req.params;
  try {
    const { data, error } = await supabaseInstance
      .from("Variation")
      .select("status, itemArray, name, outletId")
      .eq("variationId", variationId)
      .single();

    if (error) {
      console.error("error while fetching variation status==>", error);
      throw error
    };

    const newStatus = !data.status;
    const itemArray = data.itemArray.map(Number);
    
    if(itemArray.length > 0){
    const { error: updateError } = await supabaseInstance
      .from("Variation")
      .update({ status: newStatus })
      .in("itemId", itemArray)
      .eq("name", data.name)
      .eq("outletId", data.outletId);

    if (updateError) {
      console.error("error while updating variation status==>", updateError);
      throw updateError;
      }
    }

    res.status(200).json({
      success: true,
      message: "Variation status updated successfully",
    });
  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});
  


// ======================/Variation=======================

// ======================Addon Group=======================

// router.post("/createAddonGroupWithItems", async (req, res) => {
//   const { addonGroup, addonItems } = req.body;

//   if (!addonGroup || !addonItems || !Array.isArray(addonItems)) {
//     return res.status(400).json({
//       success: false,
//       error: "Please provide addon group and addon items data",
//     });
//   }

//   const { name, rank, active, outletId } = addonGroup;

//   if (!name || active == null || !outletId) {
//     return res.status(400).json({
//       success: false,
//       error: "Addon group fields are missing",
//     });
//   }

//   const insertData = async () => {
//     // Begin transaction
//     const { data: addonGroupData, error: addonGroupError } =
//       await supabaseInstance
//         .from("AddonGroup")
//         .insert([{ name, rank, active, outletId }])
//         .select("*")
//         .single();

//     if (addonGroupError) throw addonGroupError;

//     const addonGroupId = addonGroupData.id;

//     // Insert addon items
//     const addonItemsData = addonItems.map((item) => ({
//       ...item,
//       addonGroupId,
//     }));

//     const { data: itemData, error: addonItemsError } = await supabaseInstance
//       .from("Addongroupitems")
//       .insert(addonItemsData)
//       .select("*");

//     if (addonItemsError) throw addonItemsError;

//     addonGroupData.items = itemData;

//     return addonGroupData;
//   };

//   try {
//     const result = await insertData();
//     res.status(200).json({
//       success: true,
//       data: result,
//     });
//   } catch (error) {
//     res.status(500).json({
//       success: false,
//       error: error.message || "An unexpected error occurred",
//     });
//   }
// });

// router.post("/addAddonGroup", async (req, res) => {
//   let { name, rank, active, outletId } = req.body;
//   let { selectedItems, selectedVariations, min_selection, max_selection } =
//     req.body;

//   if (!name || !rank || active === undefined || !outletId) {
//     return res.status(400).json({
//       success: false,
//       error: "Please provide all required fields",
//     });
//   }

//   // console.log("selectedItems ===> ", selectedItems);
//   // console.log("selectedVariations ===> ", selectedVariations);

//   try {
//     const { data: addonGroupData, error: addonGroupError } =
//       await supabaseInstance
//         .from("AddonGroup")
//         .insert({ name, rank, active: active === true ? 1 : 0, outletId })
//         .select()
//         .single();

//     if (addonGroupError) throw addonGroupError;

//     const addonGroupId = addonGroupData.id;

//     // Insert into ItemAddonGroups into elements of selectedItems and selectedVariations
//     let itemAddonGroups = [];
//     for (let itemId of selectedItems) {
//       try {
//         itemAddonGroups.push({
//           addongroup_name: name,
//           itemId,
//           groupId: addonGroupId,
//           min_selection,
//           max_selection,
//         });
//       } catch (error) {
//         console.log("error ===> ", error);
//       }
//     }

//     for (let variationId of selectedVariations) {
//       itemAddonGroups.push({
//         addongroup_name: name,
//         variationId,
//         groupId: addonGroupId,
//         min_selection,
//         max_selection,
//       });
//     }

//     try {
//       const { data: itemAddonGroupsData, error: itemAddonGroupsError } =
//         await supabaseInstance
//           .from("ItemAddonGroups")
//           .insert(itemAddonGroups)
//           .select();

//       if (itemAddonGroupsError) throw itemAddonGroupsError;

//       selectedItems.map(async (itemId) => {
//         // fetch the item and update the isAllowVariation field
//         const { error: updateFlagError } = await supabaseInstance
//           .from("Menu_Item")
//           .update({ itemallowaddon: "1", itemaddonbasedon: "0" })
//           .eq("itemid", itemId);

//         if (updateFlagError) throw updateFlagError;
//       });

//       selectedVariations.map(async (variationId) => {
//         // fetch the item and update the isAllowVariation field
//         const { error: updateFlagError } = await supabaseInstance
//           .from("Variation")
//           .update({ itemallowaddon: "1", itemaddonbasedon: "1" })
//           .eq("variationId", variationId);

//         if (updateFlagError) throw updateFlagError;
//       });

//       const result = {
//         ...addonGroupData,
//         items: itemAddonGroupsData,
//       };

//       res.status(200).json({
//         success: true,
//         message: "Addon group created successfully",
//         data: result,
//       });
//     } catch (error) {
//       res.status(500).json({ success: false, error: error.message });
//     }
//   } catch (error) {
//     res.status(500).json({ success: false, error: error.message });
//   }
// });

// router.post("/updateAddonGroup/:addonGroupId", async (req, res) => {
//   // aa work kare che 2
//   const { addonGroupId } = req.params;
//   let {
//     name,
//     rank,
//     active,
//     outletId,
//     selectedItems,
//     selectedVariations,
//     min_selection,
//     max_selection,
//   } = req.body;

//   if (!name || !rank || active === undefined || !outletId) {
//     return res.status(400).json({
//       success: false,
//       error: "Please provide all required fields",
//     });
//   }

//   // update the addon group
//   try {
//     const { data: updatedAddonGroupData, error: updateAddonGroupError } =
//       await supabaseInstance
//         .from("AddonGroup")
//         .update({ name, rank, active: active == true ? 1 : 0 })
//         .eq("id", addonGroupId)
//         .select("*")
//         .single();

//     if (updateAddonGroupError) throw updateAddonGroupError;

//     // get the itemAddonGroups and push it to oldSelectedItems and oldSelectedVariations
//     const { data: itemAddonGroupsData, error: itemAddonGroupsError } =
//       await supabaseInstance
//         .from("ItemAddonGroups")
//         .update({ addongroup_name: name, min_selection, max_selection })
//         .eq("groupId", addonGroupId)
//         .select("*");

//     if (itemAddonGroupsError) throw itemAddonGroupsError;

//     let oldSelectedItems = [];
//     let oldSelectedVariations = [];

//     console.log("1501");

//     itemAddonGroupsData.map((iag) => {
//       if (iag.itemId) {
//         oldSelectedItems.push(iag.itemId);
//       } else {
//         oldSelectedVariations.push(iag.variationId);
//       }
//     });

//     let itemsToAdd = selectedItems.filter(
//       (item) => !oldSelectedItems.includes(item)
//     );

//     let itemsToRemove = oldSelectedItems.filter(
//       (item) => !selectedItems.includes(item)
//     );

//     let variationsToAdd = selectedVariations.filter(
//       (variation) => !oldSelectedVariations.includes(variation)
//     );

//     let variationsToRemove = oldSelectedVariations.filter(
//       (variation) => !selectedVariations.includes(variation)
//     );

//     // add itemToAdd
//     if (itemsToAdd.length > 0) {
//       itemsToAdd.map(async (itemId) => {
//         try {
//           const { error: insertError } = await supabaseInstance
//             .from("ItemAddonGroups")
//             .insert({
//               addongroup_name: name,
//               itemId,
//               groupId: addonGroupId,
//               min_selection,
//               max_selection,
//             });

//           if (insertError) throw insertError;
//           // fetch the item and update the itemallowaddon and itemaddonbasedon field
//           const { error: updateFlagError } = await supabaseInstance
//             .from("Menu_Item")
//             .update({ itemallowaddon: "1", itemaddonbasedon: "0" })
//             .eq("itemid", itemId);

//           if (updateFlagError) throw updateFlagError;
//         } catch (error) {
//           return res.status(500).json({ success: false, error: error.message });
//         }
//       });
//     }

//     // remove itemsToRemove
//     if (itemsToRemove.length > 0) {
//       itemsToRemove.map(async (itemId) => {
//         try {
//           const { error: deleteError } = await supabaseInstance
//             .from("ItemAddonGroups")
//             .delete()
//             .eq("itemId", itemId)
//             .eq("groupId", addonGroupId);

//           if (deleteError) throw deleteError;

//           // fetch the item and update the itemallowaddon and itemaddonbasedon field
//           const { error: updateFlagError } = await supabaseInstance
//             .from("Menu_Item")
//             .update({ itemallowaddon: "0", itemaddonbasedon: "0" })
//             .eq("itemid", itemId);

//           if (updateFlagError) throw updateFlagError;
//         } catch (error) {
//           return res.status(500).json({ success: false, error: error.message });
//         }
//       });
//     }

//     // add variationsToAdd
//     if (variationsToAdd.length > 0) {
//       variationsToAdd.map(async (variationId) => {
//         try {
//           const { error: insertError } = await supabaseInstance
//             .from("ItemAddonGroups")
//             .insert({
//               addongroup_name: name,
//               variationId,
//               groupId: addonGroupId,
//               min_selection,
//               max_selection,
//             });

//           if (insertError) throw insertError;

//           // fetch the item and update the itemallowaddon and itemaddonbasedon field
//           const { error: updateFlagError } = await supabaseInstance
//             .from("Menu_Item")
//             .update({ itemallowaddon: "1", itemaddonbasedon: "1" })
//             .eq("itemid", itemId);

//           if (updateFlagError) throw updateFlagError;
//         } catch (error) {
//           return res.status(500).json({ success: false, });
//         }
//       });
//     }

//     // remove variationsToRemove
//     if (variationsToRemove.length > 0) {
//       variationsToRemove.map(async (variationId) => {
//         try {
//           const { error: deleteError } = await supabaseInstance
//             .from("ItemAddonGroups")
//             .delete()
//             .eq("variationId", variationId)
//             .eq("groupId", addonGroupId);

//           if (deleteError) throw deleteError;

//           // fetch the item and update the itemallowaddon and itemaddonbasedon field
//           const { error: updateFlagError } = await supabaseInstance
//             .from("Menu_Item")
//             .update({ itemallowaddon: "0", itemaddonbasedon: "0" })
//             .eq("itemid", itemId);

//           if (updateFlagError) throw updateFlagError;
//         } catch (error) {
//           return res.status(500).json({ success: false, error: error.message });
//         }
//       });
//     }

//     const result = {
//       ...updatedAddonGroupData,
//       items: itemAddonGroupsData,
//     };

//     res.status(200).json({
//       success: true,
//       message: "Addon group updated successfully",
//       data: result,
//     });
//   } catch (error) {
//     res.status(500).json({ success: false, error: error.message });
//   }
// });

// router.post("/updateAddonGroup/:addonGroupId", async (req, res) => {
//   const { addonGroupId } = req.params;
//   const {
//     name,
//     rank,
//     active,
//     outletId,
//     selectedItems,
//     selectedVariations,
//     min_selection,
//     max_selection,
//   } = req.body;

//   if (!name || !rank || active === undefined || !outletId) {
//     return res.status(400).json({
//       success: false,
//       error: "Please provide all required fields",
//     });
//   }

//   try {
//     // Update the addon group
//     const { data: addonGroupData, error: addonGroupError } =
//       await supabaseInstance
//         .from("AddonGroup")
//         .update({ name, rank, active: active ? 1 : 0 })
//         .eq("id", addonGroupId)
//         .select("*")
//         .single();

//     if (addonGroupError) throw addonGroupError;

//     // Get the existing item addon groups
//     const { data: itemAddonGroupsData, error: itemAddonGroupsError } =
//       await supabaseInstance
//         .from("ItemAddonGroups")
//         .select("*")
//         .eq("groupId", addonGroupId);

//     if (itemAddonGroupsError) throw itemAddonGroupsError;

//     const oldSelectedItems = itemAddonGroupsData
//       .filter((iag) => iag.itemId)
//       .map((iag) => iag.itemId);
//     const oldSelectedVariations = itemAddonGroupsData
//       .filter((iag) => iag.variationId)
//       .map((iag) => iag.variationId);

//     const itemsToAdd = selectedItems.filter(
//       (item) => !oldSelectedItems.includes(item)
//     );
//     const itemsToRemove = oldSelectedItems.filter(
//       (item) => !selectedItems.includes(item)
//     );
//     const variationsToAdd = selectedVariations.filter(
//       (variation) => !oldSelectedVariations.includes(variation)
//     );
//     const variationsToRemove = oldSelectedVariations.filter(
//       (variation) => !selectedVariations.includes(variation)
//     );

//     // Add new items and variations
//     const addItemsAndVariations = async (type, items) => {
//       for (const id of items) {
//         const { error: insertError } = await supabaseInstance
//           .from("ItemAddonGroups")
//           .insert({
//             addongroup_name: name,
//             [type === "item" ? "itemId" : "variationId"]: id,
//             groupId: addonGroupId,
//             min_selection,
//             max_selection,
//           });

//         if (insertError) throw insertError;

//         const { error: updateFlagError } = await supabaseInstance
//           .from("Menu_Item")
//           .update({
//             itemallowaddon: "1",
//             itemaddonbasedon: type === "item" ? "0" : "1",
//           })
//           .eq("itemid", id);

//         if (updateFlagError) throw updateFlagError;
//       }
//     };

//     // Remove old items and variations
//     const removeItemsAndVariations = async (type, items) => {
//       for (const id of items) {
//         const { error: deleteError } = await supabaseInstance
//           .from("ItemAddonGroups")
//           .delete()
//           .eq(`${type}Id`, id)
//           .eq("groupId", addonGroupId);

//         if (deleteError) throw deleteError;

//         const { error: updateFlagError } = await supabaseInstance
//           .from("Menu_Item")
//           .update({ itemallowaddon: "0", itemaddonbasedon: "0" })
//           .eq("itemid", id);

//         if (updateFlagError) throw updateFlagError;
//       }
//     };

//     await Promise.all([
//       addItemsAndVariations("item", itemsToAdd),
//       addItemsAndVariations("variation", variationsToAdd),
//       removeItemsAndVariations("item", itemsToRemove),
//       removeItemsAndVariations("variation", variationsToRemove),
//     ]);

//     res.status(200).json({
//       success: true,
//       message: "Addon group updated successfully",
//       data: {
//         ...addonGroupData,
//         items: itemAddonGroupsData,
//       },
//     });
//   } catch (error) {
//     res.status(500).json({ success: false, error: error.message });
//   }
// });

router.post("/addAddonGroup", async (req, res) => {
  const {
    name,
    rank,
    active,
    outletId,
    selectedItems,
    selectedVariations,
    min_selection,
    max_selection,
  } = req.body;

  if (!name || !rank || active === undefined || !outletId) {
    return res.status(400).json({
      success: false,
      error: "Please provide all required fields",
    });
  }

  try {
    const { data: addonGroupData, error: addonGroupError } =
      await supabaseInstance
        .from("AddonGroup")
        .insert({ name, rank, active: active ? 1 : 0, outletId })
        .select()
        .single();

    if (addonGroupError) throw addonGroupError;

    const addonGroupId = addonGroupData.id;

    const itemAddonGroups = [
      ...selectedItems.map((itemId) => ({
        addongroup_name: name,
        itemId,
        groupId: addonGroupId,
        min_selection,
        max_selection,
      })),
      ...selectedVariations.map((variationId) => ({
        addongroup_name: name,
        variationId,
        groupId: addonGroupId,
        min_selection,
        max_selection,
      })),
    ];

    const { data: itemAddonGroupsData, error: itemAddonGroupsError } =
      await supabaseInstance
        .from("ItemAddonGroups")
        .insert(itemAddonGroups)
        .select();

    if (itemAddonGroupsError) throw itemAddonGroupsError;

    await Promise.all([
      ...selectedItems.map(async (itemId) => {
        const { error: updateFlagError } = await supabaseInstance
          .from("Menu_Item")
          .update({ itemallowaddon: "1", itemaddonbasedon: "0" })
          .eq("itemid", itemId);

        if (updateFlagError) throw updateFlagError;
      }),
      ...selectedVariations.map(async (variationId) => {
        const { data: variationData, error: updateFlagError } =
          await supabaseInstance
            // get itemId from Variation table and update the itemallowaddon and itemaddonbasedon field in the Menu_Item table
            .from("Variation")
            .select("itemId")
            .eq("variationId", variationId)
            .single();

        if (updateFlagError) throw updateFlagError;

        const { itemId } = variationData;

        const { error: updateFlagError2 } = await supabaseInstance
          .from("Menu_Item")
          .update({ itemallowaddon: "1", itemaddonbasedon: "1" })
          .eq("itemid", itemId);

        if (updateFlagError2) throw updateFlagError2;
      }),
    ]);

    res.status(200).json({
      success: true,
      message: "Addon group created successfully",
      data: {
        ...addonGroupData,
        items: itemAddonGroupsData,
      },
    });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/updateAddonGroup/:addonGroupId", async (req, res) => {
  const { addonGroupId } = req.params;
  let {
    name,
    rank,
    active,
    outletId,
    selectedItems,
    selectedVariations,
    min_selection,
    max_selection,
  } = req.body;

  if (!name || !rank || active === undefined || !outletId) {
    return res.status(400).json({
      success: false,
      error: "Please provide all required fields",
    });
  }

  try {
    const { error: updateAddonGroupError } = await supabaseInstance
      .from("AddonGroup")
      .update({ name, rank, active: active ? 1 : 0 })
      .eq("id", addonGroupId)
      .select("*")
      .single();

    if (updateAddonGroupError) throw updateAddonGroupError;

    const {
      data: deleteItemAddonGroupsData,
      error: deleteItemAddonGroupsError,
    } = await supabaseInstance
      .from("ItemAddonGroups")
      .delete()
      .eq("groupId", addonGroupId)
      .select("*");

    if (deleteItemAddonGroupsError) throw deleteItemAddonGroupsError;

    //get itemId Array
    let itemIdArray = [];
    deleteItemAddonGroupsData.map(async (item) => {
      if (item.itemId) {
        itemIdArray.push(item.itemId);
      }
    });

    //get variationId Array
    let variationIdArray = [];
    deleteItemAddonGroupsData.map(async (item) => {
      if (item.variationId) {
        variationIdArray.push(item.variationId);
      }
    });

    // get itemId of variationIdArray and push it in itemId Array using promise all
    await Promise.all(
      variationIdArray.map(async (variationId) => {
        const { data: variationData, error: fetchError } =
          await supabaseInstance
            .from("Variation")
            .select("itemId")
            .eq("variationId", variationId)
            .single();

        if (fetchError) throw fetchError;

        itemIdArray.push(variationData.itemId);
      })
    );

    // remove the itemaddonbasedon flag from the Menu_Item table
    await Promise.all(
      itemIdArray.map(async (itemId) => {
        const { error: updateFlagError } = await supabaseInstance
          .from("Menu_Item")
          .update({ itemallowaddon: "0" })
          .eq("itemid", itemId);

        if (updateFlagError) throw updateFlagError;
      })
    );

    const itemAddonGroups = [
      ...selectedItems.map((itemId) => ({
        addongroup_name: name,
        itemId,
        groupId: addonGroupId,
        min_selection,
        max_selection,
      })),
      ...selectedVariations.map((variationId) => ({
        addongroup_name: name,
        variationId,
        groupId: addonGroupId,
        min_selection,
        max_selection,
      })),
    ];

    const { data: itemAddonGroupsData, error: itemAddonGroupsError } =
      await supabaseInstance
        .from("ItemAddonGroups")
        .insert(itemAddonGroups)
        .select();

    if (itemAddonGroupsError) throw itemAddonGroupsError;

    await Promise.all([
      ...selectedItems.map(async (itemId) => {
        const { error: updateFlagError } = await supabaseInstance
          .from("Menu_Item")
          .update({ itemallowaddon: "1", itemaddonbasedon: "0" })
          .eq("itemid", itemId);

        if (updateFlagError) throw updateFlagError;
      }),
      ...selectedVariations.map(async (variationId) => {
        const { data: variationData, error: updateFlagError } =
          await supabaseInstance
            // get itemId from Variation table and update the itemallowaddon and itemaddonbasedon field in the Menu_Item table
            .from("Variation")
            .select("itemId")
            .eq("variationId", variationId)
            .single();

        if (updateFlagError) throw updateFlagError;

        const { itemId } = variationData;

        const { error: updateFlagError2 } = await supabaseInstance
          .from("Menu_Item")
          .update({ itemallowaddon: "1", itemaddonbasedon: "1" })
          .eq("itemid", itemId);

        if (updateFlagError2) throw updateFlagError2;
      }),
    ]);

    res.status(200).json({
      success: true,
      message: "Addon group created successfully",
    });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});


router.get("/getAddonGroupForOutlet/:outletId", async (req, res) => {
  let { outletId } = req.params;

  try {
    const { data: addonGroupData, error: addonGroupError } =
      await supabaseInstance
        .from("AddonGroup")
        .select("*")
        .eq("outletId", outletId);

    if (addonGroupError) throw addonGroupError;

    let idArray = [];
    // modify the data of addonGroupData
    addonGroupData.map(async (ag) => {
      ag.active = ag.active == 1 ? true : false;
      idArray.push(ag.id);
    });

    console.log("addonGroupData ===> ", addonGroupData);

    let addonGroupIds = addonGroupData.map((ag) => ag.id);

    const { data: itemAddonGroupsData, error: itemAddonGroupsError } =
      await supabaseInstance
        .from("ItemAddonGroups")
        .select("*")
        .in("groupId", addonGroupIds);

    if (itemAddonGroupsError) throw itemAddonGroupsError;

    console.log("itemAddonGroupsData ===> ", itemAddonGroupsData);

    const data = addonGroupData.map((ag) => {
      const items = itemAddonGroupsData.filter((iag) => iag.groupId === ag.id);
      return { ...ag, items };
    });

    return res.status(200).json({
      success: true,
      result: idArray,
      data,
    });
  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});

router.get("/getAddonGroup/:addonGroupId", async (req, res) => {
  let { addonGroupId } = req.params;

  try {
    const { data: addonGroupData, error: addonGroupError } =
      await supabaseInstance
        .from("AddonGroup")
        .select("*")
        .eq("id", addonGroupId)
        .single();

    if (addonGroupError && addonGroupError.code !== "PGRST116") throw addonGroupError;

    if (addonGroupError && addonGroupError.code === "PGRST116") {
      return res.status(200).json({
        success: false,
        message: "Addon group not found",
      });
    }
    addonGroupData.active = addonGroupData.active == 1 ? true : false;

    const { data: itemAddonGroupsData, error: itemAddonGroupsError } =
      await supabaseInstance
        .from("ItemAddonGroups")
        .select("itemId, variationId, min_selection, max_selection")
        .eq("groupId", addonGroupId);

    if (itemAddonGroupsError) throw itemAddonGroupsError;

    let selectedItems = [];
    itemAddonGroupsData.filter((iag) => {
      !!iag.itemId && selectedItems.push(iag.itemId);
    });
    let selectedVariations = [];
    itemAddonGroupsData.filter((iag) => {
      !!iag.variationId && selectedVariations.push(iag.variationId);
    });

    addonGroupData.selectedItems = selectedItems;
    addonGroupData.selectedVariations = selectedVariations;
    addonGroupData.min_selection = itemAddonGroupsData[0]?.min_selection || 0;
    addonGroupData.max_selection = itemAddonGroupsData[0]?.max_selection || 0;

    itemAddonGroupsData.map(async (iag) => {
      if (iag.itemId) {
        iag.itemid = iag.itemId;
        iag.itemId = this.delete;
      }
    });

    const result = {
      ...addonGroupData,
      itemAddonGroupsData,
    };

    return res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/deleteAddonGroup/:addonGroupId", async (req, res) => {
  const { addonGroupId } = req.params;

  try {
    const { error: deleteAddonGroupError } = await supabaseInstance
      .from("AddonGroup")
      .delete()
      .eq("id", addonGroupId);

    if (deleteAddonGroupError) throw deleteAddonGroupError;

    return res.status(200).json({
      success: true,
      message: "Addon group deleted successfully",
    });
  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/toggleAddonGroupStatus/:addonGroupId", async (req, res) => {
  const { addonGroupId } = req.params;
  try {
    // First get current active status
    const { data: currentData, error: fetchError } = await supabaseInstance
      .from("AddonGroup")
      .select("active")
      .eq("id", addonGroupId)
      .single();

    if (fetchError) {
      console.error("error while fetching addon group status==>", fetchError);
      throw fetchError;
    }

    // Toggle between "0" and "1"
    const newActive = currentData.active === "1" ? "0" : "1";

    const { data, error } = await supabaseInstance
      .from("AddonGroup")
      .update({ active: newActive })
      .eq("id", addonGroupId)
      .select("active")
      .single();

    if (error) {
      console.error("error while toggle addon group status==>", error);
      throw error;
    }

    res.status(200).json({
      success: true,
      data: data,
    });
  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});

// ======================/Addon Group=======================

// ======================Addon Group Items=======================

router.post("/createAddonGroupItem", async (req, res) => {
  let { name, price, active, addonGroupId, rank, attributeId } = req.body;

  if (!name || !price || active === undefined || !addonGroupId || !rank) {
    return res.status(400).json({
      success: false,
      error: "Please provide all required fields",
    });
  }

  try {
    const { error: addonGroupError } = await supabaseInstance
      .from("AddonGroup")
      .select("*")
      .eq("id", addonGroupId)
      .single();

    if (addonGroupError) throw addonGroupError;

    active = true ? "1" : "0";
    attributeId = Number(attributeId);
    rank = String(rank);

    const { data: addonGroupItemData, error: addonGroupItemError } =
      await supabaseInstance
        .from("Addongroupitems")
        .insert({ name, price, active, addonGroupId, rank, attributeId })
        .select()
        .single();

    if (addonGroupItemError) throw addonGroupItemError;

    res.status(200).json({
      success: true,
      data: addonGroupItemData,
    });
  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/updateAddonGroupItems/:addonidd", async (req, res) => {
  const { addonidd } = req.params;
  let { name, price, active, addonGroupId, rank, attributeId } = req.body;

  if (!name || !price || active == undefined || !addonGroupId || !rank) {
    return res.status(400).json({
      success: false,
      error: "Please provide all required fields",
    });
  }

  try {
    const { error: addonGroupError } = await supabaseInstance
      .from("AddonGroup")
      .select("*")
      .eq("id", addonGroupId)
      .single();

    if (addonGroupError) throw addonGroupError;

    attributeId = Number(attributeId);

    const { data: addonGroupItemData, error: addonGroupItemError } =
      await supabaseInstance
        .from("Addongroupitems")
        .update({
          name,
          price,
          active: true ? 1 : 0,
          addonGroupId,
          rank,
          attributeId,
        })
        .eq("id", addonidd)
        .select()
        .single();

    if (addonGroupItemError) throw addonGroupItemError;

    res.status(200).json({
      success: true,
      data: addonGroupItemData,
    });
  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});

router.get("/getAddonGroupItemsForOutlet/:outletId", async (req, res) => {
  let { outletId } = req.params;

  try {
    const { data: addonGroupData, error: addonGroupError } =
      await supabaseInstance
        .from("AddonGroup")
        .select("*")
        .eq("outletId", outletId);

    if (addonGroupError) throw addonGroupError;

    let addonGroupIds = addonGroupData.map((ag) => ag.id);

    const { data: addonGroupItemsData, error: addonGroupItemsError } =
      await supabaseInstance
        .from("Addongroupitems")
        .select("* , AddonGroup(id, name)")
        .in("addonGroupId", addonGroupIds);

    if (addonGroupItemsError) throw addonGroupItemsError;

    return res.status(200).json({
      success: true,
      data: addonGroupItemsData,
    });
  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});

router.get("/getAddonGroupItem/:itemidd", async (req, res) => {
  let { itemidd } = req.params;

  try {
    const { data: addonGroupItemData, error: addonGroupItemError } =
      await supabaseInstance
        .from("Addongroupitems")
        .select("*")
        .eq("id", itemidd)
        .single();

    if (addonGroupItemError) throw addonGroupItemError;

    addonGroupItemData.active = addonGroupItemData.active == 1 ? true : false;

    return res.status(200).json({
      success: true,
      data: addonGroupItemData,
    });
  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/deleteAddonGroupItem/:itemidd", async (req, res) => {
  let { itemidd } = req.params;

  try {
    const { error: deleteAddonGroupItemError } = await supabaseInstance
      .from("Addongroupitems")
      .delete()
      .eq("id", itemidd);

    if (deleteAddonGroupItemError) throw deleteAddonGroupItemError;

    return res.status(200).json({
      success: true,
      message: "Addon group item deleted successfully",
    });
  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/toggleAddonGroupItemStatus/:itemidd", async (req, res) => {
  const { itemidd } = req.params;
  try {
    const { data, error } = await supabaseInstance
      .from("Addongroupitems")
      .select("active")
      .eq("id", itemidd)
      .single();

    if (error) {
      console.error("error while fetching addon group item status==>", error);
      throw error
    };

    const newActive = data.active == "1" ? "0" : "1";
    console.log(`old active: ${data.active} new active: ${newActive}`);

    const { data: updatedData, error: updateError } = await supabaseInstance
      .from("Addongroupitems")
      .update({ active: newActive })
      .eq("id", itemidd)
      .select("active")
      .single();
    
    if (updateError) {
      console.error("error while toggle addon group item status==>", updateError);
      throw updateError
    };

    res.status(200).json({
      success: true,
      data: updatedData,
    });
  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});
// ======================/Addon Group Items======================

// ======================Manual Orders=======================



const { getPriceBreakdown } = require('../Payment/customerPayment');

router.post("/getDetailsForItems", async (req, res) => {
  const { items, outletId, isDineIn, isDelivery, isPickUp } = req.body;
  
  try {
    const itemDetails = await supabaseInstance
    .from("Menu_Item")
      .select("itemid, itemname, price")
      .in("itemid", Object.keys(items));
      
    if (itemDetails.error) throw itemDetails.error;

    const result = itemDetails.data.map((item) => ({
      id: item.itemid,
      itemname: item.itemname,
      price: item.price,
      totalPrice: item.price * items[item.itemid],
      calculated_price: item.price * items[item.itemid],
      quantity: items[item.itemid],
      qty: items[item.itemid],
    }));

    const totalAmount = result.reduce((acc, item) => acc + item.totalPrice, 0);

    const taxPrice = await getPriceBreakdown(
      outletId,
      totalAmount,
      isDineIn,
      isPickUp,
      isDelivery
    );

    const responseData = {
      outlet_id: outletId,
      order_sequence_id: Date.now().toString(),
      customer_auth_uid: null,
      customer_name: "Manual Order",
      mobile: null,
      photo: null,
      is_dine_in: isDineIn,
      is_pick_up: isPickUp,
      is_delivery: isDelivery,
      total_price: taxPrice.totalPriceForCustomer,
      additional_instruction: "",
      order_status_text: "Order Placed",
      order_status_id: 1,
      order_schedule_date: new Date().toISOString().split('T')[0],
      order_schedule_time: new Date().toTimeString().split(' ')[0],
      order_id: Date.now().toString(),
      visibility: true,
      preparation_time: 30,
      schedule_now: true,
      iscashondelivery: true,
      amount: totalAmount,
      base_price: totalAmount,
      packaging_charge: taxPrice.packagingCharge,
      deliverycharge: taxPrice.deliveryCharge,
      isgstapplied: taxPrice.isGstApplied,
      ordersavepetpoojaid: null,
      refund_status: null,
      menu_item: result,
      delivery_addresss: null,
      totalPriceForCustomer : taxPrice.totalPriceForCustomer,
      foodGST : taxPrice.foodGST,
      convenienceTotalAmount : taxPrice.convenienceTotalAmount,
      convenienceFee :  taxPrice.convenienceAmount,
    };

    // const response = {
      //   result,
      //   totalAmount,
      //   taxPrice,
      //   responseData,
      // }
      const response = [...[responseData], result];
    return res.status(200).json({
      success: true,
      data: response,
    });
  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});
//  */

router.post("/getDetailsForItems", async (req, res) => {
  const { items, outletId, isDelivery, isDineIn, isPickUp } = req.body;
  
  try {
    const itemDetails = await supabaseInstance
      .from("Menu_Item")
      .select("itemid, itemname, price")
      .in("itemid", Object.keys(items));

    if (itemDetails.error) throw itemDetails.error;

    const result = itemDetails.data.map((item) => ({
      addons: null,
      itemname: item.itemname,
      quantity: items[item.itemid],
      variation: {
        variation_id: null,
        variation_name: null,
        variation_price: null
      },
      item_price: item.price,
      calculated_price: item.price * items[item.itemid]
    }));

    const itemTotalPrice = result.reduce((acc, item) => acc + item.calculated_price, 0);
    console.log('itemTotalPrice', itemTotalPrice);
    

    // Assuming getPriceBreakdown returns an object with detailed tax information
    const priceBreakdown = await getPriceBreakdown(
      outletId,
      itemTotalPrice,
      isDineIn,
      isPickUp,
      isDelivery
    );

    // Calculate convenience fee
    const convenienceFee = priceBreakdown.convenienceAmount;

    // Calculate total price
    const totalPrice = itemTotalPrice + priceBreakdown.total_tax + convenienceFee;

/*
itemTotalPrice,
            foodGST,
            commissionAmount,
            FoodBasePrice,
            convenienceAmount,
            commissionTotalAmount,
            TDS,
            TCS,
            convenienceGSTAmount,
            totalPriceForCustomer,
            mealpeVendorAmount,
            outletVendorAmount,
            packagingCharge,
            deliveryCharge,
            isGstApplied,
            convenienceTotalAmount,
            commissionGSTAmount,

            outletBankLabel,
*/

    const responseData = {
      outlet_id: outletId,
      order_sequence_id: Date.now().toString(),
      customer_auth_uid: null,
      customer_name:"Manual Order",
      mobile: req.user ? req.user.mobile : null,
      photo: null,
      is_dine_in: isDineIn,
      is_pick_up: isPickUp,
      is_delivery: isDelivery,
      total_price: priceBreakdown.totalPriceForCustomer,
      additional_instruction: "",
      order_status_text: "Order Placed",
      order_status_id: 1,
      order_schedule_date: new Date().toISOString().split('T')[0],
      order_schedule_time: new Date().toTimeString().split(' ')[0],
      order_id: Date.now().toString(),
      visibility: true,
      preparation_time: 30,
      schedule_now: true,
      iscashondelivery: true,
      amount: totalPrice,
      base_price: itemTotalPrice,
      packaging_charge: priceBreakdown.packagingCharge,
      deliverycharge: priceBreakdown.deliveryCharge,
      isgstapplied: true,
      ordersavepetpoojaid: null,
      refund_status: null,
      menu_item: result,
      delivery_addresss: null,
      totalPriceForCustomer : priceBreakdown.totalPriceForCustomer,
      foodGST : priceBreakdown.foodGST,
      convenienceTotalAmount : priceBreakdown.convenienceTotalAmount,
      convenienceFee :  priceBreakdown.convenienceAmount,
    };
    
    return res.status(200).json({
      success: true,
      data: [responseData],
      meta: {
        page: 1,
        perPage: 1,
        totalPages: 1,
        totalCount: 1
      },
      priceBreakdown
    });
  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});

// */

// router.post("/getDetailsForItems", async (req, res) => {
//   const { items, outletId } = req.body;

//   try {
//     const itemDetails = await supabaseInstance
//       .from("Menu_Item")
//       .select("itemid, itemname, price")
//       .in("itemid", Object.keys(items));

//     if (itemDetails.error) throw itemDetails.error;

//     const result = itemDetails.data.map((item) => ({
//       addons: null, // Assuming no addons for simplicity
//       itemname: item.itemname,
//       quantity: items[item.itemid],
//       variation: {
//         variation_id: null,
//         variation_name: null,
//         variation_price: null
//       },
//       item_price: item.price,
//       calculated_price: item.price * items[item.itemid]
//     }));

//     const totalAmount = result.reduce((acc, item) => acc + item.calculated_price, 0);

//     const taxPrice = await getPriceBreakdown(
//       outletId,
//       totalAmount,
//       true,
//       false,
//       false
//     );

//     // Assuming some default values for fields not provided in the original API
//     const responseData = {
//       outlet_id: outletId,
//       order_sequence_id: Date.now().toString(), // Generate a unique ID
//       customer_auth_uid: req.user ? req.user.id : null, // Assuming user authentication
//       customer_name: req.user ? req.user.name : "Guest",
//       mobile: req.user ? req.user.mobile : null,
//       photo: null,
//       is_dine_in: true, // Default to dine-in
//       is_pick_up: false,
//       is_delivery: false,
//       total_price: totalAmount + taxPrice.total_tax,
//       additional_instruction: "",
//       order_status_text: "Order Placed",
//       order_status_id: 1, // Assuming 1 is for "Order Placed"
//       order_schedule_date: new Date().toISOString().split('T')[0],
//       order_schedule_time: new Date().toTimeString().split(' ')[0],
//       order_id: Date.now().toString(), // Generate a unique ID
//       visibility: true,
//       preparation_time: 30, // Default preparation time
//       schedule_now: true,
//       iscashondelivery: false,
//       amount: totalAmount,
//       base_price: totalAmount,
//       packaging_charge: 0,
//       convenience_total_amount: 0,
//       food_gst: taxPrice.food_gst,
//       deliverycharge: 0,
//       isgstapplied: true,
//       ordersavepetpoojaid: null,
//       refund_status: null,
//       menu_item: result,
//       delivery_addresss: null
//     };

//     return res.status(200).json({
//       success: true,
//       data: [responseData], // Wrap in an array to match the original format
//       meta: {
//         page: 1,
//         perPage: 1,
//         totalPages: 1,
//         totalCount: 1
//       }
//     });
//   } catch (error) {
//     return res.status(500).json({ success: false, error: error.message });
//   }
// });




router.get("/toggleStatusForHonest", async (req, res) => {
  const outletId = "12e12aa6-6ad4-4c5b-b7c2-3e606016073c";
  try {
    // change the status of all items in Menu_Item table to true for this outletId
    const { data: itemData, error: itemError } = await supabaseInstance
      .from("Menu_Item")
      .update({ status: true })
      .eq("outletId", outletId);

    if (itemError) throw itemError;

    // get the items with status false
    const { data: itemData2, error: itemError2 } = await supabaseInstance
      .from("Menu_Item")
      .select("*")
      .eq("status", false)
      .eq("outletId", outletId);

    if (itemError2) throw itemError2;

    return res.status(200).json({
      success: true,
      data: itemData2,
    });

  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});

//===========================================================/Manual Orders=======================

router.post("/toggleItemStatusAndRecommendation", async (req, res) => { 
  const { itemIds, fieldToUpdate, value } = req.body;

  // Validate required fields
  if (!itemIds?.length || !fieldToUpdate || value === undefined) {
    return res.status(400).json({
      success: false,
      message: `Missing fields : ${!itemIds?.length ? 'itemIds' : ''}${!fieldToUpdate ? 'fieldToUpdate' : ''}${value === undefined ? 'value' : ''}`
    });
  }

  // Validate fieldToUpdate is either status or recommendation
  if (!['status', 'isRecommendedItem'].includes(fieldToUpdate)) {
    return res.status(400).json({
      success: false,
      message: `Invalid fieldToUpdate: ${fieldToUpdate}. Must be either 'status' or 'isRecommendedItem'`
    });
  }
  
  try {
    // Use dynamic object key to set the correct field
    const updateData = {
      [fieldToUpdate]: value
    };

    const { error: itemError } = await supabaseInstance
      .from("Menu_Item")
      .update(updateData)
      .in("itemid", itemIds);

    if (itemError) throw itemError;

    return res.status(200).json({
      success: true,
      message: `Items ${fieldToUpdate} updated successfully`
    });

  } catch (error) {
    console.error("Error updating items:", error);
    return res.status(500).json({
      success: false,
      message: `Error updating items ${fieldToUpdate}`,
      error: error.message
    });
  }
});

/**
 * @description This route is used by developers for duplication menu from one outlet to another
 */

router.post("/dev/duplicateMenu", async (req, res) => {
  const { sourceOutletId, destinationOutletId } = req.body;

  if (!sourceOutletId || !destinationOutletId) {
    return res.status(400).json({
      success: false,
      message: "Source and destination outlet IDs are required"
    });
  }

  try {
    // 1. Get all categories from source outlet
    const { data: sourceCategories, error: categoriesError } = await supabaseInstance
      .from("Menu_Categories")
      .select("*")
      .eq("outletId", sourceOutletId);

    if (categoriesError) throw categoriesError;

    // Create category mapping for reference
    const categoryMapping = {};

    // 2. Duplicate categories
    for (const category of sourceCategories) {
      const { data: newCategory, error: newCategoryError } = await supabaseInstance
        .from("Menu_Categories")
        .insert({
          ...category,
          categoryid: undefined, // Let DB generate new ID
          outletId: destinationOutletId
        })
        .select("*")
        .single();

      if (newCategoryError) throw newCategoryError;
      categoryMapping[category.categoryid] = newCategory.categoryid;
    }

    // 3. Get all menu items from source outlet
    const { data: sourceItems, error: itemsError } = await supabaseInstance
      .from("Menu_Item")
      .select("*")
      .eq("outletId", sourceOutletId);

    if (itemsError) throw itemsError;

    // Create item mapping for reference
    const itemMapping = {};

    // 4. Duplicate menu items
    for (const item of sourceItems) {
      const { data: newItem, error: newItemError } = await supabaseInstance
        .from("Menu_Item")
        .insert({
          ...item,
          itemid: undefined, // Let DB generate new ID
          outletId: destinationOutletId,
          item_categoryid: categoryMapping[item.item_categoryid] || null
        })
        .select("*")
        .single();

      if (newItemError) throw newItemError;
      itemMapping[item.itemid] = newItem.itemid;
    }

    // 5. Get and duplicate variations
    const { data: sourceVariations, error: variationsError } = await supabaseInstance
      .from("Variation")
      .select("*")
      .eq("outletId", sourceOutletId);

    if (variationsError) throw variationsError;

    for (const variation of sourceVariations) {
      const { error: newVariationError } = await supabaseInstance
        .from("Variation")
        .insert({
          ...variation,
          variationId: undefined, // Let DB generate new ID
          outletId: destinationOutletId,
          itemId: itemMapping[variation.itemId]
        });

      if (newVariationError) throw newVariationError;
    }

    // 6. Get and duplicate addon groups
    const { data: sourceAddonGroups, error: addonGroupsError } = await supabaseInstance
      .from("AddonGroup")
      .select("*")
      .eq("outletId", sourceOutletId);

    if (addonGroupsError) throw addonGroupsError;

    // Create addon group mapping
    const addonGroupMapping = {};

    for (const addonGroup of sourceAddonGroups) {
      const { data: newAddonGroup, error: newAddonGroupError } = await supabaseInstance
        .from("AddonGroup")
        .insert({
          ...addonGroup,
          id: undefined, // Let DB generate new ID
          outletId: destinationOutletId
        })
        .select("*")
        .single();

      if (newAddonGroupError) throw newAddonGroupError;
      addonGroupMapping[addonGroup.id] = newAddonGroup.id;

      // Duplicate addon group items
      const { data: sourceAddonItems, error: addonItemsError } = await supabaseInstance
        .from("Addongroupitems")
        .select("*")
        .eq("addonGroupId", addonGroup.id);

      if (addonItemsError) throw addonItemsError;

      for (const addonItem of sourceAddonItems) {
        const { error: newAddonItemError } = await supabaseInstance
          .from("Addongroupitems")
          .insert({
            ...addonItem,
            id: undefined, // Let DB generate new ID
            addonGroupId: newAddonGroup.id
          });

        if (newAddonItemError) throw newAddonItemError;
      }
    }

    // 7. Duplicate item-addon group relationships
    const { data: sourceItemAddonGroups, error: itemAddonGroupsError } = await supabaseInstance
      .from("ItemAddonGroups")
      .select("*")
      .eq("groupId", Object.keys(addonGroupMapping));

    if (itemAddonGroupsError) throw itemAddonGroupsError;

    for (const itemAddonGroup of sourceItemAddonGroups) {
      const { error: newItemAddonGroupError } = await supabaseInstance
        .from("ItemAddonGroups")
        .insert({
          ...itemAddonGroup,
          id: undefined, // Let DB generate new ID
          itemId: itemAddonGroup.itemId ? itemMapping[itemAddonGroup.itemId] : null,
          groupId: addonGroupMapping[itemAddonGroup.groupId],
          addongroup_name: itemAddonGroup.addongroup_name
        });

      if (newItemAddonGroupError) throw newItemAddonGroupError;
    }

    res.status(200).json({
      success: true,
      message: "Menu duplicated successfully",
      data: {
        categoriesDuplicated: sourceCategories.length,
        itemsDuplicated: sourceItems.length,
        variationsDuplicated: sourceVariations.length,
        addonGroupsDuplicated: sourceAddonGroups.length
      }
    });

  } catch (error) {
    console.error("Error duplicating menu:", error);
    res.status(500).json({
      success: false,
      message: "Error duplicating menu",
      error: error.message
    });
  }
});

module.exports = router;

/* 
const query = `
     SELECT 
        Menu_item.itemid AS item_id,
        Menu_item.itemname AS item_name,

        Menu_item.item_allow_variation,
        Menu_item.item_allow_addon,
        Menu_item.item_addon_based_on,
        
        Variation.variationId AS variation_id,
        Variation.name AS variation_name,

        AddonGroup.id AS addongroup_id,
        AddonGroup.name AS addongroup_name,
        AddonGroup.rank AS addongroup_rank,
        AddonGroup.active AS addongroup_active,

        Addongroupitems.id AS addonitem_id,
        Addongroupitems.name AS addonitem_name,
        Addongroupitems.price AS addonitem_price,
        Addongroupitems.active AS addonitem_active,
        
        Addongroupitems.attributes AS addonitem_attributes,
        Addongroupitems.rank AS addonitem_rank

    FROM items
    LEFT JOIN Variation ON Menu_item.id = Variation.item_id
    LEFT JOIN ItemAddonGroups ON Menu_item.id = ItemAddonGroups.item_id OR Variation.id = ItemAddonGroups.variation_id
    LEFT JOIN AddonGroup ON ItemAddonGroups.group_id = AddonGroup.id
    LEFT JOIN Addongroupitems ON AddonGroup.id = Addongroupitems.group_id
    WHERE items.id = $1;
  `;

*/

/* 
router.get("/getItemTest/:itemid", async (req, res) => {
  const { itemid } = req.params;

  try {
    // Fetch the item details
    let { data: itemData, error: itemError } = await supabaseInstance
      .from('Menu_Item')
      .select('*')
      .eq('itemid', itemid)
      .single();

    if (itemError) throw itemError;

    // Fetch the variations for the item
    let { data: variations, error: variationsError } = await supabaseInstance
      .from('Variation')
      .select('*')
      .eq('itemId', itemid);

    if (variationsError) throw variationsError;

    // Fetch the add-on groups associated with the item or its variations
    let { data: addonGroups, error: addonGroupsError } = await supabaseInstance
      .from('ItemAddonGroups')
      .select(`
        groupId,
        itemId,
        variationId,
        min_selection,
        max_selection
        groupId: AddonGroup(*)
      `)
      .or(`itemId.eq.${itemid},variationId.in.(${variations.map(v => v.id).join(',')})`);

      console.log('\n\n\n\n\naddonGroups ===> \n', addonGroups );
      
    if (addonGroupsError) throw addonGroupsError;

    // Combine the results
    const result = {
      ...itemData,
      variations: variations,
      addonGroups: addonGroups.map(ag => ({
        id: ag.addongroups.id,
        name: ag.addongroups.name,
        rank: ag.addongroups.rank,
        active: ag.addongroups.active,
        min_selection: ag.min_selection,
        max_selection: ag.max_selection,
        items: ag.addongroups.addongroupitems
      }))
    };

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});
*/
