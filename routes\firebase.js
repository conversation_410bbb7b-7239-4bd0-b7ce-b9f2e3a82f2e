const { initializeApp, cert } = require("firebase-admin/app");
const admin = require("firebase-admin");
const express = require("express");
const router = express.Router();
const path = require("path");
const supabaseInstance = require("../services/supabaseClient").supabase;


initializeApp({
  credential: cert(
    path.resolve(
      __dirname,
      "./mealpe-93b10-firebase-adminsdk-niiz7-ed0b736cc8.json"
    )
  ),
  projectId: "mealpe-93b10",
});

async function sendMessNotification(
  title,
  body,
  fcmToken,
  outletName,
  outletId,
  appName = "com.mealpe"
) {
  {
    const message = {
      data: {
        screenName: "MessScreen",
        outletName: outletName,
        outletId: outletId,
        appName: appName,
      },
      notification: {
        title,
        body,
      },
      token: fcmToken,
    };

    try {
      const response = await admin.messaging().send(message);
      console.log("Successfully sent message:", response);
      return { success: true, response };
    } catch (error) {
      console.error("Error sending message:", error);

      if (
        error.code === "messaging/invalid-argument" ||
        error.code === "messaging/registration-token-not-registered"
      ) {
        console.log("Invalid FCM token detected. Token:", fcmToken);
        await removeInvalidToken(fcmToken);
        return { success: false, error: "Invalid FCM token", code: error.code };
      }

      return { success: false, error: error.message, code: error.code };
    }
  }
}

async function sendNotification(
  fcmToken,
  title,
  body,
  outletName = "test name",
  outletId = "Test id",
  appName = "com.mealpe"
) {
  const message = {
    data: {
      screenName: "HomeScreen",
      outletName: outletName,
      outletId: outletId,
      appName: appName,
    },
    notification: {
      title,
      body,
    },
    token: fcmToken,
  };

  try {
    const response = await admin.messaging().send(message);
    console.log("Successfully sent message:", response);
    return { success: true, response };
  } catch (error) {
    console.error("Error sending message:", error);

    if (
      error.code === "messaging/invalid-argument" ||
      error.code === "messaging/registration-token-not-registered"
    ) {
      console.log("Invalid FCM token detected. Token:", fcmToken);
      await removeInvalidToken(fcmToken);
      return { success: false, error: "Invalid FCM token", code: error.code };
    }

    return { success: false, error: error.message, code: error.code };
  }
}

async function sendStatusUpdateNotification(fcmToken, title, body) {
  const message = {
    notification: {
      title,
      body,
    },
    token: fcmToken,
  };

  try {
    const response = await admin.messaging().send(message);
    console.log("Successfully sent message:", response);
    return { success: true, response };
  } catch (error) {
    console.error("Error sending message:", error);

    if (
      error.code === "messaging/invalid-argument" ||
      error.code === "messaging/registration-token-not-registered"
    ) {
      console.log("Invalid FCM token detected. Token:", fcmToken);
      await removeInvalidToken(fcmToken);
      return { success: false, error: "Invalid FCM token", code: error.code };
    }

    return { success: false, error: error.message, code: error.code };
  }
}

async function removeInvalidToken(fcmToken) {
  try {
    const { data, error } = await supabaseInstance
      .from("FCM_Tokens")
      .delete()
      .match({ fcmToken });

    if (error) throw error;
    console.log(`Removed invalid token ${fcmToken}`);
  } catch (error) {
    console.error("Error removing invalid token:", error);
  }
}

router.get("/", (req, res) => {
  return res.status(200).json({
    success: true,
    message: "response from firebase.js",
  });
});

router.post("/send-broadcast", async (req, res) => {
  const { title, body, outletName, outletId, appName = "NBS" } = req.body;

  if (!title || !body || !appName) {
    return res.status(400).json({ error: "Missing required parameters" });
  }

  try {
    // Fetch all FCM tokens for the specified app
    const { data: tokens, error: tokenError } = await supabaseInstance
      .from("FCM_Tokens")
      .select("fcmToken")
      .eq("appName", appName);

    if (tokenError) throw tokenError;

    if (!tokens || tokens.length === 0) {
      return res
        .status(404)
        .json({ error: "No FCM tokens found for the specified app" });
    }

    const results = [];
    const invalidTokens = [];

    // Send notifications to all tokens
    for (const token of tokens) {
      const result = await sendNotification(
        token.fcmToken,
        title,
        body,
        outletName,
        outletId,
        appName
      );

      if (result.success) {
        results.push({ token: token.fcmToken, status: "success" });
      } else {
        if (
          result.code === "messaging/invalid-argument" ||
          result.code === "messaging/registration-token-not-registered"
        ) {
          invalidTokens.push(token.fcmToken);
        }
        results.push({
          token: token.fcmToken,
          status: "failed",
          error: result.error,
        });
      }
    }

    // Remove invalid tokens
    if (invalidTokens.length > 0) {
      const { error: deleteError } = await supabaseInstance
        .from("FCM_Tokens")
        .delete()
        .in("fcmToken", invalidTokens);

      if (deleteError) {
        console.error("Error removing invalid tokens:", deleteError);
      }
    }

    const successCount = results.filter((r) => r.status === "success").length;
    const failureCount = results.filter((r) => r.status === "failed").length;

    res.status(200).json({
      message: "Broadcast notification process completed",
      totalProcessed: tokens.length,
      successCount,
      failureCount,
      invalidTokensRemoved: invalidTokens.length,
      results,
    });
  } catch (error) {
    console.error("Error in /send-broadcast endpoint:", error);
    res
      .status(500)
      .json({ error: "Internal server error", details: error.message });
  }
});

async function getFCMTokenOfUser(customerAuthUID, appName = "com.mealpe") {
  try {
    const { data, error } = await supabaseInstance
      .from("FCM_Tokens")
      .select("fcmToken")
      .eq("customerAuthUID", customerAuthUID)
      .eq("appName", appName);

    if (error) throw error;

    return data[0]?.fcmToken;
  } catch (error) {
    console.error("Error fetching FCM token:", error);
    return null;
  }
}

/**
 * send notifications to selected users from admin.
 * @param {string} title
 * @param {string} body
 * @param {string} appName
 * @param {string} fcmTokens
 *
 * steps :
 * - validate for missing parameters
 * - send the sns notifications to all the fcm tokens
 * - return the response of successful as well as failed notifications
 */
async function sendAdminNotifications(title, body, fcmTokens, appName) {
  try {
    if (!title || !body || !fcmTokens) {
      return { success: false, error: "Missing required parameters" };
    }

    const results = [];
    const invalidTokens = [];

    for (const token of fcmTokens) {
      const result = await sendNotification(token, title, body, appName);

      if (result.success) {
        results.push({ token, status: "success" });
      } else {
        if (
          result.code === "messaging/invalid-argument" ||
          result.code === "messaging/registration-token-not-registered"
        ) {
          invalidTokens.push(token);
        }
        results.push({ token, status: "failed", error: result.error });
      }
    }

    if (invalidTokens.length > 0) {
      const { error: deleteError } = await supabaseInstance
        .from("FCM_Tokens")
        .delete()
        .in("fcmToken", invalidTokens)
        .eq("appName", appName); // Add appName to deletion query

      if (deleteError) {
        console.error("Error removing invalid tokens:", deleteError);
      }
    }

    const successCount = results.filter((r) => r.status === "success").length;
    const failureCount = results.filter((r) => r.status === "failed").length;

    return {
      success: true,
      message: "Broadcast notification process completed",
      totalProcessed: fcmTokens.length,
      successCount,
      failureCount,
      invalidTokensRemoved: invalidTokens.length,
      results,
    };
  } catch (error) {
    console.error("Error sending admin notifications:", error);
    return { success: false, error };
  }
}

async function sendOutletNotifications(title, body, fcmTokens, appName, outletName) {
  try {
    if (!title || !body || !fcmTokens || !appName) {
      return { success: false, error: "Missing required parameters" };
    }

    const results = [];
    const invalidTokens = [];
    // title = `${outletName} - ${title}`;

    for (const token of fcmTokens) {
      const result = await sendNotification(token, title, body, appName, outletName);

      if (result.success) {
        results.push({ token, status: "success" });
      } else {
        if (
          result.code === "messaging/invalid-argument" ||
          result.code === "messaging/registration-token-not-registered"
        ) {
          invalidTokens.push(token);
        }
        results.push({ token, status: "failed", error: result.error });
      }
    }

    if (invalidTokens.length > 0) {
      const { error: deleteError } = await supabaseInstance
        .from("FCM_Tokens")
        .delete()
        .in("fcmToken", invalidTokens)
        .eq("appName", appName); // Add appName to deletion query

      if (deleteError) {
        console.error("Error removing invalid tokens:", deleteError);
      }
    }

    const successCount = results.filter((r) => r.status === "success").length;
    const failureCount = results.filter((r) => r.status === "failed").length;

    return {
      success: true,
      message: "Broadcast notification process completed",
      totalProcessed: fcmTokens.length,
      successCount,
      failureCount,
      invalidTokensRemoved: invalidTokens.length,
      results,
    };
  } catch (error) {
    console.error("Error sending admin notifications:", error);
    return { success: false, error };
  }
}

// Helper function to fetch FCM tokens for multiple apps and customers
async function fetchFCMTokens(customerAuthUIDs, appNames) {
  try {
    const tokens = {};
    for (const appName of appNames) {
      const { data, error } = await supabaseInstance
        .from("FCM_Tokens")
        .select("fcmToken, customerAuthUID")
        .in("customerAuthUID", customerAuthUIDs)
        .eq("appName", appName);

      if (error) throw error;
      tokens[appName] = data || [];
    }
    return { success: true, tokens };
  } catch (error) {
    console.error("Error fetching FCM tokens:", error);
    return { success: false, error };
  }
}

module.exports = router;
module.exports.sendNotification = sendNotification;
module.exports.sendStatusUpdateNotification = sendStatusUpdateNotification;
module.exports.getFCMTokenOfUser = getFCMTokenOfUser;
module.exports.sendAdminNotifications = sendAdminNotifications;
module.exports.sendMessNotification = sendMessNotification;
module.exports.sendOutletNotifications = sendOutletNotifications;
module.exports.fetchFCMTokens = fetchFCMTokens;
