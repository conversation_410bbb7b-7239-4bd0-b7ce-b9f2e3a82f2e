var express = require("express");
const moment = require("moment-timezone");
var router = express.Router();
var supabaseInstance = require("../../services/supabaseClient").supabase;
const logger = require("../../services/logger");

router.post("/get-store-status-webhook", async (req, res) => {
    const { restID } = req.body;
    // { restID: 'IDIDIDIDID' }
    // {"http_code":200,"status":"success","store_status":"1","message":"Store Delivery Status fetched successfully"}
    console.log("req.body => ", req.body);

    if (restID) {

        try {
            const outletQuery = await supabaseInstance.from("Outlet").select("outletId,outletName,petPoojaRestId").eq("petPoojaRestId", restID);

            if (outletQuery?.data?.length > 0) {
                const outletQueryData = outletQuery.data[0];
                const store_status = outletQueryData.isOutletOpen ? 1 : 0;

                console.log("Store Delivery Status send successfully from mealpe to petpooja.");
                console.log("outlet name  => ", outletQueryData?.outletName);
                console.log("outlet ID    => ", outletQueryData?.outletId);
                console.log("Store Statos => ", store_status);

                res.status(200).json({
                    http_code: 200,
                    status: "success",
                    store_status: String(store_status),
                    message: "Store Delivery Status fetched successfully."
                });
            } else {
                if (outletQuery?.data?.length === 0) {
                    res.status(500).json({ success: false, error: "Outlet not found in mealpe system." });
                } else {
                    throw outletQuery?.error;
                }
            }
        } catch (error) {
            logger.error("get-store-status-webhook => ", error);
            res.status(500).json({ success: false, error: error?.message || error || JSON.stringify(error) });
        }
    } else {
        res.status(500).json({ success: false, error: "Please pass restID." });
    }
})

router.post("/update-store-status-webhook", async (req, res) => {
    const { restID, store_status, reason, turn_on_time } = req.body;
    // {
    //   restID: 'bq69dxai',
    //   store_status: 0,
    //   reason: 'try',
    //   turn_on_time: '2024-01-12 13:00:00'
    // }

    // {"success":true,"http_code":200,"error":""}

    console.log("req.body => ", req.body);

    if (restID) {

        try {
            const outletQuery = await supabaseInstance.from("Outlet").select("outletId,outletName,petPoojaRestId").eq("petPoojaRestId", restID);

            if (outletQuery?.data?.length > 0) {
                const outletQueryData = outletQuery.data[0];
                const isOutletOpenTimestamp = moment().tz("Asia/Kolkata");
                const isOutletOpen = Boolean(store_status === "1" || store_status === 1) ? true : false;

                const outletUpdateQuery = await supabaseInstance.from("Outlet").update({ isOutletOpen, isOutletOpenTimestamp }).eq("outletId", outletQueryData?.outletId).select("outletId").maybeSingle();

                if (outletUpdateQuery?.data) {
                    console.log("Store Delivery Status update successfully from petpooja to mealpe.");
                    console.log("outlet name  => ", outletQueryData?.outletName);
                    console.log("outlet ID    => ", outletQueryData?.outletId);
                    console.log("update body => ", { isOutletOpen, isOutletOpenTimestamp });

                    res.status(200).json({
                        http_code: 200,
                        success: true,
                        error: ""
                    });
                } else {
                    throw outletUpdateQuery?.error;
                }
            } else {
                if (outletQuery?.data?.length === 0) {
                    res.status(500).json({ success: false, error: "Outlet not found in mealpe system." });
                } else {
                    throw outletQuery?.error;
                }
            }
        } catch (error) {
            logger.error("update-store-status-webhook => ", error);
            res.status(500).json({ success: false, error: error?.message || error || JSON.stringify(error) });
        }
    } else {
        res.status(500).json({ success: false, error: "Please pass restID." });
    }
})

router.post("/item-off-webhook", async (req, res) => {
    const { restID, inStock, itemID, type, autoTurnOnTime, customTurnOnTime } = req.body;
    // {
    //   restID: 'bq69dxai',
    //   inStock: false,
    //   itemID: [ '10505299', '10505300' ],
    //   type: 'item',
    //   autoTurnOnTime: 'custom',
    //   customTurnOnTime: '2024-01-12 15:06:00'
    // }
    // {"success":true,"http_code":200,"error":""}

    console.log("req.body => ", req.body);

    if (restID) {

        try {
            const outletQuery = await supabaseInstance.from("Outlet").select("outletId,outletName,petPoojaRestId").eq("petPoojaRestId", restID);

            if (outletQuery?.data?.length > 0) {
                const outletQueryData = outletQuery.data[0];
                let resultArr = [];

                for (const itemIdElement of itemID) {
                    let resultObj = {
                        petpoojaItemId: itemIdElement,
                        success: true
                    }
                    const menuItemUpdateResponse = await supabaseInstance.from("Menu_Item").update({ status: false }).eq("outletId", outletQueryData?.outletId).eq("petpoojaItemId", itemIdElement).select("itemid");
                    if (menuItemUpdateResponse?.data?.length > 0) {
                        resultObj.updateLength = menuItemUpdateResponse?.data?.length;
                        resultObj.success = true;
                    } else {
                        resultObj.success = false;
                        if (menuItemUpdateResponse?.data?.length === 0) {
                            resultObj.reson = "Not update any Item (menuItemUpdateResponse?.data?.length === 0)";
                        } else {
                            resultObj.reson = menuItemUpdateResponse?.error?.message;
                        }
                    }
                    resultArr.push(resultObj);
                }
                console.log("result Arr for [item-off-webhook] => ", resultArr);

                res.status(200).json({
                    http_code: 200,
                    success: true,
                    error: ""
                });
            } else {
                if (outletQuery?.data?.length === 0) {
                    res.status(500).json({ success: false, error: "Outlet not found in mealpe system." });
                } else {
                    throw outletQuery?.error;
                }
            }
        } catch (error) {
            logger.error("update-store-status-webhook => ", error);
            res.status(500).json({ success: false, error: error?.message || error || JSON.stringify(error) });
        }
    } else {
        res.status(500).json({ success: false, error: "Please pass restID." });
    }
})

router.post("/item-on-webhook", async (req, res) => {
    const { restID, inStock, itemID, type } = req.body;
    // {
    //   restID: 'bq69dxai',
    //   inStock: true,
    //   itemID: [ '10505295', '10505296' ],
    //   type: 'item'
    // }
    // {"success":true,"http_code":200,"error":""}

    console.log("req.body => ", req.body);

    if (restID) {

        try {
            const outletQuery = await supabaseInstance.from("Outlet").select("outletId,outletName,petPoojaRestId").eq("petPoojaRestId", restID);

            if (outletQuery?.data?.length > 0) {
                const outletQueryData = outletQuery.data[0];
                let resultArr = [];

                for (const itemIdElement of itemID) {
                    let resultObj = {
                        petpoojaItemId: itemIdElement,
                        success: true
                    }
                    const menuItemUpdateResponse = await supabaseInstance.from("Menu_Item").update({ status: true }).eq("outletId", outletQueryData?.outletId).eq("petpoojaItemId", itemIdElement).select("itemid");
                    if (menuItemUpdateResponse?.data?.length > 0) {
                        resultObj.updateLength = menuItemUpdateResponse?.data?.length;
                        resultObj.success = true;
                    } else {
                        resultObj.success = false;
                        if (menuItemUpdateResponse?.data?.length === 0) {
                            resultObj.reson = "Not update any Item (menuItemUpdateResponse?.data?.length === 0)";
                        } else {
                            resultObj.reson = menuItemUpdateResponse?.error?.message;
                        }
                    }
                    resultArr.push(resultObj);
                }
                console.log("result Arr for [item-on-webhook] => ", resultArr);

                res.status(200).json({
                    http_code: 200,
                    success: true,
                    error: ""
                });
            } else {
                if (outletQuery?.data?.length === 0) {
                    res.status(500).json({ success: false, error: "Outlet not found in mealpe system." });
                } else {
                    throw outletQuery?.error;
                }
            }
        } catch (error) {
            logger.error("update-store-status-webhook => ", error);
            res.status(500).json({ success: false, error: error?.message || error || JSON.stringify(error) });
        }
    } else {
        res.status(500).json({ success: false, error: "Please pass restID." });
    }
})

// router.post("/menu-sharing-webhook", async (req, res) => {
//     const postBody = req.body;

//     // console.log("postBody => ", postBody);
//     console.log("postBody?.restaurants?.[0]?.details?.menusharingcode => ", postBody?.restaurants?.[0]?.details?.menusharingcode);

//     if (postBody?.success === "1" && postBody?.restaurants?.[0]?.details?.menusharingcode) {
//         try {
//             const outletQuery = await supabaseInstance.from("Outlet").select(
//                 `outletId,outletName,petPoojaRestId,
//                 Menu_Parent_Categories!left(parent_category_id,parentCategoryName,outletId,parent_category_image_url,status,petpoojaParentCategoryId),
//                 Menu_Categories!left(categoryid,status,parent_category_id,categoryname,category_image_url,petpoojaCategoryId),
//                 Menu_Item!left(*),Variation!left(*)) `
//             ).eq("petPoojaRestId", postBody?.restaurants?.[0]?.details?.menusharingcode).limit(1);

//             //          Petpooja                |           Mealpe
//             // -----------------------------------------------
//             // -----------------------------------------------
//             // parentcategories                 |   Menu_Parent_Categories
//             //  -id                             |       - parent_category_id
//             //  -name                           |       - parentCategoryName
//             //                                  |       - outletId
//             //                                  |       - parent_category_image_url
//             //  -status                         |       - status
//             //                                  |       - petpoojaParentCategoryId
//             // -----------------------------------------------
//             // categories                       |   Menu_Categories
//             //  -categoryid                     |       -categoryid
//             //  -active                         |       -status
//             //  -parent_category_id             |       -parent_category_id
//             //  -categoryname                   |       -categoryname
//             //  -category_image_url             |       -category_image_url
//             //  -                               |       -petpoojaCategoryId
//             //  
//             // -----------------------------------------------
//             // items                            |   Menu_Item
//             //  -itemid                         |       -itemid
//             //  -item_categoryid                |       -item_categoryid
//             //  -itemname                       |       -itemname
//             //  -itemdescription                |       -itemdescription
//             //  -price                          |       -price
//             //  -active                         |       -status
//             //  -minimumpreparationtime         |       -minimumpreparationtime
//             //  -item_image_url                 |       -item_image_url
//             //  -                               |       -outletId
//             //  -                               |       -petpoojaItemId
//             //  -                               |       -

//             if (outletQuery?.data?.length > 0) {
//                 const outletQueryData = outletQuery.data[0];
//                 const parentcategoriesData = postBody.parentcategories;
//                 const menuCategoriesData = postBody.categories;
//                 const itemsData = postBody.items;

//                 console.info("http_code: 200");
//                 res.status(200).json({
//                     http_code: 200,
//                     success: true,
//                     error: "",
//                     // outletQueryData
//                 });

//                 if (parentcategoriesData?.length > 0) {
//                     for (let petpoojaParentCategoryObject of parentcategoriesData) {
//                         const mealpeParentCategoryObject = outletQueryData?.Menu_Parent_Categories?.find(f => f.petpoojaParentCategoryId === petpoojaParentCategoryObject.id);

//                         let p_c_body = {
//                             status: petpoojaParentCategoryObject?.status === "1" ? true : false,
//                             parentCategoryName: petpoojaParentCategoryObject?.name,
//                             petpoojaParentCategoryId: petpoojaParentCategoryObject?.id
//                         }

//                         if (!mealpeParentCategoryObject) {
//                             p_c_body.outletId = outletQueryData.outletId;
//                             const parentcategoriesResponse = await supabaseInstance.from("Menu_Parent_Categories").insert(p_c_body).select("*").maybeSingle();
//                             if (parentcategoriesResponse?.data) {
//                                 petpoojaParentCategoryObject.mealpeResponse = parentcategoriesResponse?.data;
//                             }
//                         } else {
//                             const parentcategoriesResponse = await supabaseInstance.from("Menu_Parent_Categories").update({ parentCategoryName: p_c_body.parentCategoryName, status: p_c_body.status }).eq("parent_category_id", mealpeParentCategoryObject?.parent_category_id).select("*").maybeSingle();
//                             if (parentcategoriesResponse?.data) {
//                                 petpoojaParentCategoryObject.mealpeResponse = parentcategoriesResponse?.data;
//                             }
//                         }
//                     }
//                 }

//                 if (menuCategoriesData?.length > 0) {
//                     for (let petpoojaCategoryObject of menuCategoriesData) {
//                         const mealpeCategoryObject = outletQueryData.Menu_Categories.find(f => f.petpoojaCategoryId === petpoojaCategoryObject.categoryid);

//                         let c_body = {
//                             status: petpoojaCategoryObject?.active === "1" ? true : false,
//                             categoryname: petpoojaCategoryObject?.categoryname,
//                             category_image_url: petpoojaCategoryObject?.category_image_url,
//                             petpoojaCategoryId: petpoojaCategoryObject?.categoryid
//                         }

//                         if (petpoojaCategoryObject?.parent_category_id && petpoojaCategoryObject?.parent_category_id !== "0") {
//                             c_body.parent_category_id = parentcategoriesData.find(f => f.id === petpoojaCategoryObject?.parent_category_id)?.mealpeResponse?.parent_category_id || null;
//                         }

//                         if (!mealpeCategoryObject) {
//                             c_body.outletId = outletQueryData.outletId;
//                             const menuCategoriesResponse = await supabaseInstance.from("Menu_Categories").insert(c_body).select("*").maybeSingle();

//                             if (menuCategoriesResponse?.data) {
//                                 petpoojaCategoryObject.mealpeResponse = menuCategoriesResponse?.data;
//                             }
//                         } else {
//                             const menuCategoriesResponse = await supabaseInstance
//                                 .from("Menu_Categories")
//                                 .update({ status: c_body.active, parent_category_id: c_body.parent_category_id, categoryname: c_body.categoryname, category_image_url: c_body.category_image_url })
//                                 .eq("categoryid", mealpeCategoryObject.categoryid)
//                                 .select("*")
//                                 .maybeSingle();
//                             if (menuCategoriesResponse?.data) {
//                                 petpoojaCategoryObject.mealpeResponse = menuCategoriesResponse?.data;
//                             }
//                         }
//                     }
//                 }

//                 if (itemsData?.length > 0) {
//                     for (let petpoojaItemObject of itemsData) {
//                         let hasVariation = petpoojaItemObject?.variation?.length > 0 ? true : false;
//                         let hasAddons = petpoojaItemObject?.addongroups?.length > 0 ? true : false;
                
//                         const mealpeItemObject = outletQueryData.Menu_Item.find(f => f.petpoojaItemId === petpoojaItemObject.itemid);
//                         let itemId = mealpeItemObject?.itemid;
                
//                         let i_body = {
//                             itemname: petpoojaItemObject?.itemname,
//                             itemdescription: petpoojaItemObject?.itemdescription,
//                             price: parseFloat(hasVariation ? petpoojaItemObject?.variation[0]?.price : petpoojaItemObject?.price),
//                             status: petpoojaItemObject?.active === "1" ? true : false,
//                             minimumpreparationtime: parseFloat(petpoojaItemObject?.minimumpreparationtime),
//                             item_image_url: petpoojaItemObject?.item_image_url,
//                             petpoojaItemId: petpoojaItemObject?.itemid
//                         };
                
//                         if (petpoojaItemObject?.item_categoryid) {
//                             i_body.item_categoryid = menuCategoriesData.find(f => f.categoryid === petpoojaItemObject?.item_categoryid)?.mealpeResponse?.categoryid || null;
//                         }
                
//                         // Inserting or updating the item
//                         if (!mealpeItemObject) {
//                             i_body.outletId = outletQueryData.outletId;
//                             const itemResponse = await supabaseInstance.from("Menu_Item").insert(i_body).select("*").maybeSingle();
//                             if (itemResponse?.data) {
//                                 petpoojaItemObject.mealpeResponse = itemResponse?.data;
//                                 itemId = itemResponse?.data?.itemid;
//                             }
//                         } else {
//                             const itemResponse = await supabaseInstance
//                                 .from("Menu_Item")
//                                 .update({ itemname: i_body.itemname, price: i_body.price, itemdescription: i_body.itemdescription, item_image_url: i_body.item_image_url, status: i_body.status, minimumpreparationtime: i_body.minimumpreparationtime, item_categoryid: i_body.item_categoryid })
//                                 .select("*")
//                                 .eq("itemid", mealpeItemObject.itemid)
//                                 .maybeSingle();
//                             if (itemResponse?.data) {
//                                 petpoojaItemObject.mealpeResponse = itemResponse?.data;
//                                 itemId = itemResponse?.data?.itemid;
//                             }
//                         }
                
//                         // Handle variations if present
//                         if (hasVariation) {
//                             // change the itemallowvariation to true
//                             const itemResponse = await supabaseInstance.from("Menu_Item").update({ itemallowvariation: 1 }).eq("itemid", itemId).select("*").maybeSingle();
//                             for (let variation of petpoojaItemObject.variation) {
//                                 const mealpeVariationObject = outletQueryData.Variation.find(v => v.itemId === itemId && v.name === variation.name);
                
//                                 let v_body = {
//                                     name: variation.name,
//                                     price: parseFloat(variation.price),
//                                     attributeId: petpoojaItemObject?.item_attributeid,
//                                     status: variation.active === "1" ? true : false,
//                                     isDefault: variation.isDefault || false,
//                                     outletId: outletQueryData.outletId,
//                                     itemId: itemId,
//                                     itemArray: [itemId],
//                                 };
                
//                                 if (!mealpeVariationObject) {
//                                     // Insert new variation
//                                     const variationResponse = await supabaseInstance.from("Variation").insert(v_body).select("*").maybeSingle();
//                                     if (variationResponse?.data) {
//                                         variation.mealpeResponse = variationResponse.data;
//                                     }
//                                 } else {
//                                     // Update existing variation
//                                     const variationResponse = await supabaseInstance.from("Variation").update(v_body).eq("variationId", mealpeVariationObject.variationId).select("*").maybeSingle();
//                                     if (variationResponse?.data) {
//                                         variation.mealpeResponse = variationResponse.data;
//                                     }
//                                 }
//                             }
//                         }
//                     }
//                 }
//                 console.info("menu-sharing-webhook success");
//             } else {
//                 if (outletQuery?.data?.length === 0) {
//                     console.info("http_code: 500", "Outlet not found in mealpe system.");
//                     res.status(500).json({ success: false, error: "Outlet not found in mealpe system." });
//                 } else {
//                     throw outletQuery?.error;
//                 }
//             }
//         } catch (error) {
//             console.error("menu-sharing-webhook => ", error);
//             console.info("http_code: 500");
//             res.status(500).json({ success: false, error: error?.message || error || JSON.stringify(error) });
//         }
//     } else {
//         console.info("http_code: 500", "Please pass postBody?.restaurants?.[0]?.details?.menusharingcode.");
//         res.status(500).json({ success: false, error: "Please pass postBody?.restaurants?.[0]?.details?.menusharingcode." });
//     }
// });










// // 13:14 addons gpt code
// router.post("/menu-sharing-webhook", async (req, res) => {
//     const postBody = req.body;
//     console.log("postBody?.restaurants?.[0]?.details?.menusharingcode => ", postBody?.restaurants?.[0]?.details?.menusharingcode);

//     if (postBody?.success === "1" && postBody?.restaurants?.[0]?.details?.menusharingcode) {
//         try {
//             const outletQuery = await supabaseInstance.from("Outlet").select(
//                 `outletId,outletName,petPoojaRestId,
//                 Menu_Parent_Categories!left(parent_category_id,parentCategoryName,outletId,parent_category_image_url,status,petpoojaParentCategoryId),
//                 Menu_Categories!left(categoryid,status,parent_category_id,categoryname,category_image_url,petpoojaCategoryId),
//                 Menu_Item!left(*),Variation!left(*), AddonGroup!left(*)) `
//             ).eq("petPoojaRestId", postBody?.restaurants?.[0]?.details?.menusharingcode).limit(1);

//             if (outletQuery?.data?.length > 0) {
//                 const outletQueryData = outletQuery.data[0];
//                 const parentcategoriesData = postBody.parentcategories;
//                 const menuCategoriesData = postBody.categories;
//                 const itemsData = postBody.items;
//                 const addongroupsData = postBody.addongroups;

//                 console.info("http_code: 200");
//                 res.status(200).json({
//                     http_code: 200,
//                     success: true,
//                     error: "",
//                 });

//                 // Handle Parent Categories
//                 if (parentcategoriesData?.length > 0) {
//                     for (let petpoojaParentCategoryObject of parentcategoriesData) {
//                         const mealpeParentCategoryObject = outletQueryData?.Menu_Parent_Categories?.find(f => f.petpoojaParentCategoryId === petpoojaParentCategoryObject.id);

//                         let p_c_body = {
//                             status: petpoojaParentCategoryObject?.status === "1" ? true : false,
//                             parentCategoryName: petpoojaParentCategoryObject?.name,
//                             petpoojaParentCategoryId: petpoojaParentCategoryObject?.id
//                         }

//                         if (!mealpeParentCategoryObject) {
//                             p_c_body.outletId = outletQueryData.outletId;
//                             const parentcategoriesResponse = await supabaseInstance.from("Menu_Parent_Categories").insert(p_c_body).select("*").maybeSingle();
//                             if (parentcategoriesResponse?.data) {
//                                 petpoojaParentCategoryObject.mealpeResponse = parentcategoriesResponse?.data;
//                             }
//                         } else {
//                             const parentcategoriesResponse = await supabaseInstance.from("Menu_Parent_Categories").update({ parentCategoryName: p_c_body.parentCategoryName, status: p_c_body.status }).eq("parent_category_id", mealpeParentCategoryObject?.parent_category_id).select("*").maybeSingle();
//                             if (parentcategoriesResponse?.data) {
//                                 petpoojaParentCategoryObject.mealpeResponse = parentcategoriesResponse?.data;
//                             }
//                         }
//                     }
//                 }

//                 // Handle Menu Categories
//                 if (menuCategoriesData?.length > 0) {
//                     for (let petpoojaCategoryObject of menuCategoriesData) {
//                         const mealpeCategoryObject = outletQueryData.Menu_Categories.find(f => f.petpoojaCategoryId === petpoojaCategoryObject.categoryid);

//                         let c_body = {
//                             status: petpoojaCategoryObject?.active === "1" ? true : false,
//                             categoryname: petpoojaCategoryObject?.categoryname,
//                             category_image_url: petpoojaCategoryObject?.category_image_url,
//                             petpoojaCategoryId: petpoojaCategoryObject?.categoryid
//                         }

//                         if (petpoojaCategoryObject?.parent_category_id && petpoojaCategoryObject?.parent_category_id !== "0") {
//                             c_body.parent_category_id = parentcategoriesData.find(f => f.id === petpoojaCategoryObject?.parent_category_id)?.mealpeResponse?.parent_category_id || null;
//                         }

//                         if (!mealpeCategoryObject) {
//                             c_body.outletId = outletQueryData.outletId;
//                             const menuCategoriesResponse = await supabaseInstance.from("Menu_Categories").insert(c_body).select("*").maybeSingle();

//                             if (menuCategoriesResponse?.data) {
//                                 petpoojaCategoryObject.mealpeResponse = menuCategoriesResponse?.data;
//                             }
//                         } else {
//                             const menuCategoriesResponse = await supabaseInstance
//                                 .from("Menu_Categories")
//                                 .update({ status: c_body.status, parent_category_id: c_body.parent_category_id, categoryname: c_body.categoryname, category_image_url: c_body.category_image_url })
//                                 .eq("categoryid", mealpeCategoryObject.categoryid)
//                                 .select("*")
//                                 .maybeSingle();
//                             if (menuCategoriesResponse?.data) {
//                                 petpoojaCategoryObject.mealpeResponse = menuCategoriesResponse?.data;
//                             }
//                         }
//                     }
//                 }

//                 // Handle Items
//                 if (itemsData?.length > 0) {
//                     for (let petpoojaItemObject of itemsData) {
//                         let hasVariation = petpoojaItemObject?.variation?.length > 0 ? true : false;
//                         let hasAddons = petpoojaItemObject?.addon?.length > 0 ? true : false;
                        
//                         const mealpeItemObject = outletQueryData.Menu_Item.find(f => f.petpoojaItemId === petpoojaItemObject.itemid);
//                         let itemId = mealpeItemObject?.itemid;
                
//                         let i_body = {
//                             itemname: petpoojaItemObject?.itemname,
//                             itemdescription: petpoojaItemObject?.itemdescription,
//                             price: parseFloat(hasVariation ? petpoojaItemObject?.variation[0]?.price : petpoojaItemObject?.price),
//                             status: petpoojaItemObject?.active,
//                             minimumpreparationtime: parseFloat(petpoojaItemObject?.minimumpreparationtime),
//                             item_image_url: petpoojaItemObject?.item_image_url,
//                             petpoojaItemId: petpoojaItemObject?.itemid
//                         };
                
//                         if (petpoojaItemObject?.item_categoryid) {
//                             i_body.item_categoryid = menuCategoriesData.find(f => f.categoryid === petpoojaItemObject?.item_categoryid)?.mealpeResponse?.categoryid || null;
//                         }
                
//                         // Insert or update the item
//                         if (!mealpeItemObject) {
//                             i_body.outletId = outletQueryData.outletId;
//                             const itemResponse = await supabaseInstance.from("Menu_Item").insert(i_body).select("*").maybeSingle();
//                             if (itemResponse?.data) {
//                                 petpoojaItemObject.mealpeResponse = itemResponse?.data;
//                                 itemId = itemResponse?.data?.itemid;
//                             }
//                         } else {
//                             const itemResponse = await supabaseInstance
//                                 .from("Menu_Item")
//                                 .update({ itemname: i_body.itemname, price: i_body.price, itemdescription: i_body.itemdescription, item_image_url: i_body.item_image_url, status: i_body.status, minimumpreparationtime: i_body.minimumpreparationtime, item_categoryid: i_body.item_categoryid })
//                                 .select("*")
//                                 .eq("itemid", mealpeItemObject.itemid)
//                                 .maybeSingle();
//                             if (itemResponse?.data) {
//                                 petpoojaItemObject.mealpeResponse = itemResponse?.data;
//                                 itemId = itemResponse?.data?.itemid;
//                             }
//                         }

//                         // Handle variations if present
//                         if (hasVariation) {
//                             const itemResponse = await supabaseInstance.from("Menu_Item").update({ itemallowvariation: 1 }).eq("itemid", itemId).select("*").maybeSingle();
//                             for (let variation of petpoojaItemObject.variation) {
//                                 const mealpeVariationObject = outletQueryData.Variation.find(v => v.itemId === itemId && v.name === variation.name);
                
//                                 let v_body = {
//                                     name: variation.name,
//                                     price: parseFloat(variation.price),
//                                     attributeId: petpoojaItemObject?.item_attributeid,
//                                     status: variation.active ,
//                                     isDefault: variation.isDefault || false,
//                                     outletId: outletQueryData.outletId,
//                                     itemId: itemId,
//                                     itemArray: [itemId],
//                                 };
                
//                                 if (!mealpeVariationObject) {
//                                     const variationResponse = await supabaseInstance.from("Variation").insert(v_body).select("*").maybeSingle();
//                                     if (variationResponse?.data) {
//                                         variation.mealpeResponse = variationResponse.data;
//                                     }
//                                 } else {
//                                     const variationResponse = await supabaseInstance.from("Variation").update(v_body).eq("variationId", mealpeVariationObject.variationId).select("*").maybeSingle();
//                                     if (variationResponse?.data) {
//                                         variation.mealpeResponse = variationResponse.data;
//                                     }
//                                 }
//                             }
//                         }

                        
//                         // Handle Addons linked to Items
//                         if (hasAddons) {
//                             for (let addon of petpoojaItemObject.addon) {
//                                 const addonGroupName = addongroupsData.find(g => g.addongroupid === addon.addon_group_id)?.addongroup_name;
//                                 console.log('addonGroupName', addonGroupName);
                                

//                                 const addonGroup = outletQueryData?.AddonGroup?.find(f => 
//                                     f.name === addonGroupName && 
//                                     f.outletId === outletQueryData.outletId
//                                 );

//                                 if (addonGroup) {
//                                     let iag_body = {
//                                         addongroup_name: addonGroup.name,
//                                         itemId: itemId,
//                                         groupId: addonGroup.id,
//                                         min_selection: addon.addon_item_selection_min,
//                                         max_selection: addon.addon_item_selection_max,
//                                     };

//                                     if (iag_body.groupId && iag_body.addongroup_name) {
//                                         await supabaseInstance.from("ItemAddonGroups").upsert(iag_body).select("*");
//                                     } else {
//                                         console.error("GroupId or Addongroup_name is missing: ", iag_body);
//                                     }
//                                 } else {
//                                     console.error("Addon group not found for itemId and addongroup_name: ", { itemId, addongroup_name: addonGroupName });
//                                 }
//                             }

//                             // Update Menu_Item to allow addons
//                             await supabaseInstance.from("Menu_Item").update({ itemallowaddon: "1", itemaddonbasedon: "0" }).eq("itemid", itemId);
//                         }

//                         // Handle Addons linked to Variations
//                         if (hasVariation) {
//                             for (let variation of petpoojaItemObject.variation) {
//                                 const mealpeVariationObject = outletQueryData.Variation.find(v => v.itemId === itemId && v.name === variation.name);

//                                 if (mealpeVariationObject && variation.addon?.length > 0) {
//                                     for (let addon of variation.addon) {
//                                         const addonGroupName = addongroupsData.find(g => g.addongroupid === addon.addon_group_id)?.addongroup_name;

//                                         const addonGroup = outletQueryData.AddonGroup.find(f => 
//                                             f.name === addonGroupName &&
//                                             outletQueryData.Variation.find(v => v.variationId === mealpeVariationObject?.variationId)?.variationId === f.variationId
//                                         );

//                                         if (addonGroup) {
//                                             let iag_body = {
//                                                 addongroup_name: addonGroup.name,
//                                                 variationId: mealpeVariationObject?.variationId,
//                                                 groupId: addonGroup.id,
//                                                 min_selection: addon.addon_item_selection_min,
//                                                 max_selection: addon.addon_item_selection_max,
//                                             };

//                                             if (iag_body.groupId && iag_body.addongroup_name) {
//                                                 await supabaseInstance.from("ItemAddonGroups").upsert(iag_body).select("*");
//                                             } else {
//                                                 console.error("GroupId or Addongroup_name is missing for variation: ", iag_body);
//                                             }
//                                         } else {
//                                             console.error("Addon group not found for variationId and addongroup_name: ", { variationId: mealpeVariationObject?.variationId, addongroup_name: addonGroupName });
//                                         }
//                                     }

//                                     const { itemId } = mealpeVariationObject;
//                                     await supabaseInstance.from("Menu_Item").update({ itemallowaddon: "1", itemaddonbasedon: "1" }).eq("itemid", itemId);
//                                 }
//                             }
//                         }
//                     }
//                 }

//                 // Handle Addon Groups and Items
//                 if (addongroupsData?.length > 0) {
//                     for (let petpoojaAddonGroupObject of addongroupsData) {
//                         const mealpeAddonGroupObject = outletQueryData.AddonGroup.find(f => f.name === petpoojaAddonGroupObject.addongroup_name && f.outletId === outletQueryData.outletId);

//                         let ag_body = {
//                             name: petpoojaAddonGroupObject?.addongroup_name,
//                             rank: petpoojaAddonGroupObject?.addongroup_rank,
//                             active: petpoojaAddonGroupObject?.active,
//                             outletId: outletQueryData.outletId
//                         };

//                         let addonGroupId;
//                         if (!mealpeAddonGroupObject) {
//                             const addonGroupResponse = await supabaseInstance.from("AddonGroup").insert(ag_body).select("*").maybeSingle();
//                             if (addonGroupResponse?.data) {
//                                 addonGroupId = addonGroupResponse.data.id;
//                             }
//                         } else {
//                             const addonGroupResponse = await supabaseInstance
//                                 .from("AddonGroup")
//                                 .update(ag_body)
//                                 .eq("addongroupid", mealpeAddonGroupObject.addongroupid)
//                                 .select("*")
//                                 .maybeSingle();
//                             if (addonGroupResponse?.data) {
//                                 addonGroupId = addonGroupResponse.data.id;
//                             }
//                         }

//                         // Handle Addon Group Items
//                         if (petpoojaAddonGroupObject.addongroupitems?.length > 0) {
//                             for (let addongroupitem of petpoojaAddonGroupObject.addongroupitems) {
//                                 const mealpeAddonItemObject = outletQueryData.AddonGroup.Addongroupitems?.find(f => f.addonitemid === addongroupitem.addonitemid);

//                                 let agi_body = {
//                                     name: addongroupitem.addonitem_name,
//                                     price: parseFloat(addongroupitem.addonitem_price),
//                                     active: addongroupitem.active,
//                                     addonGroupId: addonGroupId,
//                                     rank: addongroupitem.addonitem_rank,
//                                     attributeId: addongroupitem.attributes
//                                 };

//                                 if (!mealpeAddonItemObject) {
//                                     await supabaseInstance.from("Addongroupitems").insert(agi_body).select("*").maybeSingle();
//                                 } else {
//                                     await supabaseInstance.from("Addongroupitems").update(agi_body).eq("addonitemid", mealpeAddonItemObject.addonitemid).select("*").maybeSingle();
//                                 }
//                             }
//                         }
//                     }
//                 }

//                 console.info("menu-sharing-webhook success");
//             } else {
//                 if (outletQuery?.data?.length === 0) {
//                     console.info("http_code: 500", "Outlet not found in mealpe system.");
//                     res.status(500).json({ success: false, error: "Outlet not found in mealpe system." });
//                 } else {
//                     throw outletQuery?.error;
//                 }
//             }
//         } catch (error) {
//             console.error("menu-sharing-webhook => ", error);
//             console.info("http_code: 500");
//             res.status(500).json({ success: false, error: error?.message || error || JSON.stringify(error) });
//         }
//     } else {
//         console.info("http_code: 500", "Please pass postBody?.restaurants?.[0]?.details?.menusharingcode.");
//         res.status(500).json({ success: false, error: "Please pass postBody?.restaurants?.[0]?.details?.menusharingcode." });
//     }
// });







// 14:12 itemaddon grp with null values
// router.post("/menu-sharing-webhook", async (req, res) => {
//     const postBody = req.body;
//     console.log("postBody?.restaurants?.[0]?.details?.menusharingcode => ", postBody?.restaurants?.[0]?.details?.menusharingcode);

//     if (postBody?.success === "1" && postBody?.restaurants?.[0]?.details?.menusharingcode) {
//         try {
//             const outletQuery = await supabaseInstance.from("Outlet").select(
//                 `
//                 outletId,outletName,petPoojaRestId,
//                 Menu_Parent_Categories!left(parent_category_id,parentCategoryName,outletId,parent_category_image_url,status,petpoojaParentCategoryId),
//                 Menu_Categories!left(categoryid,status,parent_category_id,categoryname,category_image_url,petpoojaCategoryId),
//                 Menu_Item!left(*),Variation!left(*), AddonGroup!left(*, Addongroupitems!left(*), ItemAddonGroups!left(*)) `
//             ).eq("petPoojaRestId", postBody?.restaurants?.[0]?.details?.menusharingcode).limit(1);

//             if (outletQuery?.data?.length > 0) {
//                 const outletQueryData = outletQuery.data[0];
//                 const parentcategoriesData = postBody.parentcategories;
//                 const menuCategoriesData = postBody.categories;
//                 const itemsData = postBody.items;
//                 const addongroupsData = postBody.addongroups;
                

//                 console.info("http_code: 200");
//                 res.status(200).json({
//                     http_code: 200,
//                     success: true,
//                     error: "",
//                 });

//                 // Handle Parent Categories
//                 if (parentcategoriesData?.length > 0) {
//                     for (let petpoojaParentCategoryObject of parentcategoriesData) {
//                         const mealpeParentCategoryObject = outletQueryData?.Menu_Parent_Categories?.find(f => f.petpoojaParentCategoryId === petpoojaParentCategoryObject.id);

//                         let p_c_body = {
//                             status: petpoojaParentCategoryObject?.status === "1" ? true : false,
//                             parentCategoryName: petpoojaParentCategoryObject?.name,
//                             petpoojaParentCategoryId: petpoojaParentCategoryObject?.id
//                         }

//                         if (!mealpeParentCategoryObject) {
//                             p_c_body.outletId = outletQueryData.outletId;
//                             const parentcategoriesResponse = await supabaseInstance.from("Menu_Parent_Categories").insert(p_c_body).select("*").maybeSingle();
//                             if (parentcategoriesResponse?.data) {
//                                 petpoojaParentCategoryObject.mealpeResponse = parentcategoriesResponse?.data;
//                             }
//                         } else {
//                             const parentcategoriesResponse = await supabaseInstance.from("Menu_Parent_Categories").update({ parentCategoryName: p_c_body.parentCategoryName, status: p_c_body.status }).eq("parent_category_id", mealpeParentCategoryObject?.parent_category_id).select("*").maybeSingle();
//                             if (parentcategoriesResponse?.data) {
//                                 petpoojaParentCategoryObject.mealpeResponse = parentcategoriesResponse?.data;
//                             }
//                         }
//                     }
//                 }

//                 // Handle Menu Categories
//                 if (menuCategoriesData?.length > 0) {
//                     for (let petpoojaCategoryObject of menuCategoriesData) {
//                         const mealpeCategoryObject = outletQueryData.Menu_Categories.find(f => f.petpoojaCategoryId === petpoojaCategoryObject.categoryid);

//                         let c_body = {
//                             status: petpoojaCategoryObject?.active === "1" ? true : false,
//                             categoryname: petpoojaCategoryObject?.categoryname,
//                             category_image_url: petpoojaCategoryObject?.category_image_url,
//                             petpoojaCategoryId: petpoojaCategoryObject?.categoryid
//                         }

//                         if (petpoojaCategoryObject?.parent_category_id && petpoojaCategoryObject?.parent_category_id !== "0") {
//                             c_body.parent_category_id = parentcategoriesData.find(f => f.id === petpoojaCategoryObject?.parent_category_id)?.mealpeResponse?.parent_category_id || null;
//                         }

//                         if (!mealpeCategoryObject) {
//                             c_body.outletId = outletQueryData.outletId;
//                             const menuCategoriesResponse = await supabaseInstance.from("Menu_Categories").insert(c_body).select("*").maybeSingle();

//                             if (menuCategoriesResponse?.data) {
//                                 petpoojaCategoryObject.mealpeResponse = menuCategoriesResponse?.data;
//                             }
//                         } else {
//                             const menuCategoriesResponse = await supabaseInstance
//                                 .from("Menu_Categories")
//                                 .update({ status: c_body.status, parent_category_id: c_body.parent_category_id, categoryname: c_body.categoryname, category_image_url: c_body.category_image_url })
//                                 .eq("categoryid", mealpeCategoryObject.categoryid)
//                                 .select("*")
//                                 .maybeSingle();
//                             if (menuCategoriesResponse?.data) {
//                                 petpoojaCategoryObject.mealpeResponse = menuCategoriesResponse?.data;
//                             }
//                         }
//                     }
//                 }
                
//                 let itemAddonGroupId;
//                 // Handle Items
//                 if (itemsData?.length > 0) {
//                     for (let petpoojaItemObject of itemsData) {
//                         let hasVariation = petpoojaItemObject?.variation?.length > 0 ? true : false;
//                         let hasAddons = petpoojaItemObject?.addon?.length > 0 ? true : false;
                       
                        
                
//                         const mealpeItemObject = outletQueryData.Menu_Item.find(f => f.petpoojaItemId === petpoojaItemObject.itemid);
//                         let itemId = mealpeItemObject?.itemid;
                
//                         let i_body = {
//                             itemname: petpoojaItemObject?.itemname,
//                             itemdescription: petpoojaItemObject?.itemdescription,
//                             price: parseFloat(hasVariation ? petpoojaItemObject?.variation[0]?.price : petpoojaItemObject?.price),
//                             status: petpoojaItemObject?.active,
//                             minimumpreparationtime: parseFloat(petpoojaItemObject?.minimumpreparationtime),
//                             item_image_url: petpoojaItemObject?.item_image_url,
//                             petpoojaItemId: petpoojaItemObject?.itemid
//                         };
                
//                         if (petpoojaItemObject?.item_categoryid) {
//                             i_body.item_categoryid = menuCategoriesData.find(f => f.categoryid === petpoojaItemObject?.item_categoryid)?.mealpeResponse?.categoryid || null;
//                         }
                
//                         // Inserting or updating the item
//                         if (!mealpeItemObject) {
//                             i_body.outletId = outletQueryData.outletId;
//                             const itemResponse = await supabaseInstance.from("Menu_Item").insert(i_body).select("*").maybeSingle();
//                             if (itemResponse?.data) {
//                                 petpoojaItemObject.mealpeResponse = itemResponse?.data;
//                                 itemId = itemResponse?.data?.itemid;
//                             }
//                         } else {
//                             const itemResponse = await supabaseInstance
//                                 .from("Menu_Item")
//                                 .update({ itemname: i_body.itemname, price: i_body.price, itemdescription: i_body.itemdescription, item_image_url: i_body.item_image_url, status: i_body.status, minimumpreparationtime: i_body.minimumpreparationtime, item_categoryid: i_body.item_categoryid })
//                                 .select("*")
//                                 .eq("itemid", mealpeItemObject.itemid)
//                                 .maybeSingle();
//                             if (itemResponse?.data) {
//                                 petpoojaItemObject.mealpeResponse = itemResponse?.data;
//                                 itemId = itemResponse?.data?.itemid;
//                             }
//                         }

//                         // Handle variations if present
//                         if (hasVariation) {
//                             const itemResponse = await supabaseInstance.from("Menu_Item").update({ itemallowvariation: 1 }).eq("itemid", itemId).select("*").maybeSingle();
//                             for (let variation of petpoojaItemObject.variation) {
//                                 const mealpeVariationObject = outletQueryData.Variation.find(v => v.itemId === itemId && v.name === variation.name);
                
//                                 let v_body = {
//                                     name: variation.name,
//                                     price: parseFloat(variation.price),
//                                     attributeId: petpoojaItemObject?.item_attributeid,
//                                     status: variation.active ,
//                                     isDefault: variation.isDefault || false,
//                                     outletId: outletQueryData.outletId,
//                                     itemId: itemId,
//                                     itemArray: [itemId],
//                                 };
                
//                                 if (!mealpeVariationObject) {
//                                     const variationResponse = await supabaseInstance.from("Variation").insert(v_body).select("*").maybeSingle();
//                                     if (variationResponse?.data) {
//                                         variation.mealpeResponse = variationResponse.data;
//                                     }
//                                 } else {
//                                     const variationResponse = await supabaseInstance.from("Variation").update(v_body).eq("variationId", mealpeVariationObject.variationId).select("*").maybeSingle();
//                                     if (variationResponse?.data) {
//                                         variation.mealpeResponse = variationResponse.data;
//                                     }
//                                 }
//                             }
//                         }

                        
//                         // Handle Addons linked to Items
//                         if (hasAddons) {

//                             for (let addon of petpoojaItemObject.addon) {
                                
//                                 // find the addon group using itemid and addongroup_name (addongroup_name is in the object of addongroups array of the postbody  )
//                                 const addonGroup = outletQueryData?.AddonGroup?.ItemAddonGroups?.find(f => f.itemId === itemId && f.addongroup_name === addongroupsData?.find(f => f.addongroupid === addon.addon_group_id)?.addongroup_name);
//                                 console.log('outletQueryData?.AddonGroup' , outletQueryData?.AddonGroup);
                                
//                                 console.log('630 addonGroup : ' , addonGroup);
                                
//                                 let iag_body = {
//                                     addongroup_name: addonGroup?.name,
//                                     itemId,
//                                     groupId: addonGroup?.id,
//                                     min_selection: addon.addon_item_selection_min,
//                                     max_selection: addon.addon_item_selection_max,
//                                 };

//                                 let data = (await supabaseInstance.from("ItemAddonGroups").upsert(iag_body).select("*"));
//                                 console.log('data973' , data);
//                                 if (data?.data[0]) {
//                                     itemAddonGroupId = data?.data[0]?.id;
//                                     console.log('itemAddonGroupId976', itemAddonGroupId);
                                    
//                                 }
                                
//                             }

//                             // Update Menu_Item to allow addons
//                             await supabaseInstance.from("Menu_Item").update({ itemallowaddon: "1", itemaddonbasedon: "0" }).eq("itemid", itemId);
//                         }
                
//                         // Handle Addons linked to Variations
//                         if (petpoojaItemObject?.variation?.length > 0) {
//                             for (let variation of petpoojaItemObject.variation) {
//                                 const mealpeVariationObject = outletQueryData.Variation.find(v => v.itemId === itemId && v.name === variation.name);

//                                 if (variation.addon?.length > 0) {
//                                     for (let addon of variation.addon) {
//                                         const addonGroup = outletQueryData.AddonGroup.find(f => f.addongroupid === addon.addon_group_id);

//                                         let iag_body = {
//                                             addongroup_name: addonGroup?.name,
//                                             variationId: mealpeVariationObject?.variationId,
//                                             groupId: addonGroup?.id,
//                                             min_selection: addon.addon_item_selection_min,
//                                             max_selection: addon.addon_item_selection_max,
//                                         };

//                                         await supabaseInstance.from("ItemAddonGroups").insert(iag_body).select("*");
//                                     }

//                                     const { itemId } = mealpeVariationObject;
//                                     await supabaseInstance.from("Menu_Item").update({ itemallowaddon: "1", itemaddonbasedon: "1" }).eq("itemid", itemId);
//                                 }
//                             }
//                         }
//                     }
//                 }

//                 // Handle Addon Groups and Items
//                 console.log('addongroupsData', addongroupsData);
                
//                 if (addongroupsData?.length > 0) {
                    
//                     for (let petpoojaAddonGroupObject of addongroupsData) {
//                         const mealpeAddonGroupObject = outletQueryData.AddonGroup.find(f => ((f.name === petpoojaAddonGroupObject.addongroup_name) && (f.outletId === outletQueryData.outletId)));

//                         let ag_body = {
//                             name: petpoojaAddonGroupObject?.addongroup_name,
//                             rank: petpoojaAddonGroupObject?.addongroup_rank,
//                             active: petpoojaAddonGroupObject?.active,
//                             outletId: outletQueryData.outletId
//                         };

//                         let addonGroupId;
//                         if (!mealpeAddonGroupObject) {
//                             const addonGroupResponse = await supabaseInstance.from("AddonGroup").insert(ag_body).select("*").maybeSingle();
//                             if (addonGroupResponse?.data) {
//                                 addonGroupId = addonGroupResponse.data.id;
//                                 // update the addongroupid and addongroup_name in the ItemAddonGroups table
//                                 let res1027 = await supabaseInstance.from("ItemAddonGroups").update({ groupId: addonGroupId, addongroup_name: ag_body.name }).eq("id" , itemAddonGroupId).select("*"); 
//                                 console.log('res1027', res1027);
                                
//                             }
//                         } else {
//                             const addonGroupResponse = await supabaseInstance
//                                 .from("AddonGroup")
//                                 .update(ag_body)
//                                 .eq("addongroupid", mealpeAddonGroupObject.addongroupid)
//                                 .select("*")
//                                 .maybeSingle();
//                             if (addonGroupResponse?.data) {
//                                 addonGroupId = addonGroupResponse.data.id;
//                                 // // update the addongroupid and addongroup_name in the ItemAddonGroups table
//                                 // let res1041 = await supabaseInstance.from("ItemAddonGroups").update({ groupId: addonGroupId, addongroup_name: ag_body.name }).eq("id" , itemAddonGroupId).select("*");
//                                 // console.log('res1041', res1041);
                                
//                             }
//                         }

//                         // Handle Addon Group Items
//                         if (petpoojaAddonGroupObject.addongroupitems?.length > 0) {
//                             for (let addongroupitem of petpoojaAddonGroupObject.addongroupitems) {
//                                 const mealpeAddonItemObject = outletQueryData.AddonGroup.Addongroupitems?.find(f => f.addonitemid === addongroupitem.addonitemid);

//                                 let agi_body = {
//                                     name: addongroupitem.addonitem_name,
//                                     price: parseFloat(addongroupitem.addonitem_price),
//                                     active: addongroupitem.active,
//                                     addonGroupId: addonGroupId,
//                                     rank: addongroupitem.addonitem_rank,
//                                     attributeId: addongroupitem.attributes
//                                 };

//                                 if (!mealpeAddonItemObject) {
//                                     await supabaseInstance.from("Addongroupitems").insert(agi_body).select("*").maybeSingle();
//                                 } else {
//                                     await supabaseInstance.from("Addongroupitems").update(agi_body).eq("addonitemid", mealpeAddonItemObject.addonitemid).select("*").maybeSingle();
//                                 }
//                             }
//                         }
//                     }
//                 }

//                 console.info("menu-sharing-webhook success");
//             } else {
//                 if (outletQuery?.data?.length === 0) {
//                     console.info("http_code: 500", "Outlet not found in mealpe system.");
//                     res.status(500).json({ success: false, error: "Outlet not found in mealpe system." });
//                 } else {
//                     throw outletQuery?.error;
//                 }
//             }
//         } catch (error) {
//             console.error("menu-sharing-webhook => ", error);
//             console.info("http_code: 500");
//             res.status(500).json({ success: false, error: error?.message || error || JSON.stringify(error) });
//         }
//     } else {
//         console.info("http_code: 500", "Please pass postBody?.restaurants?.[0]?.details?.menusharingcode.");
//         res.status(500).json({ success: false, error: "Please pass postBody?.restaurants?.[0]?.details?.menusharingcode." });
//     }
// });






// //11-08-2024 17:55
// router.post("/menu-sharing-webhook", async (req, res) => {
//     const postBody = req.body;
//     console.log("postBody?.restaurants?.[0]?.details?.menusharingcode => ", postBody?.restaurants?.[0]?.details?.menusharingcode);

//     if (postBody?.success === "1" && postBody?.restaurants?.[0]?.details?.menusharingcode) {
//         try {
//             const outletQuery = await supabaseInstance.from("Outlet").select(
//                 `outletId,outletName,petPoojaRestId,
//                 Menu_Parent_Categories!left(parent_category_id,parentCategoryName,outletId,parent_category_image_url,status,petpoojaParentCategoryId),
//                 Menu_Categories!left(categoryid,status,parent_category_id,categoryname,category_image_url,petpoojaCategoryId),
//                 Menu_Item!left(*),Variation!left(*), AddonGroup!left(*, Addongroupitems!left(*), ItemAddonGroups!left(*)) `
//             ).eq("petPoojaRestId", postBody?.restaurants?.[0]?.details?.menusharingcode).limit(1);

//             if (outletQuery?.data?.length > 0) {
//               const outletQueryData = outletQuery.data[0];
//               const parentcategoriesData = postBody.parentcategories;
//               const menuCategoriesData = postBody.categories;
//               const itemsData = postBody.items;
//               const addongroupsData = postBody.addongroups;

//               console.info("http_code: 200");
//               res.status(200).json({
//                 http_code: 200,
//                 success: true,
//                 error: "",
//               });

//               // Handle Parent Categories
//               if (parentcategoriesData?.length > 0) {
//                 for (let petpoojaParentCategoryObject of parentcategoriesData) {
//                   const mealpeParentCategoryObject =
//                     outletQueryData?.Menu_Parent_Categories?.find(
//                       (f) =>
//                         f.petpoojaParentCategoryId ===
//                         petpoojaParentCategoryObject.id
//                     );

//                   let p_c_body = {
//                     status:
//                       petpoojaParentCategoryObject?.status === "1"
//                         ? true
//                         : false,
//                     parentCategoryName: petpoojaParentCategoryObject?.name,
//                     petpoojaParentCategoryId: petpoojaParentCategoryObject?.id,
//                   };

//                   if (!mealpeParentCategoryObject) {
//                     p_c_body.outletId = outletQueryData.outletId;
//                     const parentcategoriesResponse = await supabaseInstance
//                       .from("Menu_Parent_Categories")
//                       .insert(p_c_body)
//                       .select("*")
//                       .maybeSingle();
//                     if (parentcategoriesResponse?.data) {
//                       petpoojaParentCategoryObject.mealpeResponse =
//                         parentcategoriesResponse?.data;
//                     }
//                   } else {
//                     const parentcategoriesResponse = await supabaseInstance
//                       .from("Menu_Parent_Categories")
//                       .update({
//                         parentCategoryName: p_c_body.parentCategoryName,
//                         status: p_c_body.status,
//                       })
//                       .eq(
//                         "parent_category_id",
//                         mealpeParentCategoryObject?.parent_category_id
//                       )
//                       .select("*")
//                       .maybeSingle();
//                     if (parentcategoriesResponse?.data) {
//                       petpoojaParentCategoryObject.mealpeResponse =
//                         parentcategoriesResponse?.data;
//                     }
//                   }
//                 }
//               }

//               // Handle Menu Categories
//               if (menuCategoriesData?.length > 0) {
//                 for (let petpoojaCategoryObject of menuCategoriesData) {
//                   const mealpeCategoryObject =
//                     outletQueryData.Menu_Categories.find(
//                       (f) =>
//                         f.petpoojaCategoryId ===
//                         petpoojaCategoryObject.categoryid
//                     );

//                   let c_body = {
//                     status:
//                       petpoojaCategoryObject?.active === "1" ? true : false,
//                     categoryname: petpoojaCategoryObject?.categoryname,
//                     category_image_url:
//                       petpoojaCategoryObject?.category_image_url,
//                     petpoojaCategoryId: petpoojaCategoryObject?.categoryid,
//                   };

//                   if (
//                     petpoojaCategoryObject?.parent_category_id &&
//                     petpoojaCategoryObject?.parent_category_id !== "0"
//                   ) {
//                     c_body.parent_category_id =
//                       parentcategoriesData.find(
//                         (f) =>
//                           f.id === petpoojaCategoryObject?.parent_category_id
//                       )?.mealpeResponse?.parent_category_id || null;
//                   }

//                   if (!mealpeCategoryObject) {
//                     c_body.outletId = outletQueryData.outletId;
//                     const menuCategoriesResponse = await supabaseInstance
//                       .from("Menu_Categories")
//                       .insert(c_body)
//                       .select("*")
//                       .maybeSingle();

//                     if (menuCategoriesResponse?.data) {
//                       petpoojaCategoryObject.mealpeResponse =
//                         menuCategoriesResponse?.data;
//                     }
//                   } else {
//                     const menuCategoriesResponse = await supabaseInstance
//                       .from("Menu_Categories")
//                       .update({
//                         status: c_body.status,
//                         parent_category_id: c_body.parent_category_id,
//                         categoryname: c_body.categoryname,
//                         category_image_url: c_body.category_image_url,
//                       })
//                       .eq("categoryid", mealpeCategoryObject.categoryid)
//                       .select("*")
//                       .maybeSingle();
//                     if (menuCategoriesResponse?.data) {
//                       petpoojaCategoryObject.mealpeResponse =
//                         menuCategoriesResponse?.data;
//                     }
//                   }
//                 }
//               }

//               // Handle Items
//               if (itemsData?.length > 0) {
//                 for (let petpoojaItemObject of itemsData) {
//                   let hasVariation =
//                     petpoojaItemObject?.variation?.length > 0 ? true : false;
//                   let hasAddons =
//                     petpoojaItemObject?.addon?.length > 0 ? true : false;

//                   const mealpeItemObject = outletQueryData.Menu_Item.find(
//                     (f) => f.petpoojaItemId === petpoojaItemObject.itemid
//                   );
//                   let itemId = mealpeItemObject?.itemid;

//                   let i_body = {
//                     itemname: petpoojaItemObject?.itemname,
//                     itemdescription: petpoojaItemObject?.itemdescription,
//                     price: parseFloat(
//                       hasVariation
//                         ? petpoojaItemObject?.variation[0]?.price
//                         : petpoojaItemObject?.price
//                     ),
//                     status: petpoojaItemObject?.active,
//                     minimumpreparationtime: parseFloat(
//                       petpoojaItemObject?.minimumpreparationtime
//                     ),
//                     item_image_url: petpoojaItemObject?.item_image_url,
//                     petpoojaItemId: petpoojaItemObject?.itemid,
//                   };

//                   if (petpoojaItemObject?.item_categoryid) {
//                     i_body.item_categoryid =
//                       menuCategoriesData.find(
//                         (f) =>
//                           f.categoryid === petpoojaItemObject?.item_categoryid
//                       )?.mealpeResponse?.categoryid || null;
//                   }

//                   // Inserting or updating the item
//                   if (!mealpeItemObject) {
//                     i_body.outletId = outletQueryData.outletId;
//                     const itemResponse = await supabaseInstance
//                       .from("Menu_Item")
//                       .insert(i_body)
//                       .select("*")
//                       .maybeSingle();
//                     if (itemResponse?.data) {
//                       petpoojaItemObject.mealpeResponse = itemResponse?.data;
//                       itemId = itemResponse?.data?.itemid;
//                     }
//                   } else {
//                     const itemResponse = await supabaseInstance
//                       .from("Menu_Item")
//                       .update({
//                         itemname: i_body.itemname,
//                         price: i_body.price,
//                         itemdescription: i_body.itemdescription,
//                         item_image_url: i_body.item_image_url,
//                         status: i_body.status,
//                         minimumpreparationtime: i_body.minimumpreparationtime,
//                         item_categoryid: i_body.item_categoryid,
//                       })
//                       .select("*")
//                       .eq("itemid", mealpeItemObject.itemid)
//                       .maybeSingle();
//                     if (itemResponse?.data) {
//                       petpoojaItemObject.mealpeResponse = itemResponse?.data;
//                       itemId = itemResponse?.data?.itemid;
//                     }
//                   }

//                   // Handle variations if present
//                   if (hasVariation) {
//                     const itemResponse = await supabaseInstance
//                       .from("Menu_Item")
//                       .update({ itemallowvariation: 1 })
//                       .eq("itemid", itemId)
//                       .select("*")
//                       .maybeSingle();
//                     for (let variation of petpoojaItemObject.variation) {
//                       const mealpeVariationObject =
//                         outletQueryData.Variation.find(
//                           (v) =>
//                             v.itemId === itemId && v.name === variation.name
//                         );

//                       let v_body = {
//                         name: variation.name,
//                         price: parseFloat(variation.price),
//                         attributeId: petpoojaItemObject?.item_attributeid,
//                         status: variation.active,
//                         isDefault: variation.isDefault || false,
//                         outletId: outletQueryData.outletId,
//                         itemId: itemId,
//                         itemArray: [itemId],
//                       };

//                       if (!mealpeVariationObject) {
//                         const variationResponse = await supabaseInstance
//                           .from("Variation")
//                           .insert(v_body)
//                           .select("*")
//                           .maybeSingle();
//                         if (variationResponse?.data) {
//                           variation.mealpeResponse = variationResponse.data;
//                         }
//                       } else {
//                         const variationResponse = await supabaseInstance
//                           .from("Variation")
//                           .update(v_body)
//                           .eq("variationId", mealpeVariationObject.variationId)
//                           .select("*")
//                           .maybeSingle();
//                         if (variationResponse?.data) {
//                           variation.mealpeResponse = variationResponse.data;
//                         }
//                       }
//                     }
//                   }

//                   // Handle Addons linked to Items
//                   if (hasAddons) {
//                     for (let addon of petpoojaItemObject.addon) {
//                       // find the addon group using itemid and addongroup_name (addongroup_name is in the object of addongroups array of the postbody  )
//                       const addonGroup =
//                         outletQueryData?.AddonGroup?.ItemAddonGroups?.find(
//                           (f) =>
//                             f.itemId === itemId &&
//                             f.addongroup_name ===
//                               addongroupsData?.find(
//                                 (f) => f.addongroupid === addon.addon_group_id
//                               )?.addongroup_name
//                         );

//                       console.log("630 addonGroup : ", addonGroup);

//                       let iag_body = {
//                         addongroup_name: addonGroup?.name,
//                         itemId,
//                         groupId: addonGroup?.id,
//                         min_selection: addon.addon_item_selection_min,
//                         max_selection: addon.addon_item_selection_max,
//                       };

//                       await supabaseInstance
//                         .from("ItemAddonGroups")
//                         .upsert(iag_body)
//                         .select("*");
//                     }

//                     // Update Menu_Item to allow addons
//                     await supabaseInstance
//                       .from("Menu_Item")
//                       .update({ itemallowaddon: "1", itemaddonbasedon: "0" })
//                       .eq("itemid", itemId);
//                   }

//                   // Handle Addons linked to Variations
//                   if (petpoojaItemObject?.variation?.length > 0) {
//                     for (let variation of petpoojaItemObject.variation) {
//                       const mealpeVariationObject =
//                         outletQueryData.Variation.find(
//                           (v) =>
//                             v.itemId === itemId && v.name === variation.name
//                         );

//                       if (variation.addon?.length > 0) {
//                         for (let addon of variation.addon) {
//                           const addonGroup = outletQueryData.AddonGroup.find(
//                             (f) => f.addongroupid === addon.addon_group_id
//                           );

//                           let iag_body = {
//                             addongroup_name: addonGroup?.name,
//                             variationId: mealpeVariationObject?.variationId,
//                             groupId: addonGroup?.id,
//                             min_selection: addon.addon_item_selection_min,
//                             max_selection: addon.addon_item_selection_max,
//                           };

//                           await supabaseInstance
//                             .from("ItemAddonGroups")
//                             .upsert(iag_body)
//                             .select("*");
//                         }

//                         const { itemId } = mealpeVariationObject;
//                         await supabaseInstance
//                           .from("Menu_Item")
//                           .update({
//                             itemallowaddon: "1",
//                             itemaddonbasedon: "1",
//                           })
//                           .eq("itemid", itemId);
//                       }
//                     }
//                   }
//                 }
//               }
//               // Handle Addon Groups and Items
//               if (addongroupsData?.length > 0) {
//                 for (let petpoojaAddonGroupObject of addongroupsData) {
//                   // Step 1: Get or Insert Addon Group
//                   const mealpeAddonGroupObject =
//                     outletQueryData.AddonGroup.find(
//                       (f) =>
//                         f.name === petpoojaAddonGroupObject.addongroup_name &&
//                         f.outletId === outletQueryData.outletId
//                     );

//                   let ag_body = {
//                     name: petpoojaAddonGroupObject?.addongroup_name,
//                     rank: petpoojaAddonGroupObject?.addongroup_rank,
//                     active: petpoojaAddonGroupObject?.active,
//                     outletId: outletQueryData.outletId,
//                   };

//                   let addonGroupId;
//                   if (!mealpeAddonGroupObject) {
//                     // Insert Addon Group if not exists
//                     const addonGroupResponse = await supabaseInstance
//                       .from("AddonGroup")
//                       .insert(ag_body)
//                       .select("*")
//                       .maybeSingle();
//                     if (addonGroupResponse?.data) {
//                       addonGroupId = addonGroupResponse.data.id; // Extract the UUID of the AddonGroup
//                     }
//                   } else {
//                     // Update Addon Group if exists
//                     const addonGroupResponse = await supabaseInstance
//                       .from("AddonGroup")
//                       .update(ag_body)
//                       .eq("id", mealpeAddonGroupObject.id)
//                       .select("*")
//                       .maybeSingle();
//                     if (addonGroupResponse?.data) {
//                       addonGroupId = addonGroupResponse.data.id; // Extract the UUID of the AddonGroup
//                     }
//                   }

//                   // Step 2: Link Addon Group to Items in ItemAddonGroups table
//                   if (
//                     addonGroupId &&
//                     petpoojaAddonGroupObject.addongroupitems?.length > 0
//                   ) {
//                     for (let item of itemsData) {
//                       if (item.addon?.length > 0) {
//                         for (let addon of item.addon) {
//                           if (
//                             addon.addon_group_id ===
//                             petpoojaAddonGroupObject.addongroupid
//                           ) {
//                             let iag_body = {
//                               itemId: parseInt(item.itemid),
//                               groupId: addonGroupId, // UUID of AddonGroup
//                               min_selection: parseInt(
//                                 addon.addon_item_selection_min
//                               ),
//                               max_selection: parseInt(
//                                 addon.addon_item_selection_max
//                               ),
//                               addongroup_name:
//                                 petpoojaAddonGroupObject.addongroup_name,
//                             };

//                             // Upsert ItemAddonGroups
//                             await supabaseInstance
//                               .from("ItemAddonGroups")
//                               .upsert(iag_body)
//                               .select("*")
//                               .maybeSingle();
//                           }
//                         }
//                       }
//                     }
//                   }
//                 }
//               }

//               console.info("menu-sharing-webhook success");
//             } else {
//                 if (outletQuery?.data?.length === 0) {
//                     console.info("http_code: 500", "Outlet not found in mealpe system.");
//                     res.status(500).json({ success: false, error: "Outlet not found in mealpe system." });
//                 } else {
//                     throw outletQuery?.error;
//                 }
//             }
//         } catch (error) {
//             console.error("menu-sharing-webhook => ", error);
//             console.info("http_code: 500");
//             res.status(500).json({ success: false, error: error?.message || error || JSON.stringify(error) });
//         }
//     } else {
//         console.info("http_code: 500", "Please pass postBody?.restaurants?.[0]?.details?.menusharingcode.");
//         res.status(500).json({ success: false, error: "Please pass postBody?.restaurants?.[0]?.details?.menusharingcode." });
//     }
// });









// async function removeItemAddonGroupRecords(menusharingcode) {
//   // get the outletId using menusharingcode
//   const { data: outletData, error: outletError } = await supabaseInstance
//     .from("Outlet")
//     .select("outletId")
//     .eq("petPoojaRestId", menusharingcode)
//     .limit(1);
//   if (outletError && outletError.code !== PGRST116) {
//     console.error("removeItemAddonGroupRecords => outletError", outletError);
//     return;
//   }

//   let outletId;
//   if (outletData?.length > 0) {
//     outletId = outletData[0].outletId;
//   }

//   const result = await supabaseInstance
//     .from("ItemAddonGroups")
//     .select(
//       `
//       groupId,
//       AddonGroup(id, outletId)  /* include columns from B if you need them */
//     `
//     )
//     .eq("AddonGroup.outletId", outletId);

//   if (result.error) {
//     console.error("Error fetching data:", result.error);
//   } else {
//     // extract groupId from the result.data in form of set
//     const groupIds = new Set(result.data.map((row) => row.groupId));

//     // delete the records from the ItemAddonGroups table
//     for (const groupId of groupIds) {
//       const { data, error } = await supabaseInstance
//         .from("ItemAddonGroups")
//         .delete()
//         .eq("groupId", groupId);
//       if (error) {
//         console.error("Error deleting data:", error);
//       }
//     }
//   }
// }



async function removeItemAddonGroupRecords() {
  try {
    // remove the records from the db where the groupId is null
    const { data, error } = await supabaseInstance
      .from("ItemAddonGroups")
      .delete()
      .is("groupId", null);

    if (error) {
      logger.error("Error deleting data in removeItemAddonGroupRecords:", error);
      throw error;
    }


  } catch (err) {
    logger.error("Unexpected error in removeItemAddonGroupRecords:", err);
  }
}




// itemaddongroup column null error 
router.post("/menu-sharing-webhook", async (req, res) => {
  const postBody = req.body;
  console.log("postBody?.restaurants?.[0]?.details?.menusharingcode => ", postBody?.restaurants?.[0]?.details?.menusharingcode);

  // call the function to remove the existing records from the itemaddongroup table
  await removeItemAddonGroupRecords();

  if (postBody?.success === "1" && postBody?.restaurants?.[0]?.details?.menusharingcode) {
      try {
          const outletQuery = await supabaseInstance.from("Outlet").select(
              `outletId,outletName,petPoojaRestId,
              Menu_Parent_Categories!left(parent_category_id,parentCategoryName,outletId,parent_category_image_url,status,petpoojaParentCategoryId),
              Menu_Categories!left(categoryid,status,parent_category_id,categoryname,category_image_url,petpoojaCategoryId),
              Menu_Item!left(*),Variation!left(*), AddonGroup!left(*, Addongroupitems!left(*), ItemAddonGroups!left(*)) `
          ).eq("petPoojaRestId", postBody?.restaurants?.[0]?.details?.menusharingcode).limit(1);

          if (outletQuery?.data?.length > 0) {
              const outletQueryData = outletQuery.data[0];
              const parentcategoriesData = postBody.parentcategories;
              const menuCategoriesData = postBody.categories;
              const itemsData = postBody.items;
              const addongroupsData = postBody.addongroups;
              

              console.info("http_code: 200");
              res.status(200).json({
                  http_code: 200,
                  success: true,
                  error: "",
              });

              // Handle Parent Categories
              if (parentcategoriesData?.length > 0) {
                  for (let petpoojaParentCategoryObject of parentcategoriesData) {
                      const mealpeParentCategoryObject = outletQueryData?.Menu_Parent_Categories?.find(f => f.petpoojaParentCategoryId === petpoojaParentCategoryObject.id);

                      let p_c_body = {
                          status: petpoojaParentCategoryObject?.status === "1" ? true : false,
                          parentCategoryName: petpoojaParentCategoryObject?.name,
                          petpoojaParentCategoryId: petpoojaParentCategoryObject?.id
                      }

                      if (!mealpeParentCategoryObject) {
                          p_c_body.outletId = outletQueryData.outletId;
                          const parentcategoriesResponse = await supabaseInstance.from("Menu_Parent_Categories").insert(p_c_body).select("*").maybeSingle();
                          if (parentcategoriesResponse?.data) {
                              petpoojaParentCategoryObject.mealpeResponse = parentcategoriesResponse?.data;
                          }
                      } else {
                          const parentcategoriesResponse = await supabaseInstance.from("Menu_Parent_Categories").update({ parentCategoryName: p_c_body.parentCategoryName, status: p_c_body.status }).eq("parent_category_id", mealpeParentCategoryObject?.parent_category_id).select("*").maybeSingle();
                          if (parentcategoriesResponse?.data) {
                              petpoojaParentCategoryObject.mealpeResponse = parentcategoriesResponse?.data;
                          }
                      }
                  }
              }

              // Handle Menu Categories
              if (menuCategoriesData?.length > 0) {
                  for (let petpoojaCategoryObject of menuCategoriesData) {
                      const mealpeCategoryObject = outletQueryData.Menu_Categories.find(f => f.petpoojaCategoryId === petpoojaCategoryObject.categoryid);

                      let c_body = {
                          status: petpoojaCategoryObject?.active === "1" ? true : false,
                          categoryname: petpoojaCategoryObject?.categoryname,
                          category_image_url: petpoojaCategoryObject?.category_image_url,
                          petpoojaCategoryId: petpoojaCategoryObject?.categoryid
                      }

                      if (petpoojaCategoryObject?.parent_category_id && petpoojaCategoryObject?.parent_category_id !== "0") {
                          c_body.parent_category_id = parentcategoriesData.find(f => f.id === petpoojaCategoryObject?.parent_category_id)?.mealpeResponse?.parent_category_id || null;
                      }

                      if (!mealpeCategoryObject) {
                          c_body.outletId = outletQueryData.outletId;
                          const menuCategoriesResponse = await supabaseInstance.from("Menu_Categories").insert(c_body).select("*").maybeSingle();

                          if (menuCategoriesResponse?.data) {
                              petpoojaCategoryObject.mealpeResponse = menuCategoriesResponse?.data;
                          }
                      } else {
                          const menuCategoriesResponse = await supabaseInstance
                              .from("Menu_Categories")
                              .update({ status: c_body.status, parent_category_id: c_body.parent_category_id, categoryname: c_body.categoryname, category_image_url: c_body.category_image_url })
                              .eq("categoryid", mealpeCategoryObject.categoryid)
                              .select("*")
                              .maybeSingle();
                          if (menuCategoriesResponse?.data) {
                              petpoojaCategoryObject.mealpeResponse = menuCategoriesResponse?.data;
                          }
                      }
                  }
              }


              // Handle Addon Groups and Items
              if (addongroupsData?.length > 0) {
                  
                for (let petpoojaAddonGroupObject of addongroupsData) {
                  
                    const mealpeAddonGroupObject = outletQueryData.AddonGroup.find(f => (f.petpoojaGroupId=== petpoojaAddonGroupObject.addongroupid));

                  
                    let ag_body = {
                        name: petpoojaAddonGroupObject?.addongroup_name,
                        rank: petpoojaAddonGroupObject?.addongroup_rank,
                        active: petpoojaAddonGroupObject?.active,
                        outletId: outletQueryData.outletId,
                        petpoojaGroupId: petpoojaAddonGroupObject?.addongroupid || null
                    };

                    let addonGroupId;
                    if (!mealpeAddonGroupObject) {
                        const addonGroupResponse = await supabaseInstance.from("AddonGroup").insert(ag_body).select("*").maybeSingle();
                        if (addonGroupResponse?.data) {
                            addonGroupId = addonGroupResponse.data.id;
                        }
                    } else {
                        const addonGroupResponse = await supabaseInstance
                            .from("AddonGroup")
                            .update(ag_body)
                            .eq("addongroupid", mealpeAddonGroupObject.addongroupid)
                            .select("*")
                            .maybeSingle();
                        if (addonGroupResponse?.data) {
                            addonGroupId = addonGroupResponse.data.id;
                        }
                    }

                    // Handle Addon Group Items
                    if (petpoojaAddonGroupObject.addongroupitems?.length > 0) {
                        for (let addongroupitem of petpoojaAddonGroupObject.addongroupitems) {
                            const mealpeAddonItemObject = outletQueryData.AddonGroup.Addongroupitems?.find(f => f.addonitemid === addongroupitem.addonitemid);

                            let agi_body = {
                                name: addongroupitem.addonitem_name,
                                price: parseFloat(addongroupitem.addonitem_price),
                                active: addongroupitem.active,
                                addonGroupId: addonGroupId,
                                rank: addongroupitem.addonitem_rank,
                                attributeId: addongroupitem.attributes
                            };

                            if (!mealpeAddonItemObject) {
                                await supabaseInstance.from("Addongroupitems").insert(agi_body).select("*").maybeSingle();
                            } else {
                                await supabaseInstance.from("Addongroupitems").update(agi_body).eq("addonitemid", mealpeAddonItemObject.addonitemid).select("*").maybeSingle();
                            }
                        }
                    }
                }
            }

              // Handle Items
              if (itemsData?.length > 0) {
                  for (let petpoojaItemObject of itemsData) {
                    let hasVariation =
                      petpoojaItemObject?.variation?.length > 0 ? true : false;
                    let hasAddons =
                      petpoojaItemObject?.addon?.length > 0 ? true : false;

                    const mealpeItemObject = outletQueryData.Menu_Item.find(
                      (f) => f.petpoojaItemId === petpoojaItemObject.itemid
                    );
                    let itemId = mealpeItemObject?.itemid;

                    let i_body = {
                      itemname: petpoojaItemObject?.itemname,
                      itemdescription: petpoojaItemObject?.itemdescription,
                      price: parseFloat(
                        hasVariation
                          ? petpoojaItemObject?.variation[0]?.price
                          : petpoojaItemObject?.price
                      ),
                      status: petpoojaItemObject?.active,
                      minimumpreparationtime: parseFloat(
                        petpoojaItemObject?.minimumpreparationtime
                      ),
                      item_image_url: petpoojaItemObject?.item_image_url,
                      petpoojaItemId: petpoojaItemObject?.itemid,
                    };

                    if (petpoojaItemObject?.item_categoryid) {
                      i_body.item_categoryid =
                        menuCategoriesData.find(
                          (f) =>
                            f.categoryid === petpoojaItemObject?.item_categoryid
                        )?.mealpeResponse?.categoryid || null;
                    }

                    // Inserting or updating the item
                    if (!mealpeItemObject) {
                      i_body.outletId = outletQueryData.outletId;
                      const itemResponse = await supabaseInstance
                        .from("Menu_Item")
                        .insert(i_body)
                        .select("*")
                        .maybeSingle();
                      if (itemResponse?.data) {
                        petpoojaItemObject.mealpeResponse = itemResponse?.data;
                        itemId = itemResponse?.data?.itemid;
                      }
                    } else {
                      const itemResponse = await supabaseInstance
                        .from("Menu_Item")
                        .update({
                          itemname: i_body.itemname,
                          price: i_body.price,
                          itemdescription: i_body.itemdescription,
                          item_image_url: i_body.item_image_url,
                          status: i_body.status,
                          minimumpreparationtime: i_body.minimumpreparationtime,
                          item_categoryid: i_body.item_categoryid,
                        })
                        .select("*")
                        .eq("itemid", mealpeItemObject.itemid)
                        .maybeSingle();
                      if (itemResponse?.data) {
                        petpoojaItemObject.mealpeResponse = itemResponse?.data;
                        itemId = itemResponse?.data?.itemid;
                      }
                    }

                    // Handle variations if present
                    if (hasVariation) {
                      const itemResponse = await supabaseInstance
                        .from("Menu_Item")
                        .update({ itemallowvariation: 1 })
                        .eq("itemid", itemId)
                        .select("*")
                        .maybeSingle();
                      for (let variation of petpoojaItemObject.variation) {
                        const mealpeVariationObject =
                          outletQueryData.Variation.find(
                            (v) =>
                              v.itemId === itemId && v.name === variation.name
                          );

                        let v_body = {
                          name: variation.name,
                          price: parseFloat(variation.price),
                          attributeId: petpoojaItemObject?.item_attributeid,
                          status: variation.active,
                          isDefault: variation.isDefault || false,
                          outletId: outletQueryData.outletId,
                          itemId: itemId,
                          itemArray: [itemId],
                        };

                        if (!mealpeVariationObject) {
                          const variationResponse = await supabaseInstance
                            .from("Variation")
                            .insert(v_body)
                            .select("*")
                            .maybeSingle();
                          if (variationResponse?.data) {
                            variation.mealpeResponse = variationResponse.data;
                          }
                        } else {
                          const variationResponse = await supabaseInstance
                            .from("Variation")
                            .update(v_body)
                            .eq(
                              "variationId",
                              mealpeVariationObject.variationId
                            )
                            .select("*")
                            .maybeSingle();
                          if (variationResponse?.data) {
                            variation.mealpeResponse = variationResponse.data;
                          }
                        }
                      }
                    }

                    // Handle Addons linked to Items
                    // if (hasAddons) {
                    //     console.log('\n\n\n\n\n\noutletQueryData.AddonGroup : ' , outletQueryData.AddonGroup);

                    //     for (let addon of petpoojaItemObject.addon) {

                    //         // find the addon group using itemid and addongroup_name (addongroup_name is in the object of addongroups array of the postbody  )
                    //         const addonGroup = outletQueryData?.AddonGroup?.find(f => f.petpoojaGroupId == addon.addon_group_id);

                    //         console.log('630 addonGroup : ' , addonGroup);

                    //         let iag_body = {
                    //             addongroup_name: addonGroup?.name,
                    //             itemId,
                    //             groupId: addonGroup?.id,
                    //             min_selection: addon.addon_item_selection_min,
                    //             max_selection: addon.addon_item_selection_max,
                    //         };
                    //         // check if the itemaddon group is already present in the table
                    //         // if not present then insert it
                    //         // if present then update it
                    //         if(!outletQueryData?.AddonGroup?.ItemAddonGroups?.find(f => f.itemId === itemId && f.groupId === addonGroup?.id)){
                    //         await supabaseInstance.from("ItemAddonGroups").insert(iag_body).select("*");
                    //         }
                    //         else{
                    //         await supabaseInstance.from("ItemAddonGroups").update(iag_body).eq('itemId',itemId).eq('groupId',addonGroup?.id).select("*");
                    //         }
                    //     }

                    //     // Update Menu_Item to allow addons
                    //     await supabaseInstance.from("Menu_Item").update({ itemallowaddon: "1", itemaddonbasedon: "0" }).eq("itemid", itemId);
                    // }

                    if (hasAddons) {
                      console.log(
                        "\n\n\n\n\n\noutletQueryData.AddonGroup : ",
                        outletQueryData.AddonGroup
                      );

                      for (let addon of petpoojaItemObject.addon) {
                        // Find the addon group using itemid and addongroup_name
                        const addonGroup = outletQueryData?.AddonGroup?.find(
                          (f) => f.petpoojaGroupId == addon.addon_group_id
                        );

                        console.log("630 addonGroup : ", addonGroup);

                        let iag_body = {
                          addongroup_name: addonGroup?.name,
                          itemId,
                          groupId: addonGroup?.id,
                          min_selection: addon.addon_item_selection_min,
                          max_selection: addon.addon_item_selection_max,
                        };

                        // Check if the ItemAddonGroup already exists in the table
                        const { data: existingEntry, error } =
                          await supabaseInstance
                            .from("ItemAddonGroups")
                            .select("*")
                            .eq("itemId", itemId)
                            .eq("groupId", addonGroup?.id)
                            .single();

                        if (error && error.code !== "PGRST116") {
                          // PGRST116 is the code for no data found
                          logger.error(
                            "Error checking for existing entry:",
                            error
                          );
                        }

                        if (!existingEntry) {
                          // Insert a new record if it doesn't exist
                          console.log(
                            "Inserting new ItemAddonGroup:",
                            iag_body
                          );
                          const { data, error } = await supabaseInstance
                            .from("ItemAddonGroups")
                            .insert(iag_body)
                            .select("*");
                          if (error)
                            logger.error(
                              "Error inserting ItemAddonGroup:",
                              error
                            );
                        } else {
                          // Update the existing record
                          console.log(
                            "Updating existing ItemAddonGroup:",
                            iag_body
                          );
                          const { data, error } = await supabaseInstance
                            .from("ItemAddonGroups")
                            .update(iag_body)
                            .eq("itemId", itemId)
                            .eq("groupId", addonGroup?.id)
                            .select("*");
                          if (error)
                            logger.error(
                              "Error updating ItemAddonGroup:",
                              error
                            );
                        }
                      }

                      // Update Menu_Item to allow addons
                      await supabaseInstance
                        .from("Menu_Item")
                        .update({ itemallowaddon: "1", itemaddonbasedon: "0" })
                        .eq("itemid", itemId);
                    }

                    // // Handle Addons linked to Variations
                    // if (petpoojaItemObject?.variation?.length > 0) {
                    //     for (let variation of petpoojaItemObject.variation) {
                    //         const mealpeVariationObject = outletQueryData.Variation.find(v => v.itemId === itemId && v.name === variation.name);

                    //         if (variation.addon?.length > 0) {
                    //             for (let addon of variation.addon) {
                    //                 const addonGroup = outletQueryData.AddonGroup.find(f => f.petpoojaGroupId === addon.addon_group_id);

                    //                 let iag_body = {
                    //                     addongroup_name: addonGroup?.name,
                    //                     variationId: mealpeVariationObject?.variationId,
                    //                     groupId: addonGroup?.id,
                    //                     min_selection: addon.addon_item_selection_min,
                    //                     max_selection: addon.addon_item_selection_max,
                    //                 };

                    //                 if(!outletQueryData?.AddonGroup?.ItemAddonGroups?.find(f => f.variationId === mealpeVariationObject?.variationId && f.groupId === addonGroup?.id)){
                    //                 await supabaseInstance.from("ItemAddonGroups").insert(iag_body).select("*");
                    //                 } else {
                    //                 await supabaseInstance.from("ItemAddonGroups").update(iag_body).eq('variationId',mealpeVariationObject?.variationId).eq('groupId',addonGroup?.id).select("*");
                    //                 }
                    //             }

                    //             const { itemId } = mealpeVariationObject;
                    //             await supabaseInstance.from("Menu_Item").update({ itemallowaddon: "1", itemaddonbasedon: "1" }).eq("itemid", itemId);
                    //         }
                    //     }
                    // }

                    // Handle Addons linked to Variations
                    if (petpoojaItemObject?.variation?.length > 0) {
                      for (let variation of petpoojaItemObject.variation) {
                        const mealpeVariationObject =
                          outletQueryData.Variation.find(
                            (v) =>
                              v.itemId === itemId && v.name === variation.name
                          );

                        if (variation.addon?.length > 0) {
                          for (let addon of variation.addon) {
                            const addonGroup = outletQueryData.AddonGroup.find(
                              (f) => f.petpoojaGroupId === addon.addon_group_id
                            );

                            let iag_body = {
                              addongroup_name: addonGroup?.name,
                              variationId: mealpeVariationObject?.variationId,
                              groupId: addonGroup?.id,
                              min_selection: addon.addon_item_selection_min,
                              max_selection: addon.addon_item_selection_max,
                            };

                            // Check if the record already exists
                            const { data: existingEntry, error } =
                              await supabaseInstance
                                .from("ItemAddonGroups")
                                .select("*")
                                .eq(
                                  "variationId",
                                  mealpeVariationObject?.variationId
                                )
                                .eq("groupId", addonGroup?.id)
                                .single();

                            if (error && error.code !== "PGRST116") {
                              // PGRST116 is the code for no data found
                              logger.error(
                                "Error checking for existing entry:",
                                error
                              );
                            }

                            if (!existingEntry) {
                              // Insert a new record if it doesn't exist
                              console.log(
                                "Inserting new ItemAddonGroup:",
                                iag_body
                              );
                              const { data, error } = await supabaseInstance
                                .from("ItemAddonGroups")
                                .insert(iag_body)
                                .select("*");
                              if (error)
                                logger.error(
                                  "Error inserting ItemAddonGroup:",
                                  error
                                );
                            } else {
                              // Update the existing record
                              console.log(
                                "Updating existing ItemAddonGroup:",
                                iag_body
                              );
                              const { data, error } = await supabaseInstance
                                .from("ItemAddonGroups")
                                .update(iag_body)
                                .eq(
                                  "variationId",
                                  mealpeVariationObject?.variationId
                                )
                                .eq("groupId", addonGroup?.id)
                                .select("*");
                              if (error)
                                logger.error(
                                  "Error updating ItemAddonGroup:",
                                  error
                                );
                            }
                          }

                          // Update the Menu_Item to allow addons
                          const { itemId } = mealpeVariationObject;
                          await supabaseInstance
                            .from("Menu_Item")
                            .update({
                              itemallowaddon: "1",
                              itemaddonbasedon: "1",
                            })
                            .eq("itemid", itemId);
                        }
                      }
                    }
                  }
              }
              console.info("menu-sharing-webhook success");
          } else {
              if (outletQuery?.data?.length === 0) {
                  console.info("http_code: 500", "Outlet not found in mealpe system.");
                  res.status(500).json({ success: false, error: "Outlet not found in mealpe system." });
              } else {
                  throw outletQuery?.error;
              }
          }
      } catch (error) {
          logger.error("menu-sharing-webhook => ", error);
          console.info("http_code: 500");
          res.status(500).json({ success: false, error: error?.message || error || JSON.stringify(error) });
      }
  } else {
      console.info("http_code: 500", "Please pass postBody?.restaurants?.[0]?.details?.menusharingcode.");
      res.status(500).json({ success: false, error: "Please pass postBody?.restaurants?.[0]?.details?.menusharingcode." });
  }
});



const axios = require("axios");
const petpoojaconfig = require("../../configs/petpoojaConfig").config;

// router.post('/restaurant-onboarding', async (req, res) => {
//     const {
//       request_type, outlet_id, brand_display_name, owner_name, outlet_name,
//       address, landmark, area, state, pincode, city, business_contact,
//       merchant_number, email, lat, long, is_support_self_delivery, serving_radius,
//       fssai_no, fssai_image, pan_no, pan_image, gst_no, gst_image, restaurant_logo,
//       cuisines, res_timings, callback_url
//     } = req.body;
  
//     let status;
//     // Basic validation
//     if (!request_type || !outlet_id || !owner_name || !outlet_name ||
//         !address || !landmark || !area || !state || !pincode || !city ||
//         !business_contact || !merchant_number || !email || !lat || !long ||
//         !is_support_self_delivery || !fssai_no || !fssai_image || !pan_no ||
//         !pan_image || !restaurant_logo || !cuisines || !res_timings || !callback_url) {
//         status = "0";
//         res.status(400).json({
//         status,
//         message: "Missing required fields"
//       });
//     }else{
  
//         // Process the restaurant details (e.g., save to a database or forward to Petpooja)
//         // console.log('Received restaurant details:', req.body);

//         try {
//             const { error } = await supabaseInstance
//             .from('Onboarding_Response')
//             .insert([{ data: req.body, pincode: req.body.pincode }])
//             .single();
    
//             if (error) {
//                 console.error('Error inserting into Onboarding_Response:', error);
//             } else {
    
//                 status = "1";
//                 // Respond to the client
//                 // res.status(200).json({
//                 //     status,
//                 //     message: "Restaurant details received",
//                 //     // data: req.body
//                 // });
//             }
//         } catch (error) {
//             console.error('Error inserting into Onboarding_Response:', error);
//             res.status(500).json({
//                 status: "0",
//                 message: "Internal server error",
//                 error
//             });
//         }
//     }
//     // Trigger the callback URL
//     try {
//       const payload = {
//         outlet_id: outlet_id,
//         status ,
//         message: status == "1" ? "Mapped successfully" : "Mapping failed"
//       };
  
//       const callbackResponse = await axios.post(petpoojaconfig.restaurant_callback_api, payload);
//       console.log('Callback URL response:', callbackResponse);
//       return res.status(200).json({
//         callbackResponse,
//       });
//     } catch (error) {
//       console.error('Error triggering callback URL:', error);
//     }
//   });

router.post('/restaurant-onboarding', async (req, res) => {
    const {
        request_type, outlet_id, brand_display_name, owner_name, outlet_name,
        address, landmark, area, state, pincode, city, business_contact,
        merchant_number, email, lat, long, is_support_self_delivery, serving_radius,
        fssai_no, fssai_image, pan_no, pan_image, gst_no, gst_image, restaurant_logo,
        cuisines, res_timings, callback_url
    } = req.body;

    // Basic validation
    if (!request_type || !outlet_id || !owner_name || !outlet_name ||
        !address || !landmark || !area || !state || !pincode || !city ||
        !business_contact || !merchant_number || !email || !lat || !long ||
        typeof is_support_self_delivery === 'undefined' || !fssai_no || !fssai_image ||
        !pan_no || !pan_image || !restaurant_logo || !cuisines || !res_timings || !callback_url) {
        return res.status(400).json({
            status: "0",
            message: "Missing required fields"
        });
    }

    try {
        // Process the restaurant details (e.g., save to a database or forward to Petpooja)
        const { error } = await supabaseInstance
            .from('Onboarding_Response')
            .insert([{ data: req.body, pincode: req.body.pincode }])
            .single();

        if (error) {
            logger.error('Error inserting into Onboarding_Response:', error);
            return res.status(500).json({
                status: "0",
                message: "Error saving data",
                error
            });
        }

        // If the insertion is successful, set status to "1"
        const status = "1";
        const payload = {
            outlet_id,
            status,
            message: "Mapped successfully"
        };

        try {
            const callbackResponse = await axios.post(callback_url, payload, {
                headers: {
                    'access-token': '43567059669fc7dbb3453a2d01fba500e5287f2c'  
                }
            });
            console.log('Callback URL response:', callbackResponse.data);
            return res.status(200).json({
                status,
                message: "Restaurant details received and callback successful",
                callbackResponse: callbackResponse.data
            });
        } catch (callbackError) {
            logger.error('Error triggering callback URL:', callbackError);
            return res.status(200).json({
                status,
                message: "Restaurant details received but callback failed",
                callbackError
            });
        }
    } catch (dbError) {
        logger.error('Error inserting into Onboarding_Response:', dbError);
        return res.status(500).json({
            status: "0",
            message: "Internal server error",
            error: dbError
        });
    }
});

//   // POST /rest_callback
//   router.post('/rest_callback', async (req, res) => {
//     const { outlet_id, status, message } = req.body;
  
//     if (!outlet_id || status === undefined || !message) {
//       return res.status(400).json({
//         code: "400",
//         success: "false",
//         message: "Invalid request body"
//       });
//     }
  
//     try {
//       if (status === "1") {
//         console.log(`Mapping successful for outlet ${outlet_id}: ${message}`);
//       } else {
//         console.log(`Mapping failed for outlet ${outlet_id}: ${message}`);
//       }
  
//       return res.status(200).json({
//         code: "200",
//         success: "true",
//         message: "Your request is received"
//       });
//     } catch (error) {
//       console.error('Error processing callback:', error);
//       return res.status(500).json({
//         code: "500",
//         success: "false",
//         message: "Internal server error"
//       });
//     }
//   });

module.exports = router;



                            /*
                                1. Find the addon group using addongroupid from the item object
                                2. get the addon group name from the addon group object
                                3. get itemid from the our db
                                4. check if the addon group name(from AddonGroup table) and itemid (from ItemAddonGroups table) is present in the db.
                                5. if not present then insert the record in the db
                                6. if present then update the record in the db

                            */