<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Invoice Generator</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            align-items: start;
        }

        .form-panel {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .preview-panel {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 20px;
        }

        h1 {
            color: #2d3748;
            margin-bottom: 30px;
            text-align: center;
            font-size: 2.2rem;
            font-weight: 700;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 0.9rem;
        }

        input,
        textarea,
        select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        input:focus,
        textarea:focus,
        select:focus {
            outline: none;
            border-color: #4a5568;
            background: white;
            box-shadow: 0 0 0 3px rgba(74, 85, 104, 0.1);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .form-row-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #2d3748;
            margin: 25px 0 15px 0;
            padding-bottom: 8px;
            border-bottom: 2px solid #4a5568;
        }

        .item-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
            gap: 10px;
            margin-bottom: 10px;
            align-items: end;
        }

        .add-item-btn {
            background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin-top: 15px;
            transition: all 0.3s ease;
        }

        .add-item-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(74, 85, 104, 0.3);
        }

        .remove-item-btn {
            background: #718096;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
        }

        .generate-btn {
            background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 700;
            width: 100%;
            margin-top: 30px;
            transition: all 0.3s ease;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(45, 55, 72, 0.3);
        }

        .download-btn {
            background: linear-gradient(135deg, #4a5568 0%, #718096 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 700;
            width: 100%;
            margin-top: 15px;
            transition: all 0.3s ease;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(74, 85, 104, 0.3);
        }

        /* Invoice Preview Styles */
        .invoice-preview {
            font-family: Arial, sans-serif;
            font-size: 12px; /* Compact main content */
            line-height: 1.25;
        }

        .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2px;
            padding-bottom: 10px;
            border-bottom: 2px solid #2d3748;
            font-size: 20px;
        }

        .invoice-title {
            font-size: 20px;
            font-weight: 700;
            color: #2d3748;
        }

        .company-logo {
            font-size: 18px;
            font-weight: 700;
            color: #4a5568;
        }

        .invoice-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 10px;
        }

        .billed-section {
            background: #f7fafc;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #4a5568;
        }

        .section-header {
            font-size: 15px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 15px;
        }

        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .invoice-table th {
            background: #2d3748;
            color: white;
            padding: 10px 6px;
            text-align: center;
            font-weight: 700;
            font-size: 13px;
        }

        .invoice-table td {
            padding: 8px 6px;
            text-align: center;
            border-bottom: 1px solid #e1e5e9;
        }

        .invoice-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .total-section {
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 10px;
        }

        .bank-details {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 10px;
            width: 45%;
        }

        .totals {
            width: 45%;
            text-align: right;
        }

        .totals table {
            width: 100%;
            margin-top: 10px;
        }

        .totals td {
            padding: 8px 15px;
            border-bottom: 1px solid #e1e5e9;
        }

        .grand-total {
            font-size: 20px;
            font-weight: 700;
            color: #2d3748;
            border-top: 2px solid #2d3748 !important;
            padding-top: 15px !important;
        }

        .signature-section {
            margin-top: 40px;
            text-align: right;
            font-size: 11px;
        }

        .signature-line {
            border-bottom: 2px solid #2d3748;
            width: 200px;
            margin-left: auto;
            margin-bottom: 1px;
            font-size: 11px;
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .form-row,
            .form-row-3 {
                grid-template-columns: 1fr;
            }

            .item-row {
                grid-template-columns: 1fr;
            }

            .invoice-info {
                grid-template-columns: 1fr;
            }

            .total-section {
                flex-direction: column;
                gap: 20px;
            }

            .bank-details,
            .totals {
                width: 100%;
            }
        }

        .invoice-table th, .invoice-table td {
            font-size: 12px;
            line-height: 1.2;
        }
        .totals td, .bank-details, .billed-section, .section-header, .signature-section {
            font-size: 12px;
        }
        .remove-item-btn {
            font-size: 11px;
        }
        .signature-line {
            font-size: 11px;
        }

        .gst-field {
            transition: all 0.3s ease;
        }

        .gst-field.hidden {
            display: none;
        }

        input[type="checkbox"] {
            transform: scale(1.2);
            margin-right: 8px;
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- Form Panel -->
        <div class="form-panel">
            <h1>Invoice Generator</h1>

            <!-- Invoice Details -->
            <div class="section-title">Invoice Details</div>
            <div class="form-row">
                <div class="form-group">
                    <label for="invoiceNo">Invoice Number</label>
                    <input type="text" id="invoiceNo" placeholder="2025-26/001">
                </div>
                <div class="form-group">
                    <label for="invoiceDate">Invoice Date</label>
                    <input type="date" id="invoiceDate">
                </div>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="includeGST" checked style="margin-right: 8px;">
                    Include GST in Invoice
                </label>
            </div>

            <!-- Billed By -->
            <div class="section-title">Billed By</div>
            <div class="form-group">
                <label for="companyName">Company Name</label>
                <input type="text" id="companyName" placeholder="Your Company Name">
            </div>
            <div class="form-group">
                <label for="companyAddress">Address</label>
                <textarea id="companyAddress" rows="3" placeholder="Complete address"></textarea>
            </div>
            <div class="form-row">
                <div class="form-group gst-field">
                    <label for="gstin">GSTIN</label>
                    <input type="text" id="gstin" placeholder="GSTIN Number">
                </div>
                <div class="form-group">
                    <label for="pan">PAN</label>
                    <input type="text" id="pan" placeholder="PAN Number">
                </div>
            </div>

            <!-- Billed To -->
            <div class="section-title">Billed To</div>
            <div class="form-group">
                <label for="clientName">Client Name</label>
                <input type="text" id="clientName" placeholder="Client Company Name">
            </div>
            <div class="form-group">
                <label for="clientAddress">Client Address</label>
                <textarea id="clientAddress" rows="3" placeholder="Client complete address"></textarea>
            </div>
            <div class="form-row-3">
                <div class="form-group gst-field">
                    <label for="clientGstin">Client GSTIN</label>
                    <input type="text" id="clientGstin" placeholder="Client GSTIN">
                </div>
                <div class="form-group">
                    <label for="clientPan">Client PAN</label>
                    <input type="text" id="clientPan" placeholder="Client PAN">
                </div>
                <div class="form-group">
                    <label for="clientPhone">Phone</label>
                    <input type="text" id="clientPhone" placeholder="Phone Number">
                </div>
            </div>
            <div class="form-group">
                <label for="clientEmail">Email</label>
                <input type="email" id="clientEmail" placeholder="<EMAIL>">
            </div>

            <!-- Items -->
            <div class="section-title">Items/Services</div>
            <div id="itemsContainer">
                <div class="item-row">
                    <div class="form-group">
                        <label>Description</label>
                        <input type="text" class="item-description" placeholder="Service description">
                    </div>
                    <div class="form-group gst-field">
                        <label>GST Rate (%)</label>
                        <input type="number" class="item-gst-rate" placeholder="18" step="0.01">
                    </div>
                    <div class="form-group">
                        <label>Quantity</label>
                        <input type="number" class="item-quantity" placeholder="1" step="1">
                    </div>
                    <div class="form-group">
                        <label>Rate (₹)</label>
                        <input type="number" class="item-rate" placeholder="1000" step="0.01">
                    </div>
                    <div class="form-group">
                        <label>Action</label>
                        <button type="button" class="remove-item-btn" onclick="removeItem(this)">Remove</button>
                    </div>
                </div>
            </div>
            <button type="button" class="add-item-btn" onclick="addItem()">Add Item</button>

            <!-- Bank Details -->
            <div class="section-title">Bank Details</div>
            <div class="form-row">
                <div class="form-group">
                    <label for="accountName">Account Name</label>
                    <input type="text" id="accountName" placeholder="Account Holder Name">
                </div>
                <div class="form-group">
                    <label for="accountNumber">Account Number</label>
                    <input type="text" id="accountNumber" placeholder="Account Number">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="ifsc">IFSC Code</label>
                    <input type="text" id="ifsc" placeholder="IFSC Code">
                </div>
                <div class="form-group">
                    <label for="bankName">Bank Name</label>
                    <input type="text" id="bankName" placeholder="Bank Name">
                </div>
            </div>
            <div class="form-group">
                <label for="swiftCode">SWIFT Code</label>
                <input type="text" id="swiftCode" placeholder="SWIFT Code (Optional)">
            </div>

            <button type="button" class="generate-btn" onclick="generatePreview()">Generate Preview</button>
            <button type="button" class="download-btn" onclick="downloadPDF()">Download PDF</button>
        </div>

        <!-- Preview Panel -->
        <div id="preview-panel" class="preview-panel">
            <div id="invoicePreview" class="invoice-preview">
                <div style="text-align: center; color: #666; padding: 50px;">
                    Fill the form and click "Generate Preview" to see your invoice
                </div>
            </div>
        </div>
    </div>

    <script>
        let itemCount = 1;

        function toggleGSTFields() {
            const includeGST = document.getElementById('includeGST').checked;
            const gstFields = document.querySelectorAll('.gst-field');

            gstFields.forEach(field => {
                if (includeGST) {
                    field.classList.remove('hidden');
                } else {
                    field.classList.add('hidden');
                }
            });
        }

        function addItem() {
            itemCount++;
            const itemsContainer = document.getElementById('itemsContainer');
            const newItem = document.createElement('div');
            newItem.className = 'item-row';
            newItem.innerHTML = `
                <div class="form-group">
                    <label>Description</label>
                    <input type="text" class="item-description" placeholder="Service description">
                </div>
                <div class="form-group gst-field">
                    <label>GST Rate (%)</label>
                    <input type="number" class="item-gst-rate" placeholder="18" step="0.01">
                </div>
                <div class="form-group">
                    <label>Quantity</label>
                    <input type="number" class="item-quantity" placeholder="1" step="1">
                </div>
                <div class="form-group">
                    <label>Rate (₹)</label>
                    <input type="number" class="item-rate" placeholder="1000" step="0.01">
                </div>
                <div class="form-group">
                    <label>Action</label>
                    <button type="button" class="remove-item-btn" onclick="removeItem(this)">Remove</button>
                </div>
            `;
            itemsContainer.appendChild(newItem);

            // Apply GST visibility to new item
            toggleGSTFields();
        }

        function removeItem(button) {
            if (document.querySelectorAll('.item-row').length > 1) {
                button.closest('.item-row').remove();
            }
        }

        function numberToWords(num) {
            const ones = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine'];
            const tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];
            const teens = ['Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];

            if (num === 0) return 'Zero';

            let result = '';

            if (num >= 10000000) {
                result += numberToWords(Math.floor(num / 10000000)) + ' Crore ';
                num %= 10000000;
            }

            if (num >= 100000) {
                result += numberToWords(Math.floor(num / 100000)) + ' Lakh ';
                num %= 100000;
            }

            if (num >= 1000) {
                result += numberToWords(Math.floor(num / 1000)) + ' Thousand ';
                num %= 1000;
            }

            if (num >= 100) {
                result += ones[Math.floor(num / 100)] + ' Hundred ';
                num %= 100;
            }

            if (num >= 20) {
                result += tens[Math.floor(num / 10)] + ' ';
                num %= 10;
            } else if (num >= 10) {
                result += teens[num - 10] + ' ';
                return result.trim();
            }

            if (num > 0) {
                result += ones[num] + ' ';
            }

            return result.trim();
        }

        function generatePreview() {
            // Get form data
            const includeGST = document.getElementById('includeGST').checked;
            const invoiceNo = document.getElementById('invoiceNo').value || '2025-26/001';
            const invoiceDate = document.getElementById('invoiceDate').value || new Date().toISOString().split('T')[0];
            const companyName = document.getElementById('companyName').value || 'Your Company Name';
            const companyAddress = document.getElementById('companyAddress').value || 'Your Company Address';
            const gstin = document.getElementById('gstin').value || 'GSTIN Number';
            const pan = document.getElementById('pan').value || 'PAN Number';

            const clientName = document.getElementById('clientName').value || 'Client Name';
            const clientAddress = document.getElementById('clientAddress').value || 'Client Address';
            const clientGstin = document.getElementById('clientGstin').value || 'Client GSTIN';
            const clientPan = document.getElementById('clientPan').value || 'Client PAN';
            const clientEmail = document.getElementById('clientEmail').value || '<EMAIL>';
            const clientPhone = document.getElementById('clientPhone').value || 'Phone Number';

            const accountName = document.getElementById('accountName').value || 'Account Name';
            const accountNumber = document.getElementById('accountNumber').value || 'Account Number';
            const ifsc = document.getElementById('ifsc').value || 'IFSC Code';
            const bankName = document.getElementById('bankName').value || 'Bank Name';
            const swiftCode = document.getElementById('swiftCode').value || 'SWIFT Code';

            // Process items
            const itemRows = document.querySelectorAll('.item-row');
            let itemsHTML = '';
            let grandTotal = 0;
            let totalCGST = 0;
            let totalSGST = 0;

            itemRows.forEach((row, index) => {
                const description = row.querySelector('.item-description').value || 'Service Description';
                const gstRate = includeGST ? (parseFloat(row.querySelector('.item-gst-rate').value) || 18) : 0;
                const quantity = parseInt(row.querySelector('.item-quantity').value) || 1;
                const rate = parseFloat(row.querySelector('.item-rate').value) || 1000;

                const total = quantity * rate;
                const cgst = includeGST ? (total * gstRate) / 200 : 0; // CGST is half of GST
                const sgst = includeGST ? (total * gstRate) / 200 : 0; // SGST is half of GST
                const itemTotal = total + cgst + sgst;

                grandTotal += itemTotal;
                totalCGST += cgst;
                totalSGST += sgst;

                itemsHTML += `
                    <tr>
                        <td>${index + 1}.</td>
                        <td style="text-align: left;">${description}</td>
                        ${includeGST ? `<td>${gstRate}%</td>` : ''}
                        <td>${quantity}</td>
                        <td>₹${rate.toFixed(2)}</td>
                        <td>₹${total.toFixed(2)}</td>
                        ${includeGST ? `<td>₹${cgst.toFixed(2)}</td>` : ''}
                        ${includeGST ? `<td>₹${sgst.toFixed(2)}</td>` : ''}
                        <td>₹${itemTotal.toFixed(2)}</td>
                    </tr>
                `;
            });

            const totalInWords = numberToWords(Math.floor(grandTotal)) + ' Rupees Only';
            const formattedDate = new Date(invoiceDate).toLocaleDateString('en-GB', {
                day: 'numeric',
                month: 'long',
                year: 'numeric'
            });

            // Generate preview HTML
            const previewHTML = `
                <div class="invoice-header">
                    <div class="invoice-title">Invoice
                        <p>
                            <div style="margin-bottom: 10px; font-size:12px; color: black"; >
                                <div>Invoice No. : # ${invoiceNo}</div>
                                <div>Invoice Date: ${formattedDate}</div>
                            </div>
                        </p>
                     </div>
                    
                    <div class="company-logo" style="display: flex; align-items: center;">
                        <span style="display: flex; align-items: center; height: 64px;"> <!-- Increased height for larger SVG -->
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="46.9661 96.0534 398.438 398.437" width="64" height="64" style="display: block;"><g transform="matrix(1, 0, 0, 1, -3.8148871605064727, -8.583626049269029)"><g><path style="fill-rule: evenodd; fill: rgb(27, 25, 24); fill-opacity: 1; stroke-width: 0.2; stroke-linecap: butt; stroke-linejoin: miter; stroke: rgb(0, 0, 0); stroke-opacity: 1; stroke-miterlimit: 4;" d="M 250 104.637 C 360.008 104.637 449.219 193.848 449.219 303.856 C 449.219 413.863 360.008 503.***********.074 C 139.996 503.074 50.781 413.863 50.781 303.856 C 50.781 193.848 139.996 104.***********.637 Z M 250 104.637"/><path style="fill-rule: evenodd; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0.2; stroke-linecap: butt; stroke-linejoin: miter; stroke: rgb(0, 0, 0); stroke-opacity: 1; stroke-miterlimit: 4;" d="M 178.301 176.856 L 249.254 216.273 L 319.719 176.856 L 339.922 261.109 L 290.152 238.445 L 341.262 430.633 L 280.262 430.625 L 250.242 234.5 L 220.184 430.613 L 159.09 430.606 L 211.805 236.805 L 158.738 260.941 L 178.301 176.856 Z M 178.301 176.856"/></g><path style="fill-rule: evenodd; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0.2; stroke-linecap: butt; stroke-linejoin: miter; stroke: rgb(0, 0, 0); stroke-opacity: 1; stroke-miterlimit: 4;" d="M 250 130.563 C 345.691 130.563 423.297 208.164 423.297 303.856 C 423.297 399.547 345.691 477.152 250 477.152 C 154.309 477.152 76.703 399.547 76.703 303.856 C 76.703 208.164 154.309 130.563 250 130.563 Z M 250 120.578 C 351.207 120.578 433.277 202.652 433.277 303.856 C 433.277 405.063 351.207 487.***********.133 C 148.797 487.133 66.723 405.063 66.723 303.856 C 66.723 202.652 148.797 120.***********.578 Z M 250 120.578"/></g></svg>
                        </span>
                    </div>
                </div>

                <div class="invoice-info" style="font-size:12px;">
                    <div class="billed-section">
                        <div class="section-header">Billed By</div>
                        <div><strong>${companyName}</strong></div>
                        <div>${companyAddress}</div>
                        ${includeGST ? `<div><strong>GSTIN:</strong> ${gstin}</div>` : ''}
                        <div><strong>PAN:</strong> ${pan}</div>
                    </div>
                    <div class="billed-section">
                        <div class="section-header">Billed To</div>
                        <div><strong>${clientName}</strong></div>
                        <div>${clientAddress}</div>
                        ${includeGST ? `<div><strong>GSTIN:</strong> ${clientGstin}</div>` : ''}
                        <div><strong>PAN:</strong> ${clientPan}</div>
                        <div><strong>Email:</strong> ${clientEmail}</div>
                        <div><strong>Phone:</strong> ${clientPhone}</div>
                    </div>
                </div>
                <table class="invoice-table">
                    <thead>
                        <tr>
                            <th>S.No</th>
                            <th>Item</th>
                            ${includeGST ? '<th>GST Rate</th>' : ''}
                            <th>Qty</th>
                            <th>Rate</th>
                            <th>Grand Total</th>
                            ${includeGST ? '<th>CGST</th>' : ''}
                            ${includeGST ? '<th>SGST</th>' : ''}
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${itemsHTML}
                    </tbody>
                </table>
                <div style="margin: 10px 0; font-size:12px;">
                    <strong>Total (in words):</strong> ${totalInWords}
                </div>
                <div class="total-section" style="font-size:12px;">
                    <div class="bank-details">
                        <div class="section-header">Bank Details</div>
                        <div><strong>Account Name:</strong> ${accountName}</div>
                        <div><strong>Account Number:</strong> ${accountNumber}</div>
                        <div><strong>IFSC:</strong> ${ifsc}</div>
                        <div><strong>SWIFT Code:</strong> ${swiftCode}</div>
                        <div><strong>Bank:</strong> ${bankName}</div>
                    </div>
                    <div class="totals">
                        <table>
                            <tr>
                                <td><strong>Grand Total</strong></td>
                                <td>₹${(grandTotal - totalCGST - totalSGST).toFixed(2)}</td>
                            </tr>
                            ${includeGST ? `
                            <tr>
                                <td><strong>CGST</strong></td>
                                <td>₹${totalCGST.toFixed(2)}</td>
                            </tr>
                            <tr>
                                <td><strong>SGST</strong></td>
                                <td>₹${totalSGST.toFixed(2)}</td>
                            </tr>
                            ` : ''}
                            <tr class="grand-total">
                                <td><strong>Total (INR)</strong></td>
                                <td><strong>₹${grandTotal.toFixed(2)}</strong></td>
                            </tr>
                        </table>
                    </div>
                </div>
                <div class="signature-section">
                    <div class="signature-line"></div>
                    <div><strong>Authorised Signatory</strong></div>
                </div>
            `;

            document.getElementById('invoicePreview').innerHTML = previewHTML;
        }

        async function downloadPDF() {
                const invoiceElement = document.getElementById('preview-panel');
                if (!invoiceElement.innerHTML.includes('invoice-header')) {
                    alert('Please generate preview first!');
                    return;
                }

                try {
                    const canvas = await html2canvas(invoiceElement, {
                        scale: 2, // Increase scale for better quality
                        useCORS: true,
                        backgroundColor: '#ffffff'
                    });

                    const imgData = canvas.toDataURL('image/png');
                    const { jsPDF } = window.jspdf;
                    const pdf = new jsPDF('p', 'mm', 'a4');

                    const pageWidth = pdf.internal.pageSize.getWidth();
                    const pageHeight = pdf.internal.pageSize.getHeight();

                    const imgProps = {
                        width: canvas.width,
                        height: canvas.height
                    };

                    // Convert px to mm: 1px = 0.264583 mm
                    const pxToMm = 0.264583;
                    const imgWidthMM = (imgProps.width * pxToMm);
                    const imgHeightMM = imgProps.height * pxToMm;

                    // If the image is taller than a page, allow multi-page split
                    let position = 0;
                    let heightLeft = imgHeightMM;

                    // Set font size for PDF (main: 12, header: 20, section: 15, table: 13, footnote: 11)
                    pdf.setFontSize(12); // Main content
                    pdf.addImage(imgData, 'PNG', 0, position, pageWidth, (imgHeightMM * pageWidth) / imgWidthMM);
                    heightLeft -= pageHeight;

                    while (heightLeft > 0) {
                        position = position - pageHeight;
                        pdf.addPage();
                        pdf.addImage(imgData, 'PNG', 0, position, pageWidth, (imgHeightMM * pageWidth) / imgWidthMM);
                        heightLeft -= pageHeight;
                    }

                    const invoiceNo = document.getElementById('invoiceNo').value || '2025-26-001';
                    pdf.save(`Invoice-${invoiceNo}.pdf`);
                } catch (error) {
                    console.error('Error generating PDF:', error);
                    alert('Error generating PDF. Please try again.');
                }
            }
            

        // Set default date to today
        document.getElementById('invoiceDate').value = new Date().toISOString().split('T')[0];

        // Add event listener for GST checkbox
        document.getElementById('includeGST').addEventListener('change', toggleGSTFields);

        // Initialize GST field visibility
        toggleGSTFields();
    </script>
</body>

</html>