let express = require("express");
let router = express.Router();
let supabaseInstance = require("../../services/supabaseClient").supabase;
const { getWeekInfo , getDayNumber, getDayNameFromNumber } = require("../../services/dateTimeService");

async function getAllReportRecords(outletId) {
  try {
    // Fetch all RSVP records for the outletId
    const { data: rsvpData, error: rsvpError } = await supabaseInstance
      .from("RSVP")
      .select("rsvpDate, mealTypeId, response")
      .eq("outletId", outletId)
      .eq("response", true)
      .order("rsvpDate", { ascending: true });

    if (rsvpError) throw rsvpError;

    // Fetch all Meals_Served records for the outletId
    const { data: mealData, error: mealError } = await supabaseInstance
      .from("Meals_Served")
      .select("served_date, mealTypeId")
      .eq("outletId", outletId)
      .order("served_date", { ascending: true });

    if (mealError) throw mealError;

    // Group data by date
    const groupedData = {};

    rsvpData.forEach((record) => {
      const date = record.rsvpDate.split("T")[0];
      if (!groupedData[date])
        groupedData[date] = {
          date,
          day: getDayNameFromNumber(getDayNumber(date)),
          rsvpCount: 0,
          mealCount: 0,
        };
      groupedData[date].rsvpCount += 1;
    });

    mealData.forEach((record) => {
      const date = record.served_date.split("T")[0];
      if (!groupedData[date])
        groupedData[date] = {
          date,
          day: getDayNameFromNumber(getDayNumber(date)),
          rsvpCount: 0,
          mealCount: 0,
        };
      groupedData[date].mealCount += 1;
    });

    // Convert grouped data to an array
    const reportData = Object.values(groupedData);

    // Calculate the difference between RSVP count and Meal count
    reportData.forEach((record) => {
      record.difference = record.rsvpCount - record.mealCount;
    });

    return reportData;
  } catch (error) {
    throw new Error(`Error fetching all records: ${error.message}`);
  }
}
async function getAverageMealCount(mealTypeId, outletId) {

  // check if the meal is enabled 
  const {data:mealTiming, error:mealTimingError} = await supabaseInstance
    .from("Meal_Timings")
    .select("enabled")
    .eq("outletId", outletId)
    .eq("mealTypeId", mealTypeId);

  if (mealTimingError) throw mealTimingError;

  if (mealTiming) {
    if (mealTiming[0]?.enabled === false) {
      return -1;
    }
  }

  // get current month start date and end date
  const startDate = new Date(new Date().getFullYear(), new Date().getMonth(), 1)
    .toISOString()
    .split("T")[0];

  // end date is today
  const endDate = new Date().toISOString().split("T")[0];

  const mealCount = await getMealCount(
    mealTypeId,
    outletId,
    startDate,
    endDate
  );

  // get only today's date. (eg : if today is 2024-07-26 then get 26 as day number)
  const dayNumber = new Date().getDate();

  let average = mealCount / dayNumber;

  // round off to integer
  console.log("average", average);
  average = Math.round(average);
  console.log("rounded average", average);

  return average;
}

// async function getRSVPCount(mealTypeId, outletId, rsvpDate) {
//   console.log("rsvpDate", new Date(rsvpDate));

//   const { data: rsvpCount, error: rsvpCountError } = await supabaseInstance
//     .from("RSVP")
//     .select("*")
//     .eq("outletId", outletId)
//     .eq("mealTypeId", mealTypeId)
//     .eq("rsvpDate", new Date(rsvpDate))
//     .order("mealTypeId", { ascending: true });

//   if (rsvpCountError) throw rsvpCountError;

//   if (rsvpCount) {
//     let count = 0;
//     for (let i = 0; i < rsvpCount.length; i++) {
//       console.log('rsvpCount[i].rsvpDate', new Date(rsvpCount[i].rsvpDate));

//       if (new Date(rsvpCount[i].rsvpDate) == new Date(rsvpDate)) {
//         count++;
//       }
//     }
//     return count;
//   }

//   return rsvpCount.length;
// }

// async function getRSVPCount(mealTypeId, outletId) {
//   let count = 0;
//   // get day of the week (1 for Monday, 2 for ..., 7 for Sunday)
//   let dayOfTheWeek = getDayNumber();
//   console.log("dayOfTheWeek", dayOfTheWeek);

//   const { data: rsvpCount, error: rsvpCountError } = await supabaseInstance
//     .from("RSVP")
//     // get dayOfWeek from Weekly_Menu
//     .select("Weekly_Menu( dayOfWeek )")
//     .eq("outletId", outletId)
//     .eq("mealTypeId", mealTypeId)
//     .eq("response", true);

//   if (rsvpCountError) throw rsvpCountError;

//   console.log("rsvpCount", rsvpCount);

//   /*
// [ { Weekly_Menu: { dayOfWeek: 1 } } ]
//    */

//   if (rsvpCount) {
//     // compare dayOfWeek with dayOfTheWeek
//     for (let i = 0; i < rsvpCount.length; i++) {
//       if (rsvpCount[i].Weekly_Menu.dayOfWeek === dayOfTheWeek) {
//         count++;
//       }
//     }
//   }

//   return count;
// }

// with date range (production)
// async function getRSVPCount(mealTypeId, outletId, startDate, endDate) {
//   let count = 0;
//   // get day of the week (1 for Monday, 2 for ..., 7 for Sunday)

//   const { data: rsvpCount, error: rsvpCountError } = await supabaseInstance
//     .from("RSVP")
//     .select("Weekly_Menu( dayOfWeek )" , {count: "exact", head: true})
//     .eq("outletId", outletId)
//     .eq("mealTypeId", mealTypeId)
//     .eq("response", true)
//     .gte("rsvpDate", startDate)
//     .lte("rsvpDate", endDate);

//   if (rsvpCountError) throw rsvpCountError;

//   if (rsvpCount) {
//     count = rsvpCount.length;
//   }

//   return count;
// }

async function getRSVPCount(mealTypeId, outletId, startDate, endDate) {

  // check if the meal is enabled 
  const {data:mealTiming, error:mealTimingError} = await supabaseInstance
    .from("Meal_Timings")
    .select("enabled")
    .eq("outletId", outletId)
    .eq("mealTypeId", mealTypeId);

  if (mealTimingError) throw mealTimingError;

  if (mealTiming) {
    if (mealTiming[0]?.enabled === false) {
      return -1;
    }
  }


  // Convert the startDate and endDate from yyyy-mm-ddT00:00:00.000Z to yyyy-mm-dd
  startDate = startDate.split("T")[0];
  endDate = endDate.split("T")[0];

  // Query the RSVP count with count option
  const { count: rsvpCount, error: rsvpCountError } = await supabaseInstance
    .from("RSVP")
    .select("Weekly_Menu( dayOfWeek )", { count: "exact", head: true })
    .eq("outletId", outletId)
    .eq("mealTypeId", mealTypeId)
    .eq("response", true)
    .gte("rsvpDate", startDate)
    .lte("rsvpDate", endDate);

  if (rsvpCountError) throw rsvpCountError;

  return rsvpCount !== null ? rsvpCount : 0;
}

// async function getMealCount(mealTypeId, outletId, servedDate) {
//   const { data: mealCount, error: mealCountError } = await supabaseInstance
//     .from("Meals_Served")
//     .select("mealTypeId")
//     .eq("outletId", outletId)
//     .eq("served_date", servedDate)
//     .eq("mealTypeId", mealTypeId);

//   if (mealCountError) throw mealCountError;
//   if (mealCount) return mealCount.length;

//   return 0;
// }

// with date range
async function getMealCount(mealTypeId, outletId, startDate, endDate) {

  // check if the meal is enabled 
  const {data:mealTiming, error:mealTimingError} = await supabaseInstance
    .from("Meal_Timings")
    .select("enabled")
    .eq("outletId", outletId)
    .eq("mealTypeId", mealTypeId);

  if (mealTimingError) throw mealTimingError;

  if (mealTiming) {
    if (mealTiming[0]?.enabled === false) {
      return -1;
    }
  }

  // Convert the startDate and endDate from yyyy-mm-ddT00:00:00.000Z to yyyy-mm-dd
  startDate = startDate.split("T")[0];
  endDate = endDate.split("T")[0];

  // Query the meal count with count option
  const { count: mealCount, error: mealCountError } = await supabaseInstance
    .from("Meals_Served")
    .select("mealTypeId", { count: "exact", head: true })
    .gte("served_date", startDate)
    .lte("served_date", endDate)
    .eq("outletId", outletId)
    .eq("mealTypeId", mealTypeId);

  if (mealCountError) throw mealCountError;

  if (mealCount !== null) return mealCount;
}

// working
// async function getMonthVsTotalData(outletId) {
//   // Fetch all mealServed data for the given mealTypeId and outletId
//   let { data: mealServedData, error: mealServedDataError } =
//     await supabaseInstance
//       .from("Meals_Served")
//       .select("served_date")
//       .eq("outletId", outletId);

//   if (mealServedDataError) throw mealServedDataError;

//   // Process the data to group by month and calculate total counts
//   const monthlyData = {};

//   mealServedData.forEach((meal) => {
//     const date = new Date(meal.served_date);
//     const month = date.getUTCMonth() + 1;

//     if (!monthlyData[month]) {
//       monthlyData[month] = 0;
//     }

//     monthlyData[month] += 1;
//   });

//   // Format the data as specified
//   const monthVsTotalData = [];
//   for (let month = 1; month <= 12; month++) {
//     monthVsTotalData.push({
//       /* format :
//       [
//         { count1: 10 },
//         { count2: 20 },
//         ...
//       ]
//       */
//       [`count${month}`]: monthlyData[month] ? monthlyData[month] : 0,
//     });
//   }

//   return monthVsTotalData;
// }

// async function getDayVsTotalData(outletId) {
//   // Fetch all mealServed data for the given outletId
//   // get current month start date and end date
//   const date = new Date();
//   const year = date.getFullYear();
//   const month = date.getMonth();
//   const startDate = new Date(year, month, 1).toISOString().split("T")[0];
//   const endDate = new Date(year, month + 1, 0).toISOString().split("T")[0];

//   let { data: mealServedData, error: mealServedDataError } =
//     await supabaseInstance
//       .from("Meals_Served")
//       .select("served_date")
//       .eq("outletId", outletId)
//       .gte("served_date", startDate)
//       .lte("served_date", endDate);

//   if (mealServedDataError) throw mealServedDataError;
//   console.log('mealServedData', mealServedData.length);

//   // Process the data to group by day and calculate total counts
//   const dailyData = {};

//   mealServedData.forEach((meal) => {
//     const date = new Date(meal.served_date).getUTCDate(); // Extract the day of the month

//     if (!dailyData[date]) {
//       dailyData[date] = 0;
//     }

//     dailyData[date] += 1;
//   });

//   // Format the data as specified
//   const dayVsTotalData = Object.keys(dailyData).map((day) => ({
//     [`count${day}`]: dailyData[day],
//   }));

//   // set the count to 0 for days that do not have any data
//   for (let day = 1; day <= 31; day++) {
//     // 01, 02, 03, ..., 09, 10, 11, ..., 31
//     const dayStr = day < 10 ? `0${day}` : `${day}`;
//     const dayData = dayVsTotalData.find((data) => Object.keys(data)[0].slice(5) === dayStr);

//     if (!dayData) {
//       dayVsTotalData.push({
//         [`count${dayStr}`]: 0,
//       });
//     }
//   }

//   // sort the data by day
//   dayVsTotalData.sort((a, b) => {
//     const keyA = Object.keys(a)[0].slice(5);
//     const keyB = Object.keys(b)[0].slice(5);
//     return keyA - keyB;
//   });

//   return dayVsTotalData;
// }

// testing
async function getDayVsTotalData(outletId) {
  /* 
  [
    { count01: 10 },
    { count02: 20 },
    ...
    { count31: 24 }
  ]
  */

  // get current month start date and end date
  const date = new Date();
  const year = date.getFullYear();
  const month = date.getMonth();
  // start date = 1st day of the month
  let startDate = new Date(year, month, 1).toISOString().slice(0, 10);
  // end date = last day of the month
  let endDate = new Date(year, month + 1, 0).toISOString().slice(0, 10);

  console.log("startDate", startDate);
  console.log("endDate", endDate);

  // Fetch all mealServed data for the given outletId
  const { data: mealServedData, error: mealServedDataError } =
    await supabaseInstance
      .from("Meals_Served")
      .select("served_date")
      .eq("outletId", outletId)
      .gt("served_date", startDate)
      .lte("served_date", endDate);

  if (mealServedDataError) throw mealServedDataError;

  // Process the data to group by day and calculate total counts
  const dailyData = {};

  mealServedData.forEach((meal) => {
    const date = new Date(meal.served_date).getUTCDate(); // Extract the day of the month

    if (!dailyData[date]) {
      dailyData[date] = 0;
    }

    dailyData[date] += 1;
  });

  // Format the data as specified
  const dayVsTotalData = Object.keys(dailyData).map((day) => ({
    [`count${day < 10 ? `0${day}` : day}`]: dailyData[day], // Ensure days 1-9 are prefixed with '0'
  }));

  // set the count to 0 for days that do not have any data
  for (let day = 1; day <= 31; day++) {
    const dayStr = day < 10 ? `0${day}` : `${day}`;
    const dayData = dayVsTotalData.find((data) => Object.keys(data)[0].slice(5) === dayStr);

    if (!dayData) {
      dayVsTotalData.push({
        [`count${dayStr}`]: 0,
      });
    }
  }

  // sort the data by day
  dayVsTotalData.sort((a, b) => {
    const keyA = Object.keys(a)[0].slice(5);
    const keyB = Object.keys(b)[0].slice(5);
    return keyA - keyB;
  });

  return dayVsTotalData;
}

router.get("/", async (req, res) => {
  return res.send({
    success: true,
    message: "Response from mess/dashboard.js",
  });
});

// router.get("/getDashboardData/:outletId", async (req, res) => {
//   const { outletId } = req.params;
//   let data = {};

//   //get date only
//   let rsvpDate = new Date().toISOString().split("T")[0];

//   try {
//     // get count of RSVPs for each meal type
//     data.breakfastRSVP = await getRSVPCount(1, outletId);
//     data.lunchRSVP = await getRSVPCount(2, outletId);
//     data.highTeaRSVP = await getRSVPCount(3, outletId);
//     data.dinnerRSVP = await getRSVPCount(4, outletId);

//     // get count of meals served for each meal type
//     data.breakfastCount = await getMealCount(1, outletId, rsvpDate);
//     data.lunchCount = await getMealCount(2, outletId, rsvpDate);
//     data.highTeaCount = await getMealCount(3, outletId, rsvpDate);
//     data.dinnerCount = await getMealCount(4, outletId, rsvpDate);

//     // get average count of meals served for each meal type for current month
//     data.avBreakfastCount = await getAverageMealCount(1, outletId);
//     data.avLunchCount = await getAverageMealCount(2, outletId);
//     data.avHighTeaCount = await getAverageMealCount(3, outletId);
//     data.avDinnerCount = await getAverageMealCount(4, outletId);

//     return res.status(200).send({
//       success: true,
//       message: "Dashboard Data fetched successfully",
//       data,
//     });
//   } catch (error) {
//     return res.status(500).send({
//       success: false,
//       message: "Error fetching Dashboard Data",
//       error,
//     });
//   }
// });

router.get("/getDashboardData/:outletId", async (req, res) => {
  const { outletId } = req.params;
  let { day, startDate, endDate } = req.query;

  /*
  day = 1 for Today, 2 for Yesterday, 3 for Tomorrow
  startDate and endDate are for custom date range
  convert the startDate and endDate from yyyy-mm-dd to yyyy-mm-ddT00:00:00.000Z 
  */

  if (
    startDate === undefined ||
    endDate === undefined ||
    startDate === "null" ||
    endDate === "null" ||
    startDate === null ||
    endDate === null ||
    endDate === "Invalid Date" ||
    startDate === "Invalid Date" ||
    startDate === "" ||
    endDate === ""
  ) {
    if (day == 1) {
      startDate = new Date().toISOString().split("T")[0];
      endDate = new Date().toISOString().split("T")[0];
    } else if (day == 2) {
      let yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      startDate = yesterday.toISOString().split("T")[0];
      endDate = yesterday.toISOString().split("T")[0];
    } else if (day == 3) {
      let tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      startDate = tomorrow.toISOString().split("T")[0];
      endDate = tomorrow.toISOString().split("T")[0];
    }
  }
  // convert the startDate and endDate from yyyy-mm-dd to yyyy-mm-ddT00:00:00.000Z
  startDate = startDate + "T00:00:00.000Z";
  endDate = endDate + "T00:00:00.000Z";

  let data = {};

  try {
    // get count of RSVPs for each meal type
    data.breakfastRSVP = await getRSVPCount(1, outletId, startDate, endDate);
    data.lunchRSVP = await getRSVPCount(2, outletId, startDate, endDate);
    data.highTeaRSVP = await getRSVPCount(3, outletId, startDate, endDate);
    data.dinnerRSVP = await getRSVPCount(4, outletId, startDate, endDate);

    // get count of meals served for each meal type
    data.breakfastCount = await getMealCount(1, outletId, startDate, endDate);
    data.lunchCount = await getMealCount(2, outletId, startDate, endDate);
    data.highTeaCount = await getMealCount(3, outletId, startDate, endDate);
    data.dinnerCount = await getMealCount(4, outletId, startDate, endDate);

    // // get average count of meals served for each meal type for current month
    data.avBreakfastCount = await getAverageMealCount(1, outletId);
    data.avLunchCount = await getAverageMealCount(2, outletId);
    data.avHighTeaCount = await getAverageMealCount(3, outletId);
    data.avDinnerCount = await getAverageMealCount(4, outletId);

    return res.status(200).send({
      success: true,
      message: "Dashboard Data fetched successfully",
      data,
    });
  } catch (error) {
    return res.status(500).send({
      success: false,
      message: "Error fetching Dashboard Data",
      error,
    });
  }
});

// graph data for month(x) vs average meals served
router.get("/getGraphData/:outletId", async (req, res) => {
  const { outletId } = req.params;

  try {
    // get data for month vs average meals served graph
    // let data = await getMonthVsTotalData(outletId);
    let data = await getDayVsTotalData(outletId);

    // calculate how many days are there in the current month
    const date = new Date();
    const year = date.getFullYear();
    const month = date.getMonth();
    const daysInMonth = new Date(year, month + 1, 0).getDate();

    return res.status(200).send({
      success: true,
      message: "Graph Data fetched successfully",
      data,
      daysInMonth,
    });
  } catch (error) {
    return res.status(500).send({
      success: false,
      message: "Error fetching Graph Data",
      error,
    });
  }
});

// router.get("/getReportData/:outletId", async (req, res) => {
//   const { outletId } = req.params;
//   const { startDate, endDate } = req.query;

//   if (!startDate || !endDate) {
//     return res.status(400).json({
//       success: false,
//       message: "startDate and endDate query parameters are required",
//     });
//   }

//   try {
//     let reportData = [];
//     let currentDate = new Date(startDate);
//     const end = new Date(endDate);
//     const datePromises = [];

//     while (currentDate <= end) {
//       const date = currentDate.toISOString().split("T")[0];
//       datePromises.push(date);

//       // Move to the next day
//       currentDate.setDate(currentDate.getDate() + 1);
//     }

//     const dataPromises = datePromises.map(async (date) => {
//       const dayNumber = getDayNumber(date);

//       // Run both async calls in parallel
//       const [rsvpCount, mealCount] = await Promise.all([
//         getRSVPCount(1, outletId, date, date),
//         getMealCount(1, outletId, date, date),
//       ]);

//       return {
//         date,
//         dayNumber,
//         rsvpCount,
//         mealCount,
//       };
//     });

//     // Resolve all promises
//     reportData = await Promise.all(dataPromises);

//     return res.status(200).json({
//       success: true,
//       message: "Report Data fetched successfully",
//       data: reportData,
//     });

//   } catch (error) {
//     return res.status(500).send({
//       success: false,
//       message: "Error fetching Report Data",
//       error: error.message,
//     });
//   }
// });

router.get("/getReportData/:outletId", async (req, res) => {
  const { outletId } = req.params;
  const { startDate, endDate } = req.query;

  try {
    let reportData = [];

    if (!startDate || !endDate) {
      // If no startDate and endDate, fetch all records for the outlet
      reportData = await getAllReportRecords(outletId);
    } else {
      let currentDate = new Date(startDate);
      const end = new Date(endDate);
      const datePromises = [];

      while (currentDate <= end) {
        const date = currentDate.toISOString().split("T")[0];
        datePromises.push(date);

        // Move to the next day
        currentDate.setDate(currentDate.getDate() + 1);
      }

      const dataPromises = datePromises.map(async (date) => {
        const dayNumber = getDayNumber(date);

        // Run both async calls in parallel
        const [
          rsvpCount1,
          rsvpCount2,
          rsvpCount3,
          rsvpCount4,
          mealCount1,
          mealCount2,
          mealCount3,
          mealCount4,
        ] = await Promise.all([
          getRSVPCount(1, outletId, date, date),
          getRSVPCount(2, outletId, date, date),
          getRSVPCount(3, outletId, date, date),
          getRSVPCount(4, outletId, date, date),
          getMealCount(1, outletId, date, date),
          getMealCount(2, outletId, date, date),
          getMealCount(3, outletId, date, date),
          getMealCount(4, outletId, date, date),
        ]);

        const rsvpCount = rsvpCount1 + rsvpCount2 + rsvpCount3 + rsvpCount4;
        const mealCount = mealCount1 + mealCount2 + mealCount3 + mealCount4;

        const difference = rsvpCount - mealCount;

        return {
          date,
          day : getDayNameFromNumber(dayNumber),
          rsvpCount,
          mealCount,
          difference,
        };
      });

      // Resolve all promises
      reportData = await Promise.all(dataPromises);
    }

    return res.status(200).json({
      success: true,
      message: "Report Data fetched successfully",
      data: reportData,
    });
  } catch (error) {
    return res.status(500).send({
      success: false,
      message: "Error fetching Report Data",
      error: error.message,
    });
  }
});

router.get("/getRsvp/:outletId", async (req, res) => {
  const { outletId } = req.params;
  const { startDate, endDate } = req.query;

  try {
    let query = supabaseInstance
      .from("RSVP")
      .select(
        "rsvpId, Customer(customerName, email) , mealTypeId, response, menuId, rsvpDate"
      )
      .eq("outletId", outletId);

    if (startDate && endDate) {
      query = query.gte("rsvpDate", startDate).lte("rsvpDate", endDate);
    }

    const { data: rsvpData, error: rsvpDataError } = await query;

    if (rsvpDataError) throw rsvpDataError;

    return res.status(200).send({
      success: true,
      message: "RSVP Data fetched successfully",
      data: rsvpData,
    });
  } catch (error) {
    return res.status(500).send({
      success: false,
      message: "Error fetching RSVP Data",
      error,
    });
  }
});

router.get("/getUsersWithNoRSVP/:outletId", async (req, res) => {
  const { outletId } = req.params;
  const { mealTypeId } = req.query;
  const date = new Date().toISOString().split("T")[0] + " 00:00:00+00";

  if (!mealTypeId) {
    return res.status(400).json({
      success: false,
      message: "mealTypeId query parameter is required",
    });
  }

  try {
    // Get the list of whitelist users and RSVP users in parallel
    const [whitelistUsersResponse, rsvpUsersResponse] = await Promise.all([
      supabaseInstance
        .from("Mess_Whitelist")
        .select("customerAuthUID, Customer(customerName, email, mobile)")
        .eq("outletId", outletId),
      supabaseInstance
        .from("RSVP")
        .select("customerAuthUID")
        .eq("outletId", outletId)
        .eq("mealTypeId", mealTypeId)
        .eq("rsvpDate", date)
        .eq("response", true),
    ]);

    console.log("rsvpUsersResponse", rsvpUsersResponse);

    if (whitelistUsersResponse.error) throw whitelistUsersResponse.error;
    if (rsvpUsersResponse.error) throw rsvpUsersResponse.error;

    const whitelistUsers = whitelistUsersResponse.data;
    const rsvpUsers = rsvpUsersResponse.data;

    // Create a set of RSVP user IDs for quick lookup
    const rsvpUserIds = new Set(rsvpUsers.map((user) => user.customerAuthUID));

    // Filter out users who have already responded yes from the whitelist users
    const usersWithNoRSVP = whitelistUsers.filter(
      (user) => !rsvpUserIds.has(user.customerAuthUID)
    );

    // remove the users whose meal is already served
    const servedUsers = await supabaseInstance
      .from("Meals_Served")
      .select("customerAuthUID")
      .eq("outletId", outletId)
      .eq("mealTypeId", mealTypeId)
      .eq("served_date", date);

    if (servedUsers.error) throw servedUsers.error;

    const servedUserIds = new Set(
      servedUsers.data.map((user) => user.customerAuthUID)
    );

    const usersWithNoRSVPAndServed = usersWithNoRSVP.filter(
      (user) => !servedUserIds.has(user.customerAuthUID)
    );

    return res.status(200).json({
      success: true,
      message: "Users with no RSVP fetched successfully",
      data: usersWithNoRSVPAndServed,
    });
  } catch (error) {
    console.error("Error fetching Users with no RSVP:", error); // Log the full error for debugging purposes
    return res.status(500).send({
      success: false,
      message: "Error fetching Users with no RSVP",
      error: error.message,
    });
  }
});

router.get("/getUsersWithRSVP/:outletId", async (req, res) => {
  const { outletId } = req.params;
  const { mealTypeId } = req.query;
  const date = new Date().toISOString().split("T")[0] + " 00:00:00+00";

  if (!mealTypeId) {
    return res.status(400).json({
      success: false,
      message: "mealTypeId query parameter is required",
    });
  }

  try {
    // First, get the list of RSVP users
    const rsvpUsersResponse = await supabaseInstance
      .from("RSVP")
      .select("customerAuthUID, rsvpId")
      .eq("outletId", outletId)
      .eq("mealTypeId", mealTypeId)
      .eq("rsvpDate", date)
      .eq("response", true);

    if (rsvpUsersResponse.error) throw rsvpUsersResponse.error;

    const rsvpUsers = rsvpUsersResponse.data;

    // Create a set of RSVP user IDs for quick lookup
    const rsvpUserIds = new Set(rsvpUsers.map((user) => user.customerAuthUID));

    // Fetch customers data only for the RSVP users
    const customersResponse = await supabaseInstance
      .from("Customer")
      .select("customerAuthUID, email, mobile, customerName")
      .or("customerAuthUID.in.(" + Array.from(rsvpUserIds).join(",") + ")");

    if (customersResponse.error) throw customersResponse.error;

    const customers = customersResponse.data;

    // Filter out users who have already responded yes from the customers
    const usersWithRSVP = customers.filter((user) =>
      rsvpUserIds.has(user.customerAuthUID)
    );

    // Add the rsvpId to the usersWithRSVP
    usersWithRSVP.forEach((user) => {
      const rsvpUser = rsvpUsers.find(
        (rsvpUser) => rsvpUser.customerAuthUID === user.customerAuthUID
      );
      user.rsvpId = rsvpUser.rsvpId;
      user.Customer = {
        email: user.email,
        mobile: user.mobile,
        customerName: user.customerName,
      };
      delete user.email;
      delete user.mobile;
      delete user.customerName;
    });

    return res.status(200).json({
      success: true,
      message: "Users with RSVP fetched successfully",
      data: usersWithRSVP,
    });
  } catch (error) {
    console.error("Error fetching Users with RSVP:", error); // Log the full error for debugging purposes
    return res.status(500).send({
      success: false,
      message: "Error fetching Users with RSVP",
      error: error.message,
    });
  }
});

async function getMenuId(mealTypeId, outletId) {
  const { weekNumber, dayOfWeek } = getWeekInfo();

  const { data: menuData, error: menuDataError } = await supabaseInstance
    .from("Weekly_Menu")
    .select("menuId")
    .eq("mealTypeId", mealTypeId)
    .eq("outletId", outletId)
    .eq("weekNumber", weekNumber)
    .eq("dayOfWeek", dayOfWeek)
    .single();

  if (menuDataError) throw menuDataError;
  return menuData.menuId;
}

router.post("/addManualRSVP", async (req, res) => {
  const { customerAuthUID, mealTypeId, outletId } = req.body;
  if (!customerAuthUID || !mealTypeId || !outletId) {
    return res
      .status(400)
      .send({ success: false, message: "Missing required fields" });
  }
  const rsvpDate = new Date().toISOString().split("T")[0] + " 00:00:00+00";

  try {
    const menuId = await getMenuId(mealTypeId, outletId);

    const { data: rsvpData, error: rsvpError } = await supabaseInstance
      .from("RSVP")
      .insert([
        {
          customerAuthUID,
          mealTypeId,
          outletId,
          rsvpDate,
          menuId,
          response: true,
        },
      ])
      .single();

    if (rsvpError) throw rsvpError;

    return res.status(200).json({
      success: true,
      message: "RSVP added successfully",
      data: rsvpData,
    });
  } catch (error) {
    console.error("Error:", error);
    return res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/addManualPlates", async (req, res) => {
  const { customerAuthUID, mealTypeId, outletId } = req.body;
  if (!customerAuthUID || !mealTypeId || !outletId) {
    return res
      .status(400)
      .send({ success: false, message: "Missing required fields" });
  }
  const menuId = await getMenuId(mealTypeId, outletId);
  const served_date = new Date().toISOString().split("T")[0];
  const served_time = new Date().toISOString();

  try {
    const [existingRSVPResponse, weeklyMenuResponse] = await Promise.all([
      supabaseInstance
        .from("Meals_Served")
        .select("*, Customer(customerName, email)")
        .eq("customerAuthUID", customerAuthUID)
        .eq("served_date", served_date)
        .eq("menuId", menuId)
        .eq("outletId", outletId)
        .single(),
      supabaseInstance
        .from("Weekly_Menu")
        .select("*")
        .eq("menuId", menuId)
        .single(),
    ]);

    const { data: existingRSVP, error: existingRSVPError } =
      existingRSVPResponse;
    const { data: weeklyMenuData, error: weeklyMenuError } = weeklyMenuResponse;

    if (existingRSVPError && existingRSVPError.code !== "PGRST116") {
      throw existingRSVPError;
    }

    if (existingRSVP) {
      const { error: updateError } = await supabaseInstance
        .from("Meals_Served")
        .update({ menuId })
        .eq("mealServedId", existingRSVP.mealServedId)
        .single();

      if (updateError) throw updateError;

      return res.send({
        success: false,
        message: "Meal already served",
        data: existingRSVP,
      });
    }

    if (weeklyMenuError) throw weeklyMenuError;

    const mealTypeId = weeklyMenuData.mealTypeId;

    const { data, error: rsvpError } = await supabaseInstance
      .from("Meals_Served")
      .insert([
        {
          customerAuthUID,
          served_date,
          served_time,
          mealTypeId,
          menuId,
          outletId,
        },
      ])
      .select("*, Customer(customerName, email, photo)")
      .single();

    if (rsvpError) throw rsvpError;

    return res.send({
      success: true,
      message: "Meal Served Successfully",
      data,
    });
  } catch (error) {
    console.error("Error:", error);
    return res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/removeRSVP/:rsvpId", async (req, res) => {
  const { rsvpId } = req.params;

  try {
    const { error } = await supabaseInstance
      .from("RSVP")
      .delete()
      .eq("rsvpId", rsvpId);

    if (error) throw error;

    return res.status(200).json({
      success: true,
      message: "RSVP removed successfully",
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Error removing RSVP",
      error: error.message,
    });
  }
});

/**
 * Remove the plate served to the customer
 * @param {string} mealServedId
 * @returns {object} response
 * @returns {boolean} response.success
 * @returns {string} response.message
 */
router.post("/removeManualPlates/:mealServedId", async (req, res) => {
  const { mealServedId } = req.params;

  try {
    const { error } = await supabaseInstance
      .from("Meals_Served")
      .delete()
      .eq("mealServedId", mealServedId);

    if (error) throw error;

    return res.status(200).json({
      success: true,
      message: "Plate removed successfully",
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Error removing Plate",
      error: error.message,
    });
  }
});


router.get('/getRSVPCompulsion/:outletId', async (req, res) => {
  const { outletId } = req.params;

  if(!outletId) {
    return res.status(400).json({
      success: false,
      message: "outletId is required"
    });
  }

  try {
    const { data: outletData, error: outletDataError } = await supabaseInstance
      .from("Outlet")
      .select("rsvpCompulsion")
      .eq("outletId", outletId)
      .single();

    if(outletDataError) throw outletDataError;

    return res.status(200).json({
      success: true,
      message: "Compulsory RSVP fetched successfully",
      data: outletData
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Error fetching Compulsory RSVP",
      error: error.message
    });
  }
});

router.post("/toggleRSVPCompulsion/:outletId", async (req, res) => {
  const { outletId } = req.params;

  if(!outletId) {
    return res.status(400).json({
      success: false,
      message: "outletId is required"
    });
  }

  try {
    const { data: outletData, error: outletDataError } = await supabaseInstance
      .from("Outlet")
      .select("rsvpCompulsion")
      .eq("outletId", outletId)
      .single();

    if(outletDataError) throw outletDataError;

    const rsvpCompulsion = !outletData.rsvpCompulsion;

    const { error: updateError } = await supabaseInstance
      .from("Outlet")
      .update({ rsvpCompulsion })
      .eq("outletId", outletId);

    if(updateError) throw updateError;

    return res.status(200).json({
      success: true,
      message: "Compulsory RSVP toggled successfully",
      data: { rsvpCompulsion }
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Error toggling Compulsory RSVP",
      error: error.message
    });
  }
});
router.get("/getReviewsDashboard/:outletId", async (req, res) => {
  const { outletId } = req.params;

  if(!outletId) {
    return res.status(400).json({ success: false, message: "outletId is required" });
  }

  try {
    // Only select needed fields instead of *
    const { data: reviewsData, error: reviewsDataError } = await supabaseInstance
      .from("Mess_Meal_Review")
      .select(`
        *,
        Customer (
          customerName,
          mobile,
          photo
        ),
        Weekly_Menu (
          mealTypeId
        )
      `)
      .eq("outletId", outletId)
      .order("reviewDate", { ascending: false });

    if(reviewsDataError) throw reviewsDataError;

    // Initialize ratings array with default values
    const ratings = [
      { stars: 5, count: 0, color: '#4CAF50' },
      { stars: 4, count: 0, color: '#FFC107' },
      { stars: 3, count: 0, color: '#9C27B0' },
      { stars: 2, count: 0, color: '#F44336' },
      { stars: 1, count: 0, color: '#00BCD4' }
    ];

    let totalRating = 0;
    const totalReviews = reviewsData.length;

    reviewsData.forEach(review => {
      ratings[5 - review.rating].count++;
      totalRating += review.rating;
    });

    // send first 5 reviews in feedbacks with the given format
    const feedbacks = reviewsData.slice(0, 5).map(review => {
      return {
        reviewId: review.reviewId,
        name: review.Customer.customerName,
        phone: review.Customer.mobile,
        date: review.reviewDate,
        rating: review.rating,
        mealType: review.Weekly_Menu.mealTypeId,
        comment: review.review,
        photo: review.Customer.photo,
        reply: review.reply
      };
    });

    const averageRating = totalReviews ? totalRating / totalReviews : 0;

    return res.status(200).json({
      success: true,
      message: "Reviews fetched successfully",
      data: { ratings, feedbacks, averageRating, totalReviews }
    });

  } catch (error) {
    return res.status(500).json({ success: false, message: "Error fetching reviews", error: error.message });
  }
});


router.get("/getReviewsData/:outletId", async (req, res) => {
  const { outletId } = req.params;
  const { fromDate, toDate } = req.query;

  if(!outletId) {
    return res.status(400).json({ success: false, message: "outletId is required" });
  }

  try {
    // Only select needed fields instead of *
    let query = supabaseInstance
      .from("Mess_Meal_Review")
      .select(`
        *,
        Customer (
          customerAuthUID,
          customerName
        ),
        Weekly_Menu (
          mealTypeId
        )
      `)
      .eq("outletId", outletId)
      .order("reviewDate", { ascending: false });

    if (fromDate && toDate) {
      // Add date range filter in single call if both dates present
      query = query.filter('reviewDate', 'gte', fromDate)
                  .filter('reviewDate', 'lte', toDate);
    } else if (fromDate) {
      query = query.gte("reviewDate", fromDate);
    } else if (toDate) {
      query = query.lte("reviewDate", toDate);
    }

    const { data: reviewsData, error: reviewsDataError } = await query;

    if(reviewsDataError) throw reviewsDataError;

    // get the average rating of the user from the reviews array
    function getUserAvgRating(reviews, customerAuthUID) {
      const userReviews = reviews.filter(review => review.Customer.customerAuthUID === customerAuthUID);
      const userTotalRating = userReviews.reduce((sum, review) => sum + review.rating, 0);
      return userTotalRating / userReviews.length;
    }
    
    const feedbacks = reviewsData.map(review => {
      return {
        date: review.reviewDate,
        name: review.Customer.customerName,
        mealType: review.Weekly_Menu.mealTypeId,
        rating: review.rating,
        feedback: review.review,
        userAvgRating: getUserAvgRating(reviewsData, review.Customer.customerAuthUID),
        reply: review.reply
      }
    });

    const averageRating = reviewsData.reduce((sum, review) => sum + review.rating, 0) / reviewsData.length;
    const totalReviews = reviewsData.length;

    return res.status(200).json({
      success: true,
      message: "Reviews fetched successfully",
      data: { feedbacks, averageRating, totalReviews }
    });
  } catch (error) {
    return res.status(500).json({ success: false, message: "Error fetching reviews", error: error.message });
  }
});


router.post("/giveReplyForReview", async (req, res) => {
  const { reviewId, reply } = req.body;
  
  if(!reviewId || !reply) {
    return res.status(400).json({ success: false, message: "Missing required fields" });
  }
  
  try {
    const { data: reviewData, error } = await supabaseInstance
    .from("Mess_Meal_Review")
    .update({ reply })
    .eq("reviewId", reviewId)
    .select("customerAuthUID, outletId, Outlet(outletName)")
    .single();
    
    if(error) throw error;
    
    // notify the customer about the reply
    const sendNotification = require("../firebase").sendNotification;
    const { data: fcmData, error: fcmDataError } = await supabaseInstance
      .from("FCM_Tokens")
      .select("fcmToken")
      .eq("customerAuthUID", reviewData.customerAuthUID);

    if (fcmDataError) throw fcmDataError;

    fcmData.forEach(async (fcm) => {
      await sendNotification(
        fcm.fcmToken,
        `Reply for your review on ${reviewData.Outlet.outletName}`,
        reply,
        reviewData.Outlet.outletName,
        reviewData.outletId,
        fcm.appName || "com.mealpe"
      );
    });

    return res
      .status(200)
      .json({ success: true, message: "Reply given successfully" });
  } catch (error) {
    console.error("Error:", error);
    return res.status(500).json({ success: false, error });
  }
});

router.get("/servedUsers/:outletId", async (req, res) => {
  const { outletId } = req.params;
  const { mealTypeId } = req.query;
  const date = new Date().toISOString().split("T")[0] + " 00:00:00+00";

  if (!mealTypeId || !date) {
    return res.status(400).json({
      success: false,
      message: "mealTypeId and date query parameters are required",
    });
  }

  try {
    const { data: servedUsers, error: servedUsersError } = await supabaseInstance
      .from("Meals_Served")
      .select("mealServedId, customerAuthUID, Customer(customerName, email, mobile)")
      .eq("outletId", outletId)
      .eq("mealTypeId", mealTypeId)
      .eq("served_date", date);

    if (servedUsersError) throw servedUsersError;

    return res.status(200).json({
      success: true,
      message: "Served Users fetched successfully",
      data: servedUsers,
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Error fetching Served Users",
      error: error.message,
    });
  }
});

/**
 * get the users who have rsvped for the meal but not served
 * @param {string} outletId
 * @param {string} mealTypeId
 * @returns {object} response
 * 
 * -get the rsvped users for the meal
 * -check the Meals_Served table for the users who have already been served
 * -return the users who have rsvped but not served
 */
router.get("/getRSVPUsersNotServed/:outletId", async (req, res) => {
  const { outletId } = req.params;
  const { mealTypeId } = req.query;
  const date = new Date().toISOString().split("T")[0] + " 00:00:00+00";

  if (!mealTypeId) {
    return res.status(400).json({
      success: false,
      message: "mealTypeId query parameter is required",
    });
  }

  try {
    // Get the list of RSVP users
    const rsvpUsersResponse = await supabaseInstance
      .from("RSVP")
      .select("customerAuthUID")
      .eq("outletId", outletId)
      .eq("mealTypeId", mealTypeId)
      .eq("rsvpDate", date)
      .eq("response", true);

    if (rsvpUsersResponse.error) throw rsvpUsersResponse.error;

    const rsvpUsers = rsvpUsersResponse.data;

    // Create a set of RSVP user IDs for quick lookup
    const rsvpUserIds = new Set(rsvpUsers.map((user) => user.customerAuthUID));

    // Fetch customers data only for the RSVP users
    const customersResponse = await supabaseInstance
      .from("Customer")
      .select("customerAuthUID, email, mobile, customerName")
      .or("customerAuthUID.in.(" + Array.from(rsvpUserIds).join(",") + ")");
    if (customersResponse.error) throw customersResponse.error;

    const customers = customersResponse.data;

    // Filter out users who have already responded yes from the customers
    const usersWithRSVP = customers.filter((user) =>
      rsvpUserIds.has(user.customerAuthUID)
    );

    // Add the rsvpId to the usersWithRSVP
    usersWithRSVP.forEach((user) => {
      const rsvpUser = rsvpUsers.find(
        (rsvpUser) => rsvpUser.customerAuthUID === user.customerAuthUID
      );
      user.rsvpId = rsvpUser.rsvpId;
      user.Customer = {
        email: user.email,
        mobile: user.mobile,
        customerName: user.customerName,
      };
      delete user.email;
      delete user.mobile;
      delete user.customerName;
    });

    // Fetch served users to filter out
    const servedUsersResponse = await supabaseInstance
      .from("Meals_Served")
      .select("customerAuthUID")
      .eq("outletId", outletId)
      .eq("mealTypeId", mealTypeId)
      .eq("served_date", date);
    if (servedUsersResponse.error) throw servedUsersResponse.error;

    const servedUsers = servedUsersResponse.data;
    const servedUserIds = new Set(servedUsers.map((user) => user.customerAuthUID));

    // Filter out users who have already been served
    const notServedUsers = usersWithRSVP.filter((user) =>
      !servedUserIds.has(user.customerAuthUID)
    );

    return res.status(200).json({
      success: true,
      message: "Not served RSVP users fetched successfully",
      data: notServedUsers,
      metadata: {
        count: notServedUsers.length,
      },
    });

  } catch (error) {
    console.error("Error fetching Users with RSVP:", error); // Log the full error for debugging purposes
    return res.status(500).send({
      success: false,
      message: "Error fetching Users with RSVP",
      error: error.message,
    });
  }
});


router.get("/getUserwiseReport/:outletId", async (req, res) => {
  const { outletId } = req.params;
  let { startDateTime, endDateTime } = req.query;

  if (!outletId) {
    return res
      .status(400)
      .json({ success: false, message: "Missing outletId" });
  }

  try {
    
    let rsvpQuery = supabaseInstance
      .from("RSVP")
      .select("customerAuthUID, mealTypeId, Customer(email, mobile, customerName)")
      .eq("outletId", outletId);

    let actualQuery = supabaseInstance
      .from("Meals_Served")
      .select("customerAuthUID, mealTypeId")
      .eq("outletId", outletId);

    if (startDateTime && endDateTime) {
      //convert to timestampz
      startDateTime = new Date(startDateTime).toISOString();

      // make enddate time to end of the day
      endDateTime = new Date(endDateTime);
      endDateTime.setHours(23, 59, 59, 999);
      endDateTime = endDateTime.toISOString();

      rsvpQuery = rsvpQuery
        .gte("rsvpDate", startDateTime)
        .lte("rsvpDate", endDateTime);

      actualQuery = actualQuery
        .gte("served_date", startDateTime)
        .lte("served_date", endDateTime);
    }

    let { data: rsvpData, error: rsvpError } = await rsvpQuery;
    if (rsvpError) throw rsvpError;

    let { data: actualData, error: actualError } = await actualQuery;
    if (actualError) throw actualError;

    // get unique customers from RSVP
    let whiteListData = rsvpData.reduce((acc, curr) => {
      if (!acc.find((item) => item.customerAuthUID === curr.customerAuthUID)) {
        acc.push(curr);
      }
      return acc;
    }, []);

    whiteListData = whiteListData.map((data) => {
      let rsvpCount = rsvpData?.filter(
        (rsvp) => rsvp.customerAuthUID === data.customerAuthUID
      );
      let actualCount = actualData?.filter(
        (actual) => actual?.customerAuthUID === data?.customerAuthUID
      );

      let rsvpCountObj = {
        1: 0,
        2: 0,
        3: 0,
        4: 0,
      };
      let actualCountObj = {
        1: 0,
        2: 0,
        3: 0,
        4: 0,
      };

      rsvpCount.forEach((rsvp) => {
        rsvpCountObj[rsvp.mealTypeId] = rsvpCountObj[rsvp.mealTypeId]
          ? rsvpCountObj[rsvp.mealTypeId] + 1
          : 1;
      });

      actualCount.forEach((actual) => {
        actualCountObj[actual.mealTypeId] = actualCountObj[actual.mealTypeId]
          ? actualCountObj[actual.mealTypeId] + 1
          : 1;
      });

      let differenceObj = {};

      Object.keys(rsvpCountObj).forEach((mealType) => {
        differenceObj[mealType] =
          rsvpCountObj[mealType] - actualCountObj[mealType] ||
          rsvpCountObj[mealType];
      });

      return {
        whitelistingId: data.whitelistingId,
        created_at: data.created_at,
        outletId: data.outletId,
        customerAuthUID: data.customerAuthUID,
        Customer: {
          email: data.Customer.email,
          mobile: data.Customer.mobile,
          customerName: data.Customer.customerName,
          rsvpCount: rsvpCountObj,
          actualCount: actualCountObj,
          difference: differenceObj,
        },
      };
    });
    return res.status(200).json({ success: true, data: whiteListData });
  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});


// get the serveId and serve dates for which there is no corresponding record for rsvp for a outlet in given dates
router.get("/dev/getServedUsersNotRSVP", async (req, res) => {
  const outletId = "ab26af06-087f-4ee3-b2f8-78f5d4233ba8";
  let startDateTime = "2025-05-11 00:00:00+00:00", endDateTime = "2025-05-11 23:59:59+00:00";

  if (!outletId) {
    return res
      .status(400)
      .json({ success: false, message: "Missing outletId" });
  }

  try {
    // Parallel fetch of both datasets to improve response time
    const [servedResponse, rsvpResponse] = await Promise.all([
      supabaseInstance
        .from("Meals_Served")
        .select("mealServedId, customerAuthUID, Customer(mobile,customerName), mealTypeId, served_date")
        .eq("outletId", outletId)
        .gte("served_date", startDateTime)
        .lte("served_date", endDateTime),

      supabaseInstance
        .from("RSVP")
        .select("customerAuthUID, mealTypeId, rsvpDate")
        .eq("outletId", outletId)
        .gte("rsvpDate", startDateTime)
        .lte("rsvpDate", endDateTime)
    ]);

    if (servedResponse.error) throw servedResponse.error;
    if (rsvpResponse.error) throw rsvpResponse.error;

    const servedData = servedResponse.data;
    const rsvpData = rsvpResponse.data;

    // Create a lookup map for RSVPs - this is much faster than using .some() in a loop
    const rsvpLookup = {};

    rsvpData.forEach(rsvp => {
      // Extract the date part (assuming ISO format with T separator)
      const rsvpDateOnly = rsvp.rsvpDate.split('T')[0];

      // Create a unique key combining customer, date and meal type
      const key = `${rsvp.customerAuthUID}|${rsvpDateOnly}|${rsvp.mealTypeId}`;

      // Add to lookup
      rsvpLookup[key] = true;
    });

    // Filter and format in a single pass for better performance
    const formattedData = servedData
      .filter(served => {
        // Create the same key format to check against our lookup map
        const key = `${served.customerAuthUID}|${served.served_date}|${served.mealTypeId}`;

        // If the key exists in our lookup, there's a matching RSVP
        return !rsvpLookup[key];
      })
      .map(served => ({
        mealServedId: served.mealServedId,
        customerAuthUID: served.customerAuthUID,
        Customer: {
          mobile: served.Customer.mobile,
          customerName: served.Customer.customerName
        },
        mealTypeId: served.mealTypeId,
        served_date: served.served_date,
        hasRSVP: false
      }));

    return res.status(200).json({
      success: true,
      data: formattedData,
      count: formattedData.length
    });

  } catch (error) {
    return res.status(500).json({ success: false, error: error.message });
  }
});
module.exports = router;
module.exports.getMenuId = getMenuId;
