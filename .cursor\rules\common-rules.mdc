---
description: 
globs: 
alwaysApply: true
---
# Cursor Coding Rules for mealpe-backend

## General Principles

- Use modern ES6+ JavaScript features (e.g., `const`, `let`, arrow functions, destructuring).
- Write clean, modular, and maintainable code with clear separation of concerns.
- Use descriptive, camelCase variable and function names.
- Always include error handling and input validation.
- Add concise but comprehensive comments for complex logic and business rules.
- Follow SOLID principles and design patterns where applicable.
- Optimize for performance and scalability.
- Ensure cross-browser compatibility and accessibility for any frontend code.
- Implement proper security measures and data validation for all endpoints and services.

## Project Structure

- Place all route handlers in the `routes/` directory, organized by feature or resource.
- Place business logic and reusable functions in the `services/` directory.
- Store configuration files in the `configs/` directory.
- Use the `public/` directory for static assets.
- Use the `views/` directory for server-rendered templates (Jade).
- Store email templates in the `emailTemplates/` directory.
- Use environment variables for sensitive data and configuration.

## API Endpoints

- All API responses must include a `success` boolean and a `message` string.
- Use appropriate HTTP status codes for all responses.
- Validate all incoming request data and sanitize inputs to prevent security vulnerabilities.
- Use try-catch blocks for error handling in all async route handlers and services.
- Log errors to the console for debugging, but avoid leaking sensitive information in production.

## Coding Style

- Use 2 spaces for indentation.
- Use single quotes for strings, except when using template literals.
- Place `import` or `require` statements at the top of each file.
- Group related code together and separate with clear comments.
- Avoid deeply nested callbacks; use async/await for asynchronous code.
- Prefer named exports over default exports for modules.

## Dependency Management

- Use only the dependencies listed in `package.json`.
- Keep dependencies up to date and remove unused packages.
- Use environment variables for configuration (see `.env`).

## Security

- Never commit sensitive information (API keys, passwords) to the repository.
- Validate and sanitize all user input.
- Use HTTPS in production environments.
- Handle authentication and authorization checks where required.

## Testing & Validation

- Write unit and integration tests for critical business logic.
- Validate all API endpoints with real-world data and edge cases.
- Ensure all new code passes linting and does not introduce errors.

## Documentation

- Document all public functions, classes, and modules.
- Update README and relevant documentation when adding new features or making significant changes.

## Performance

- Optimize database queries and avoid N+1 query problems.
- Use efficient algorithms and data structures.
- Minimize synchronous/blocking operations in the main event loop.

## Version Control

- Use clear, descriptive commit messages.
- Group related changes into a single commit.
- Do not commit generated files or dependencies (e.g., `node_modules/`).

